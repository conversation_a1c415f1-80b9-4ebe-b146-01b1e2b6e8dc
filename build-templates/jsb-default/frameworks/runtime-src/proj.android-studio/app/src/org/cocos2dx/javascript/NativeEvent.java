/****************************************************************************
Copyright (c) 2008-2010 Ricardo <PERSON>
Copyright (c) 2010-2016 cocos2d-x.org
Copyright (c) 2013-2016 Chukong Technologies Inc.
 
http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/
package org.cocos2dx.javascript;

import android.util.Log;

import org.cocos2dx.lib.Cocos2dxJavascriptJavaBridge;
import org.cocos2dx.lib.Cocos2dxWebViewHelper;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;

public class NativeEvent{
    public static final String TAG = "NativeEvent";

    public static void init(){

    }

    public static String call_native(String msg) {
        try {
            JSONObject jobj = new JSONObject(msg);

            final String param = jobj.getString("param");
            final int isSync = jobj.getInt("isSync");
            final String respMsgKey = jobj.getString("respMsgKey");
            final String object = jobj.getString("object");
            final String method = jobj.getString("method");

            if(isSync == 1){    //同步执行
                return runByGLThread(object,method,respMsgKey,param);
            } else {
                AppActivity.mActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        runByUIThread(object,method,respMsgKey,param);
                    }
                });
            }
        }catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void callback_to_script(final String respMsgKey, final String jsonStr){
        AppActivity.mActivity.getGLSurfaceView().queueEvent(new Runnable() {
            @Override
            public void run() {
                String retStr = "";
                try{
                    JSONObject jobj;
                    if(!jsonStr.equals("")){
                        jobj = new JSONObject(jsonStr);
                    } else {
                        jobj = new JSONObject();
                    }

                    jobj.put("respMsgKey", respMsgKey);
                    retStr = jobj.toString();
                }catch (JSONException e) {
                    e.printStackTrace();
                }

                try {
                    retStr = URLEncoder.encode(retStr, "UTF-8");
                    String jsCall = "OnNativeEventCallback(\"" + retStr + "\");";
                    Cocos2dxJavascriptJavaBridge.evalString(jsCall);
                } catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 在Ui线程执行
     * @param objName
     * @param methodName
     * @param resKey 回调时使用与传参的key
     * @param param
     */
    public static String runByUIThread(String objName, String methodName, String resKey, String param){
        return runReflectObject(objName,methodName,resKey,param);
    }

    /**
     * 在gl主线程执行
     * @param objName
     * @param methodName
     * @param param
     */
    public static String runByGLThread(String objName, String methodName, String resKey, String param){
        return runReflectObject(objName,methodName,resKey,param);
    }

    /**
     * 通过反射机制调用指定类的方法
     * @param objName
     * @param methodName
     * @param param
     */
    private static String runReflectObject(String objName, String methodName, String resKey, String param){
        try {
            Log.d(TAG, "objName = " + objName + "; methodName = " + methodName
                    + "; resKey = " + resKey + "; param = " + param);
            Class<?> mclass = Class.forName(objName);
            Field[] fields = mclass.getDeclaredFields();
            Object mObject = mclass.newInstance();

            Method mMethod = mclass.getMethod(methodName, String.class, String.class);
            Object ret = mMethod.invoke(mObject,resKey,param);
            return ret.toString();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        return "";
    }
    
    public static void setWebviewBGColor(String color){
        Cocos2dxWebViewHelper.setDefaultColor(color);
    }

    public static void resetWebviewBG(){
        Cocos2dxWebViewHelper.resetDefault();
    }

    /**
     * 屏幕方向切换代理方法
     * 避免反射创建AppActivity实例，直接调用静态方法
     */
    public static void changeOrientationProxy(int value) {
        AppActivity.changeOrientation(value);
    }
}
