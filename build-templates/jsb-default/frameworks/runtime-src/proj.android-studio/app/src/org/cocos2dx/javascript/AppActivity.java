/****************************************************************************
Copyright (c) 2015-2016 Chukong Technologies Inc.
Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/
package org.cocos2dx.javascript;

import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxGLSurfaceView;

import android.Manifest;
import android.animation.ValueAnimator;
import android.animation.Animator;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Service;
import android.content.ServiceConnection;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Rect;
import android.location.LocationManager;
import android.os.Bundle;
import org.cocos2dx.javascript.SDKWrapper;
import org.json.JSONException;
import org.json.JSONObject;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;


import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import android.content.ComponentName;
import android.os.Environment;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.Vibrator;
import android.os.Build;
import android.provider.MediaStore;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.widget.EditText;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.alibaba.fastjson.JSON;
import com.huangsongyao.custom.util.BridgeUtil;
import com.huangsongyao.custom.util.ClipBoardUtil;
import com.huangsongyao.custom.views.KeyboardDisplayView;
import com.huangsongyao.custom.views.StatusBarUtils;
import com.keyboard.monitor.SoftHideKeyBoardUtil;
import com.keyboard.monitor.OnKeyboardListener;
import android.view.Window;
import android.view.WindowManager;
import com.keyboard.monitor.KeyboardHeightProvider;

import com.jni.emulator.EmulatorDectectUtil;
import android.os.Handler;
import android.os.Looper;
import com.pknewname.pknew.BuildConfig;


public class AppActivity extends Cocos2dxActivity implements OnKeyboardListener {

    private static final int ID_KEYBOARD_DISPLAY_VIEW = 111;
    private static final int ID_KEYBOARD_DISPLAY_VIEW_LANDSCAPE = 112;

    private final static String TAG = " Creator AppActivity";
    public static AppActivity mActivity;
    private KeyboardHeightProvider keyboardHeightProvider;

    public static String WriteblePath = "";
    public static AudioRecorder2Mp3Util util = null;
    public static boolean canClean = false;
    public static int netGps_code = 20;
	public static RecaptchaUtils recaptchaUtils;
    private static int emulatorCheckVal = 0;

    public static BridgeUtil bridge;

    // TODO recording default stop time setting
    private static final int MAX_RECORDING_TIME_MS = 10000; // 10 seconds
    private static Handler autoStopHandler;
    private static Runnable autoStopRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mActivity = this;
        ImagePicker.getInstance().init(this);
        DeviceMgr.init();
        GeoComplySDK.getInstance().init(this);

        LocationMgr.locationManager = (LocationManager) mActivity.getSystemService(Context.LOCATION_SERVICE);
        LocationMgr.init();
        ToolsMgr.init();
        // Workaround in https://stackoverflow.com/questions/16283079/re-launch-of-activity-on-home-button-but-only-the-first-time/16447508
        if (!isTaskRoot()) {
            // Android launched another instance of the root activity into an existing task
            //  so just quietly finish and go away, dropping the user back into the activity
            //  at the top of the stack (ie: the last state of this task)
            // Don't need to finish it again since it's finished in super.onCreate .
            Log.d(TAG, "!isTaskRoot() ");
            return;
        }
        // DO OTHER INITIALIZATION BELOW
        SDKWrapper.getInstance().init(this);
        changeOrientation(0);
        getStoragePermissions();

        CrashHandler crashHandler = CrashHandler.getInstance();
        crashHandler.init(this);
        //video sdk
        AgoraSdk.setActivity(this);
		//google recaptcha
		recaptchaUtils = new RecaptchaUtils(this);


        //软键盘弹出相关
        initEditTextStyle();
        changeSoftToInputProtrait();
        registerKeyboardListener();

        bridge = new BridgeUtil(mActivity);
        ClipBoardUtil.init(mActivity);
    }

    public static int emulatorCheck() {
        if (emulatorCheckVal == 0) {
            emulatorCheckVal = EmulatorDectectUtil.isEmulator()? 1 : 2;
        }
        if (BuildConfig.LOGGING_ENABLED) {
            Log.d(TAG, "emulatorCheckVal " + emulatorCheckVal);
        }
        return emulatorCheckVal;
    }

    @Override
    public Cocos2dxGLSurfaceView onCreateView() {
        Cocos2dxGLSurfaceView glSurfaceView = new Cocos2dxGLSurfaceView(this);
        // TestCpp should create stencil buffer
        glSurfaceView.setEGLConfigChooser(5, 6, 5, 0, 16, 8);
        SDKWrapper.getInstance().setGLSurfaceView(glSurfaceView, this);
        return glSurfaceView;
    }

    //private static native void OnRecordEnd();


    @Override
    protected void onResume() {
        super.onResume();
        SDKWrapper.getInstance().onResume();
        LocationMgr.updateHaveGps();

    }

    @Override
    protected void onPause() {
        super.onPause();
        SDKWrapper.getInstance().onPause();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (autoStopHandler != null && autoStopRunnable != null) {
            autoStopHandler.removeCallbacks(autoStopRunnable);
        }

        // Workaround in https://stackoverflow.com/questions/16283079/re-launch-of-activity-on-home-button-but-only-the-first-time/16447508
        if (!isTaskRoot()) {
            return;
        }


        SDKWrapper.getInstance().onDestroy();

		recaptchaUtils.closeRecaptcha();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        ImagePicker.getInstance().onActivityResult(requestCode, resultCode, data);
        SDKWrapper.getInstance().onActivityResult(requestCode, resultCode, data);
        AutoUpdateManager.recheckInstallPermissions(requestCode);
//        if (requestCode == LocationMgr.OPENGPS) {
//            if (LocationMgr.isLocServiceEnable()) {
//                LocationMgr.init();
//            }
//        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        SDKWrapper.getInstance().onNewIntent(intent);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        SDKWrapper.getInstance().onRestart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        SDKWrapper.getInstance().onStop();
    }

    @Override
    public void onBackPressed() {
        SDKWrapper.getInstance().onBackPressed();
        super.onBackPressed();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        SDKWrapper.getInstance().onConfigurationChanged(newConfig);
        super.onConfigurationChanged(newConfig);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        SDKWrapper.getInstance().onRestoreInstanceState(savedInstanceState);
        super.onRestoreInstanceState(savedInstanceState);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        SDKWrapper.getInstance().onSaveInstanceState(outState);
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onStart() {
        SDKWrapper.getInstance().onStart();
        super.onStart();
    }

    public static void openUrl(String url) {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        Uri content_url = Uri.parse(url);
        intent.setData(content_url);
        mActivity.startActivity(intent);
    }



    public static void changeOrientation(int value){
        boolean isLandscape = (value==1);
        int orientation = isLandscape ? ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE: ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
        if(orientation == mActivity.getRequestedOrientation()){
            return;
        }

        mActivity.setRequestedOrientation(orientation);
    }

    public static void SetWriteblePath(String Path)
    {
        WriteblePath = Path;
    }

    public static boolean DoRecord(int value) {

        Log.d(TAG,"DoRecord");

        //判断是否已经获取相应录音权限
        if(android.os.Build.VERSION.SDK_INT >= 23) {  //API为23以上必须动态获取录音权限
            if (mActivity.getContext().checkSelfPermission(android.Manifest.permission.RECORD_AUDIO) ==  PackageManager.PERMISSION_GRANTED) {
            }// 若没有获得相应权限，则弹出对话框获取
            else {
                mActivity.requestPermissions(new String[]{android.Manifest.permission.RECORD_AUDIO}, 0);
            }
        }

        if (util == null) {
            util = new AudioRecorder2Mp3Util(mActivity,
                    WriteblePath + "lvRecord.raw",
                    WriteblePath + "lvRecord.mp3");
        }

        if (canClean) {
            util.cleanFile(AudioRecorder2Mp3Util.MP3
                    | AudioRecorder2Mp3Util.RAW);
        }
        try{
            util.startRecording();
            // TODO recording default stop time setting
            // 設置自動停止計時器
            autoStopHandler = new Handler(Looper.getMainLooper());
            autoStopRunnable = new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, "Auto-stopping recording after 10 seconds");
                    StopRecord(0);
                }
            };
            autoStopHandler.postDelayed(autoStopRunnable, MAX_RECORDING_TIME_MS);

        }catch (IllegalStateException e){
            Log.e(TAG, "startRecording: ", e);
        }
        if (!util.getRecordingState()){
            return false;
        }
        canClean = true;
        return true;
    }



    public static void StopRecord(int value) {

        Log.d("AudioRecorder2Mp3Util","AppActivity StopRecord, int value = "+ value);

        // 取消自動停止計時器
        if (autoStopHandler != null && autoStopRunnable != null) {
            Log.d("AudioRecorder2Mp3Util","AppActivity StopRecord...stop autoStop");
            autoStopHandler.removeCallbacks(autoStopRunnable);
        }

        if (util != null) {

            util.stopRecordingAndConvertFile();
            //util.cleanFile(AudioRecorder2Mp3Util.RAW);
            util.close();
            util = null;
            // OnRecordEnd();
            String retStr = "";
            String x = "on_voice_record_finish";
            NativeEvent.callback_to_script(x, retStr);
        }
    }

    public static String getFilePath(String filePath) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            filePath = Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_PICTURES) + File.separator + filePath + ".jpg";
        }
        else {
            filePath = Environment.getExternalStorageDirectory().getPath() + File.separator + "pkuser" + File.separator + filePath;
        }
        return filePath;
    }
    //读取文件中的字符串
    public static String readFile(String filePath) {
        getStoragePermissions();
        Log.v("readFile", "======================> " + filePath);
        filePath = getFilePath(filePath);
        File file = new File(filePath);
        StringBuilder stringBuilder = new StringBuilder();
        char [] buf = new char[64];
        int count=0;
        try {
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStreamReader reader = new InputStreamReader(fileInputStream, "UTF-8");
            while ((count = reader.read(buf)) != -1) {
                stringBuilder.append(buf,0,count);
            }
        } catch (Exception e) {
            Log.e("读取文件出错",e.getMessage());
        }
        Log.v("readFile", "======================>1");
        Log.v("readFile", stringBuilder.toString());
        return stringBuilder.toString();
    }

    //将内容写入文件
    public static void writeToFile(String filePath, String content){
        getStoragePermissions();
        Log.v("writeToFile", "======================>0");
        filePath = getFilePath(filePath);
        File file = getFile(filePath);
        Log.v("writeToFile", "======================>1");
        try {
            FileWriter fw = new FileWriter(file,false);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(content);
            bw.close();
            fw.close();
        } catch (Exception e) {
            Log.e("写文件出错",e.getMessage());
        }
    }

    //根据路径获取文件
    public static File getFile(String filePath) {
        File dir = new File(filePath);
        if (!dir.getParentFile().exists()) {
            dir.getParentFile().mkdirs();
        }
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                boolean flag = file.createNewFile();
                if (!flag) {
                    Log.e("创建文件失败","createNewFile 失败");
                }
            } catch (Exception e) {
                Log.e("创建文件失败",e.getMessage());
            }
        }
        return file;
    }

    static public void getStoragePermissions(){
//		String permissionsNeeded = android.Manifest.permission.WRITE_EXTERNAL_STORAGE;

        //判断是否已经获取相应权限
        if (android.os.Build.VERSION.SDK_INT >= 23) {
//				if (activity.getContext().checkSelfPermission(permissionsNeeded) != PackageManager.PERMISSION_GRANTED) {
//					// 若没有获得相应权限，则弹出对话框获取
//					if (activity.shouldShowRequestPermissionRationale(permissionsNeeded)) {
//
//					}
//					else {
//					activity.requestPermissions(new String[]{permissionsNeeded}, 3);
//					}
//				}
            String[] permissions = new String[]{
                    android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    android.Manifest.permission.READ_EXTERNAL_STORAGE,
                    android.Manifest.permission.READ_PHONE_STATE};
            //检查权限
            if (mActivity.getContext().checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED || mActivity.getContext().checkSelfPermission(android.Manifest.permission.READ_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED || mActivity.getContext().checkSelfPermission(android.Manifest.permission.READ_PHONE_STATE)
                    != PackageManager.PERMISSION_GRANTED ) {
                // 之前拒绝了权限，但没有点击 不再询问 这个时候让它继续请求权限
                mActivity.shouldShowRequestPermissionRationale(android.Manifest.permission.WRITE_EXTERNAL_STORAGE);
                mActivity.shouldShowRequestPermissionRationale(android.Manifest.permission.READ_EXTERNAL_STORAGE);
                mActivity.shouldShowRequestPermissionRationale(android.Manifest.permission.READ_PHONE_STATE);

                mActivity.requestPermissions(permissions,  4);

            }
            else {
//                LocationMgr.init();
            }
        }
        else {
//            LocationMgr.init();
        }

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        AutoUpdateManager.recheckReadWriteFilePermissions(requestCode);
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean readGranted = false, writeGranted = false;
        boolean fineGranted = false, coarseGranted = false, stateGranted = false;
        for (int i = 0, j = permissions.length; i < j; i++) {
            if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                if (android.Manifest.permission.WRITE_EXTERNAL_STORAGE.equals(permissions[i])) {
                    writeGranted = true;
                } else if (android.Manifest.permission.READ_EXTERNAL_STORAGE.equals(permissions[i])) {
                    readGranted = true;
                }
                else if (android.Manifest.permission.ACCESS_FINE_LOCATION.equals(permissions[i])) {
                    fineGranted = true;
                } else if (android.Manifest.permission.ACCESS_COARSE_LOCATION.equals(permissions[i])) {
                    coarseGranted = true;
                }
                else if (android.Manifest.permission.READ_PHONE_STATE.equals(permissions[i])) {
                    stateGranted = true;
                }
            }
            if (requestCode == PermissionMgr.FORCE_PERMISSION_REQUEST_CODE) {
                String response  = "";
                try {
                    JSONObject obj = new JSONObject();
                    obj.put("accepted", (grantResults[i] == PackageManager.PERMISSION_GRANTED));
                    response = obj.toString();
                }
                catch (JSONException e) {
                    Log.e(TAG, "JSONException: ", e);
                }
                NativeEvent.callback_to_script(PermissionMgr.FORCE_PERMISSION_RESPONSE_EVENT, response);
            }
        }

        //权限回调
        if (requestCode == 4) {
            if (writeGranted && readGranted) {
//                    readFile("pkuserinfo");
//                    readFile("pkpassinfo");

            } else {
                Log.e("申请权限1","读或写文件权限被拒绝");
            }

            if (fineGranted || coarseGranted) {
                Log.e("onRequmissionsResult","------------->LocationMgr.init");
//                LocationMgr.init();

            } else {
                Log.e("申请权限2","位置权限被拒绝");
            }
        }
        else if(requestCode == netGps_code) {
            if (fineGranted || coarseGranted) {
                LocationMgr.init();
            }
            else {
                LocationMgr.callScript(false);
            }
        }
    }

    public static String getVersion()throws IOException{
        return Build.VERSION.RELEASE;
    }

    public static void getVibrate() {
        Vibrator vb = (Vibrator) mActivity.getSystemService(Service.VIBRATOR_SERVICE);
        if (vb.hasVibrator()) {
            vb.vibrate(300);
        }
    }

    public static void initRecaptcha(String key) {
        AppActivity.recaptchaUtils.init(key);
    }

    public static void getRecaptchaToken() {
        AppActivity.recaptchaUtils.getRecatchaToken();
    }

    //---------------------------------软键盘相关----------------------------------------
    private void initEditTextStyle() {
        try { //1|2等ID来自Cocos2dxEditBox
            @SuppressLint("ResourceType") EditText et = findViewById(1);
            @SuppressLint("ResourceType") View btn = findViewById(2);
            ViewGroup vg = (ViewGroup) et.getParent();

            //输入框样式和默认底部位置
            et.setBackgroundColor(Color.parseColor("#99000000"));
            et.setTextColor(Color.WHITE);
            int screenHeight = ((WindowManager) getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getHeight();
            if(!EmulatorDectectUtil.isEmulator())
            {
                ViewGroup.MarginLayoutParams etLP = (ViewGroup.MarginLayoutParams) et.getLayoutParams();
                etLP.topMargin = screenHeight;
                et.setLayoutParams(etLP);
            }

            //按钮不可见
            ViewGroup.LayoutParams btnLP = btn.getLayoutParams();
            btnLP.width = 0;
            btn.setLayoutParams(btnLP);

            //利用cocos自带的键盘弹出时输入框位置调节
            vg.setFitsSystemWindows(false);
            Field topMarginField = et.getClass().getDeclaredField("mTopMargin");
            topMarginField.setAccessible(true);
            et.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
                Rect r = new Rect();
                et.getWindowVisibleDisplayFrame(r);
                int heightDiff = et.getRootView().getHeight() - (r.bottom - r.top);
                if (!(heightDiff > screenHeight / 4)) {
                    try {
                        topMarginField.setInt(et, 0);
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting top margin: ", e);
                    }
                }
            });
            et.getViewTreeObserver().addOnGlobalFocusChangeListener((oldFocus, newFocus) -> StatusBarUtils.hideVirtualButton());

            vg.requestLayout();
        } catch (Exception ex) {
            Log.e(getClass().getSimpleName(), "changeEditTextStyle异常");
        }
    }

    @Override
    public void onKeyBoardEvent(boolean isShow,int height, int orientation){
        Log.d("***onKeyBoardEvent*", "isShow：" + isShow +",height：" + height +",orientation：" + orientation );
        //竖屏不用处理
        if(orientation == Configuration.ORIENTATION_LANDSCAPE){//横屏
            if (isShow) {
                Log.d("***keyboardDisplayView*", "横屏，打开键盘");
            } else {
                Log.d("**keyboardDisplayView2*", "横屏，关闭键盘");
            }
            mActivity.changeEditTextStyleToLandscapeByKeyboardHeight(height);
        }else{
            if(isShow){
                Log.d("***keyboardDisplayView*", "竖屏，打开键盘" );
            } else{
                Log.d("***keyboardDisplayView*", "竖屏，关闭键盘" );
            }
            mActivity.changeEditTextStyleProtrait(height);
        }
    }

    public void changeEditTextStyleToLandscapeByKeyboardHeight(int keyboardHeight) {
//        try { //1|2等ID来自Cocos2dxEditBox
//            EditText et = findViewById(1);
//            View btn = findViewById(2);
//            ViewGroup vg = (ViewGroup) et.getParent();
//
//            ViewGroup.LayoutParams lp = btn.getLayoutParams();
//            lp.width = 0;
//            btn.setLayoutParams(lp);
//
//            //左边偏移刘海的高度
//            int notchHeight = 0;
//
//            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//            rlp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
//            rlp.leftMargin = notchHeight;
//            rlp.rightMargin = SoftHideKeyBoardUtil.getVirtualBarHeigh(mActivity);
//            rlp.bottomMargin = 0;
//            Log.d("**********","**" + ",rlp.leftMargin :" + rlp.leftMargin  + ",rlp.rightMargin :" + rlp.rightMargin + ",rlp.bottomMargin :" + rlp.bottomMargin );
//            Log.d("****", "keyboardDisplayView2  不存在，新建，设置y值为："+ keyboardHeight );
//            KeyboardDisplayView keyboardDisplayView = new KeyboardDisplayView(this,ID_KEYBOARD_DISPLAY_VIEW_LANDSCAPE,true,0);
//            keyboardDisplayView.setId(ID_KEYBOARD_DISPLAY_VIEW_LANDSCAPE);
//            mFrameLayout.addView(keyboardDisplayView, rlp);
//            putEditTextUp(keyboardDisplayView,keyboardHeight);
//
//            rlp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
//            rlp.leftMargin = notchHeight;
//            rlp.rightMargin = SoftHideKeyBoardUtil.getVirtualBarHeigh(mActivity);
//            rlp.addRule(RelativeLayout.ABOVE, ID_KEYBOARD_DISPLAY_VIEW_LANDSCAPE);
//            vg.setLayoutParams(rlp);
//
//        } catch (Exception ex) {
//            Log.e(getClass().getSimpleName(), "changeEditTextStyleToLandscapeByKeyboardHeight");
//            ex.printStackTrace();
//        }
    }

    private void changeEditTextStyleProtrait(int keyboardHeight) {
//        try { //1|2等ID来自Cocos2dxEditBox
//            EditText et = findViewById(1);
//            View btn = findViewById(2);
//            ViewGroup vg = (ViewGroup) et.getParent();
//
//            ViewGroup.LayoutParams lp = btn.getLayoutParams();
//            lp.width = 0;
//            btn.setLayoutParams(lp);
//
//            RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
//            rlp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
//            Log.d("*changeEditTextStyle**","**" + ",rlp.leftMargin :" + rlp.leftMargin  + ",rlp.rightMargin :" + rlp.rightMargin + ",rlp.bottomMargin :" + rlp.bottomMargin );
//            KeyboardDisplayView keyboardDisplayView = new KeyboardDisplayView(this);
//            keyboardDisplayView.setId(ID_KEYBOARD_DISPLAY_VIEW);
//            mFrameLayout.addView(keyboardDisplayView, rlp);
//
//            putEditTextUp(keyboardDisplayView,keyboardHeight);
//
//            rlp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
//            rlp.addRule(RelativeLayout.ABOVE, ID_KEYBOARD_DISPLAY_VIEW);
//            vg.setLayoutParams(rlp);
//            Log.d("****", "kankan changeEditTextStyleProtrait");
//        } catch (Exception ex) {
//            Log.e(getClass().getSimpleName(), "changeEditTextStyle异常");
//            ex.printStackTrace();
//        }
    }

    private static boolean isEditUp = false;
    private static boolean isEditDown = true;
    private void putEditTextUp(View view,int realKeyboardHeight){
        ValueAnimator anim = ValueAnimator.ofInt(0, realKeyboardHeight);
        anim.setDuration(100);
        anim.setRepeatCount(0);
        anim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                isEditDown = false;
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                try{
                    view.setVisibility(View.VISIBLE);
                    int currentValue = (Integer) animation.getAnimatedValue();
                    // 获得改变后的值
                    Log.d("*currentValue*" ,"putEditTextUp "+ currentValue);
                    // 输出改变后的值
                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) view.getLayoutParams();
                    layoutParams.setMargins(layoutParams.leftMargin, 0, layoutParams.rightMargin, currentValue);
                    view.setLayoutParams(layoutParams);
                    view.requestLayout();
//
//        if(currentValue == realKeyboardHeight){
//          view.setVisibility(View.VISIBLE);
//        }
                } catch (Exception ex) {
                    Log.e("", "putEditTextUp onAnimationUpdate异常");
                }
            }
        });
        anim.start();
    }

    private void putEditDown(View view,int distance_to_move){
        ValueAnimator anim = ValueAnimator.ofInt(distance_to_move, 0);
        anim.setDuration(100);
        anim.setRepeatCount(0);
        anim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                isEditUp = false;
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                try{
                    int currentValue = (Integer) animation.getAnimatedValue();
                    // 获得改变后的值
                    Log.d("*currentValue*" ,"putEditDown "+ currentValue);
                    // 输出改变后的值
                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) view.getLayoutParams();
//        layoutParams.setMargins(0, 0, 0, currentValue);
                    layoutParams.setMargins(layoutParams.leftMargin, 0, layoutParams.rightMargin, currentValue);
                    view.setLayoutParams(layoutParams);
                    view.requestLayout();
                } catch (Exception ex) {
                    Log.e("", "putEditDown onAnimationUpdate异常");
                }
            }
        });
        anim.start();

    }

    /***
     * 软键盘竖向
     *
     */
    public static void changeSoftToInputProtrait() {
        mActivity.runOnUiThread(() -> {
            Window window = mActivity.getWindow();
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            Log.d("***changeSoftInput*", "changeSoftToInputProtrait");
        });
    }

    private void registerKeyboardListener() {
        keyboardHeightProvider = new KeyboardHeightProvider(this);
        mActivity.mFrameLayout.post(new Runnable() {
            public void run() {
                keyboardHeightProvider.start();
            }
        });
        keyboardHeightProvider.setKeyboardHeightObserver(this);
    }

    public ViewGroup getContentView() {
        return mActivity.mFrameLayout;
    }
    // ------------------------------------------------------------

    public static String jsInvokeMethod(String parameters) {
        Log.d("----------", "---------jsInvokeMethod=" + parameters);
        Map<String, Object> map = JSON.parseObject(parameters, Map.class);
        Object object;
        String methodName = map.get("invokeName") == null ? "" : map.get("invokeName").toString();

        if ("loadingJsInit".equals(methodName)) { //loadingJS初始化了隐藏启动图
            mActivity.runOnUiThread(() -> {
                mActivity.getGLSurfaceView().setBackgroundResource(android.R.color.transparent);
            });
            return "{result:0}";
        }

        String methodParameters = map.get("parameters") == null ? "" : map.get("parameters").toString();
        String callback_id = map.get("callback_id") == null ? "" : map.get("callback_id").toString();
        Log.d("----------------", "---------------------jsInvokeMethod  callback_id=" + callback_id);
        if (!TextUtils.isEmpty(methodParameters)) {
            StringBuffer stringBuffer = new StringBuffer(methodParameters);
            methodParameters = stringBuffer.insert(1, "\"callback_id\":\"" + callback_id + "\",").toString();
        }
        if (!TextUtils.isEmpty(methodName)) {
            Method method = bridge.getMethods().get(methodName);
            if (method != null) {
                try {
                    method.setAccessible(true);
                    if (!TextUtils.isEmpty(methodParameters)) {
                        object = method.invoke(bridge, (Object) methodParameters);
                    } else {
                        object = method.invoke(bridge, (Object) callback_id);
                    }
                    return object.toString();
                } catch (IllegalAccessException e) {
                    Log.e(TAG, "Error invoking method: ", e);
                } catch (InvocationTargetException e) {
                    Log.e(TAG, "Error invoking method: ", e);
                }
            }
        } else {
            return "{result:-1}";
        }
        return "{result:0}";
    }
}
