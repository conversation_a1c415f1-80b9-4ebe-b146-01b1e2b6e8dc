<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"

    xmlns:tools="http://schemas.android.com/tools"
    android:installLocation="auto">

    <meta-data android:allowBackup="false" android:name="com.google.android.gms.vision.DEPENDENCIES" android:value="barcode" />

    <uses-feature android:glEsVersion="0x00020000" />

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30"/>
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BATTERY_STATS"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="getui.permission.GetuiService.com.ylc.qp.Pokermate" />
    <!-- agora-rtc-sdk -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <!--<uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />-->

    <!-- GeoComply permissions starts-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_UPDATES"/>
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission"/>
    <uses-permission
        android:name="com.google.android.providers.gsf.permission.READ_GSERVICES"/>
    <!-- GeoComply permissions ends-->

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <permission android:name="getui.permission.GetuiService.com.ylc.qp.Pokermate"
        android:protectionLevel="normal" >
    </permission>
    <application
        android:allowBackup="false"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:requestLegacyExternalStorage="true"
		android:usesCleartextTraffic="true"
        android:extractNativeLibs="true">

        <!-- Tell Cocos2dxActivity the name of our .so -->
        <meta-data android:name="android.app.lib_name"
                   android:value="cocos2djs" />
        
        <activity
            android:name="org.cocos2dx.javascript.AppActivity"
            android:screenOrientation="unspecified"
            android:hardwareAccelerated="true"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:label="@string/app_name"
            android:requestLegacyExternalStorage="true"
            android:usesCleartextTraffic="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
		<provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.pknewname.pknew.fileProvider"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path"/>
        </provider>

        <activity
            android:name="com.huangsongyao.custom.webview.FromJsWebview"
            android:configChanges="orientation|keyboardHidden|screenSize|navigation"
            android:theme="@style/AppThemeNoAnimation" />

        <!--GeoComply starts-->
        <receiver android:name="com.geocomply.client.GeoComplyClientBootBroadcastReceiver"
            android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.geocomply.client.GeoComplyClientBroadcastReceiver"/>

        <service
            android:name="com.geocomply.location.WarmingUpLocationProvidersService"
            android:exported="false" />
        <!--GeoComply ends-->

    </application>

</manifest>
