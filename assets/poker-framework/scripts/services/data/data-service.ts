/* eslint-disable camelcase */
/* eslint-disable max-params */
import { Service } from '../../core/core-index';
import { PokerHandData, RevealHandData } from './hand-data';
import { OpponentPublicData } from './data-opponent-public';
import { SelfPublicData } from './data-self-public';
import { ValueObject } from '../../core/core-index';
import type {
    IRequestGameHand,
    IRequestGetPublicData,
    IRequestRevealHand,
    IRequestSelfStatisticalData
} from '../../poker-client/session/data-session-types';
import type { ISocket } from '../../poker-client/poker-socket';
import type { IDataSession } from '../../poker-client/session/data-session';

export class DataService extends Service {
    static readonly serviceName = 'DataService';
    _userId: number;
    _userToken: string;
    _socket: ISocket;
    _session: IDataSession = null;

    constructor(socket: ISocket, userId: number, userToken: string) {
        super(DataService.serviceName);
        this._userId = userId;
        this._userToken = userToken;
        this._socket = socket;
        this._session = this._socket.createDataSession();
    }

    async getSelfPublicData(
        umode: number,
        uGameid: number,
        blind: number,
        ante: number,
        identity: number,
        currencyType: number
    ): Promise<SelfPublicData> {
        const obj: IRequestSelfStatisticalData = {
            uid: this._userId,
            token: this._userToken,
            mode: umode,
            gameid: uGameid,
            blind,
            ante,
            identity,
            currencyType: currencyType
        };
        const response = await this._session.getSelfPublicData(obj);
        const result = ValueObject.fromProto(SelfPublicData, response);
        return result;
    }

    async getOpponentPublicData(
        uid: number,
        umode: number,
        uGameid: number,
        blind: number,
        ante: number,
        identity: number,
        requestuid: number,
        currencyType: number
    ): Promise<OpponentPublicData> {
        const obj: IRequestGetPublicData = {
            uid: uid,
            mode: umode,
            gameid: uGameid,
            blind,
            ante,
            identity,
            req_uid: requestuid,
            currencyType: currencyType
        };
        const response = await this._session.getOpponentPublicData(obj);
        const result = ValueObject.fromProto(OpponentPublicData, response);
        return result;
    }

    async getGameHand(uuidJs: string, gameId: number): Promise<PokerHandData> {
        const obj: IRequestGameHand = {
            uid: this._userId,
            token: this._userToken,
            gameid: gameId,

            game_uuid_js: uuidJs
        };
        const response = await this._session.getGameHand(obj);
        const hand: PokerHandData = ValueObject.fromProto(PokerHandData, response);
        return hand;
    }

    async getGameFavoriteHand(uid: number, game_uuid_js: string): Promise<any> {
        const token: string = this._userToken;
        const obj = {
            uid,
            game_uuid_js,
            token
        };
        const response = await this._session.getGameFavoriteHand(obj);
        return response;
    }

    async revealHand(uuidJs: string, gameId: number): Promise<RevealHandData> {
        const obj: IRequestRevealHand = {
            uid: this._userId,
            game_id: gameId,
            game_uuid: uuidJs,
            is_replay: true
        };
        const response = await this._session.revealHand(obj);
        const hand: RevealHandData = ValueObject.fromProto(RevealHandData, response);
        return hand;
    }

    async doFavorite(uuidJs: string, gameId: number): Promise<any> {
        const obj = {
            uid: this._userId,
            gameid: gameId,
            game_uuid_js: uuidJs
        };
        const response = await this._session.requestDoFavorite(obj);
        return response;
    }

    async getBigPotGameUUIDs(roomUuidJs: string, gameId: number): Promise<any> {
        const obj = {
            uid: this._userId,
            token: this._userToken,
            room_uuid_js: roomUuidJs,
            game_id: gameId
        };
        const response = await this._session.requestBigPotGameUUIDs(obj);
        return response;
    }

    async setFeatureHandSubmit(gameUuid: number, gameUuidJs: string, gameId: number, roomUuid: number): Promise<any> {
        const obj = {
            user_id: this._userId,
            game_id: gameId,
            game_uuid: gameUuid,
            game_uuid_js: gameUuidJs,
            room_uuid: roomUuid,
            token: this._userToken
        };
        const response = await this._session.requestFeatureHandSubmit(obj);
        return response;
    }
}
