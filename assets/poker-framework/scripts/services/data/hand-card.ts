import type { IValueObject } from '../../core/core-index';
import { CardNum, CardSuit } from '../../poker-client/poker-data-types';
import type { IHandCardType } from '../../poker-client/session/data-session-types';

export class HandCardType implements IValueObject {
    number: CardNum = CardNum.CARD_2;
    suit: CardSuit = CardSuit.CARD_DIAMOND;

    fromProto(data: IHandCardType) {
        this.number = data.number || 0;
        this.suit = data.suit || 0;
    }
}
