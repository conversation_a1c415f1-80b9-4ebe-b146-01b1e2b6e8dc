/* eslint-disable camelcase, complexity, @typescript-eslint/prefer-for-of */
import type { IValueObject } from '../../core/core-index';
import { DataUtil, TypeUtil, ValueObject, ValueObjectArray } from '../../core/core-index';
import type {
    IGameRecord,
    ISquidPlayerInfo,
    IPlayerRecord,
    IPokerHandData,
    IRevealHandData,
    IRevealSeat
} from '../../poker-client/session/data-session-types';
import { HandCardType } from './hand-card';
import { ReplayData, ReplayInsuranceData } from './replay-data';

export class PokerHandData implements IValueObject {
    errCode: number | null = null;
    gameRecord: GameRecord | null = null;
    nClubID: number | null = 0; // 俱乐部id
    nRoomID: number | null = 0; // 房间id
    sGameUUID: string | null = ''; // 游戏uuid
    sRoomUUID: string | null = ''; // 房间uuid
    nCreateTime: number | null = 0; // 创建时间
    nMaxPot: number | null = 0; // 最大注
    nInsuranceWinbet: number | null = 0; // 保险
    nJackpotWinbet: number | null = 0; // jackpot
    nGameMode: number | null = 0; // 游戏模式
    nShortFull: number | null = 0; // 0: 花 > 葫芦, 1:葫芦 > 花
    bMirco: boolean | null = false; // 是否是微牌局
    nGameid: number | null = 0; // 当前游戏ID
    bAssociatedJackpot: boolean | null = true; // 是否有关联的JP
    objReplay: ReplayData | null = null; // 牌局回放数据串对象
    objReplayInsurance: ReplayInsuranceData[] | null = []; // 保险回放数据串对象
    bForceShowcard: boolean | null = false; // 该手牌局是否开启"强制亮牌"功能
    bStarClosed: boolean | null = true; // 明星桌是否关闭(默认:true, 关闭后即为普通桌)
    vShowCardByStanderUID: number[] | null = []; // 旁观者发齐强制亮牌的uid数组
    nForceShowCoin: number | null = 0; // 强制亮牌价格(只针对收藏牌局, 牌局中牌谱的价格与牌局保持一致)
    nSendOutCoin: number | null = 0; // 下一次发发看价格(只针对收藏牌局, 牌局中牌谱的价格与牌局保持一致)
    featureHandFee: number | null = 0; // Feature Hand Submit Fee
    bSpectatorEnabled: boolean | null = false;

    nTotalPot: number = 0; // 底池
    vPlayerRecords: PlayerRecord[] = []; // 玩家记录
    vPublicCards: HandCardType[] = []; // 公共牌堆
    vUnsendPublicCards: HandCardType[] = []; // 未发出的公共牌堆
    nJackpotTotalWinbet: number = 0; // jackpot falling mars

    fromProto(data: IPokerHandData) {
        this.errCode = data?.err_code;
        this.sGameUUID = data?.game_uuid_js ?? '';

        // 有真实数据
        if (this.errCode === 0) {
            this.gameRecord = ValueObject.fromProto(GameRecord, data.game_record);
            // 已知共牌
            this.vPublicCards = this.gameRecord.publicCards || [];

            // 未发出的共牌
            this.vUnsendPublicCards = this.gameRecord.unSendPublicCards || [];

            // 旁观者发齐强制亮牌的uid数组
            this.vShowCardByStanderUID = data.show_card_bystander_uid || [];

            // 临时解析 d sb bb 位(这里"replay"字段在下发数据中有可能是空, 做个容错处理)
            const playerSeatInfo: Map<number, any> = new Map();
            if (data.replay !== null && typeof data.replay !== 'undefined') {
                const tableInfo = data.replay.TableInfo;
                const seatsInfo = data.replay.SeatsInfo.seats_info;
                const roundsInfo = data.replay.RoundsInfo;
                for (let i = 0; i < DataUtil.getArrayLength(seatsInfo); ++i) {
                    const psi = {
                        seatNo: seatsInfo[i].seat_no,
                        seatInfo: 0,
                        jackpotType: 0,
                        uid: seatsInfo[i].UID
                    };
                    const settlementRound = roundsInfo.settlement_round.filter(
                        (round: any) => round.win_seat_no === seatsInfo[i].seat_no
                    );
                    psi.jackpotType = settlementRound[0].jackpot_type;

                    if (psi.seatNo === tableInfo.dealer_seat) {
                        psi.seatInfo |= 1; // D 001
                    }
                    if (psi.seatNo === tableInfo.sb_seat) {
                        psi.seatInfo |= 2; // SB 010
                    }
                    if (psi.seatNo === tableInfo.bb_seat) {
                        psi.seatInfo |= 4; // BB 100
                    }
                    playerSeatInfo.set(psi.uid, psi);
                }
            }

            this.gameRecord.records.forEach((e) => {
                e.updateSeatInfo(playerSeatInfo);
            });
            // 填充玩家数据信息
            this.vPlayerRecords = this.gameRecord.records;
        } else {
            console.error(`HttpHandler - GAME_HAND_RESP error: code = ${this.errCode}`);
        }

        this.nClubID = data?.clubid ?? 0;
        this.nRoomID = data?.roomid ?? 0;

        this.sRoomUUID = data?.room_uuid_js ?? '';
        this.nCreateTime = data?.start_time ?? 0;
        this.nMaxPot = data?.max_port ?? 0;
        this.nInsuranceWinbet = data?.insurace_winbet ?? 0;
        this.nJackpotWinbet = data?.jackpot_winbet ?? 0;
        this.nGameMode = data?.game_mode ?? 0;
        this.nShortFull = data?.short_full ?? 0;
        this.bMirco = data?.ismirco ?? false;
        this.nGameid = data?.gameid ?? 0;
        this.bAssociatedJackpot = data?.is_associated_jackpot ?? false;
        this.objReplay = ValueObject.fromProto(ReplayData, data.replay);
        ValueObjectArray.cloneFromProto(ReplayInsuranceData, this.objReplayInsurance, data.replayinsurance);
        this.bForceShowcard = data?.force_showcard ?? false;
        this.bStarClosed = true;
        this.nForceShowCoin = data?.force_show_coin ?? 0;
        this.nSendOutCoin = data?.next_show_left_coin ?? 0;
        this.featureHandFee = data?.fee ?? 0;
        this.bSpectatorEnabled = data?.spectator_enabled ?? false;
        this.vShowCardByStanderUID = data?.show_card_bystander_uid ?? [];
        this.nTotalPot = TypeUtil.toSafeNumber(this.gameRecord.totalPot);
        this.nJackpotTotalWinbet = TypeUtil.toSafeNumber(data.replay.RoundsInfo.jp_total_winbet);
    }
}

export class RevealHandData implements IValueObject {
    errCode: number;
    uid: number;
    gameUuid: string;
    revealSeats: RevealSeat[] = [];

    fromProto(data?: IRevealHandData): void {
        this.errCode = data.err_code;
        ValueObjectArray.cloneFromProto(RevealSeat, this.revealSeats, data.ShowSeats);
        this.uid = data?.uid ?? 0;
        this.gameUuid = data?.game_uuid ?? '';
    }
}

class RevealSeat implements IValueObject {
    uid: number;
    cards: HandCardType[] = [];

    fromProto(data?: IRevealSeat): void {
        ValueObjectArray.cloneFromProto(HandCardType, this.cards, data.cards);
        this.uid = data?.uid ?? 0;
    }
}

class GameRecord implements IValueObject {
    publicCards: HandCardType[] | null = [];
    unSendPublicCards: HandCardType[] | null = [];
    records: PlayerRecord[] | null = [];
    // handRecords: IHandRecord[] | null = [];
    totalPot: number | null = 0;
    insuranceWinbet: number | null = 0;
    insuranceBet: number | null = 0;
    insuranceAmount: number | null = 0;

    fromProto(data?: IGameRecord): void {
        ValueObjectArray.cloneFromProto(HandCardType, this.publicCards, data.public_cards);
        ValueObjectArray.cloneFromProto(HandCardType, this.unSendPublicCards, data.unsend_public_cards);
        ValueObjectArray.cloneFromProto(PlayerRecord, this.records, data.records);
        this.totalPot = data?.total_pot ?? 0;
        this.insuranceWinbet = data?.insurance_winbet ?? 0;
        this.insuranceBet = data?.insurance_bet ?? 0;
        this.insuranceAmount = data?.insurance_amount ?? 0;
    }
}

export class PlayerRecord implements IValueObject {
    lastRoundType: number | null = 0; // 玩家坚持到哪一阶段(cv.Enum.BettingRoundType)
    cards: HandCardType[] | null = []; // 手牌数组
    playerBettingRoundBet: number | null = 0; // 本局下注的所有筹码总数
    playerHead: string | null = '';
    playerName: string | null = '';
    playerid: number | null = 0;
    reviewSendOutLen: number | null = 0; // 发发看的长度(即该玩家额外能看到的长度)
    reviewSendOutActLen: number = 0; // 牌局回顾"发发看"动画长度
    forceShowedActLen: number = 0; // 被强制亮牌的长度(需要显示翻牌动画)
    winBet: number | null = 0; // 输赢的筹码数
    insuranceBet: number | null = 0; // 投保额
    insuranceAmount: number | null = 0; // 赔付额
    jackWinbet: number | null = 0; // 一手牌赢的jackpot筹码数
    bMuck: boolean | null = false; // 是否自动埋牌
    bActiveShow: boolean | null = false; // 主动show
    bForceShowDown: boolean | null = false; // 是否强制show
    isShowDown: boolean | null = false;
    plat: number | null = 0; // 平台
    bFold: boolean | null = false; // 是否弃牌
    nJackWinbet: number | null = 0; // 一手牌赢的jackpot筹码数
    seatNo: number | null = -1; //
    seatInfo: number | null = 0; // 000=default, 001=D, 010=SB, 100=BB
    jackpotType: number | null = 0;

    boughtInsurance: boolean | null = false;
    drawin: number | null = 0;
    award2ClubFund: number;
    shotForce: boolean | null = false;
    curDrawin: number | null = 0;
    forceShowCnt: number | null = 0;

    squidCount: number = 0;
    squidWinLoseAmount: number = 0;
    superSquidCount: number = 0;
    seatStatus: number = 0; // 0 Playing, 1 isWaiting, 2 isAway
    currentHandWinnerNoOfNormalSquids: number = 0;
    squidValue: number = 0;
    squidMultiplier: number = 0;

    fromProto(data?: IPlayerRecord): void {
        this.lastRoundType = data?.LastRoundType ?? 0;
        ValueObjectArray.cloneFromProto(HandCardType, this.cards, data.cards);
        this.playerBettingRoundBet = data?.player_betting_round_bet ?? 0;
        this.playerHead = data?.player_head ?? '';
        this.playerName = data?.player_name ?? '';
        this.playerid = data?.playerid ?? 0;
        this.winBet = data?.win_bet ?? 0;
        this.reviewSendOutLen = data?.send_card_len ?? 0;
        // this.forceShowedActLen = data?.send_card_len ?? 0;
        // this.reviewSendOutActLen = data?.send_card_len ?? 0;
        this.insuranceBet = data?.insurance_bet_amount ?? 0;
        this.insuranceAmount = data?.insurance_winbet ?? 0;
        this.bFold = data?.is_fold ?? false;
        this.bMuck = data?.is_muck ?? false;
        this.bActiveShow = data?.is_active_show ?? false;
        this.bForceShowDown = data?.is_force_show ?? false;
        this.isShowDown = data?.IsShowDown ?? false;
        this.plat = data?.plat ?? 0;
        this.jackWinbet = data?.jack_winbet ?? 0;
        this.seatStatus = data?.seatStatus ?? 0;
        this.boughtInsurance = data?.bought_insurance ?? false;
        this.drawin = data?.drawin ?? 0;
        this.award2ClubFund = data?.drawin ?? 0;
        this.shotForce = data?.shot_force ?? false;
        this.curDrawin = data?.cur_drawin ?? 0;
        this.forceShowCnt = data?.force_show_cnt ?? 0;

        // squid game data added
        if (data?.squidHuntGameSettlement?.allRoundTotalNoOfSquidsWon) {
            this.squidCount = data.squidHuntGameSettlement.allRoundTotalNoOfSquidsWon[this.playerid];
            if (this.playerid === data.squidHuntGameSettlement.currentHandWinner) {
                // tRecord.squidCount=value.squidHuntGameSettlement.currentHandWinnerNoOfTotalSquids;
                this.superSquidCount = 0;
                if (data.squidHuntGameSettlement.currentHandWinnerNoOfSuperSquids > 0)
                    this.superSquidCount = data.squidHuntGameSettlement.currentHandWinnerNoOfSuperSquids;
                if (data.squidHuntGameSettlement.currentHandWinnerNoOfNormalSquids > 0) {
                    this.currentHandWinnerNoOfNormalSquids =
                        data.squidHuntGameSettlement.currentHandWinnerNoOfNormalSquids;
                }
            }
            if (this.squidCount > 0)
                this.squidMultiplier = data.squidHuntGameSettlement.squidMultiplier[this.squidCount].multiplier;
            // this data only show on final Settlement round (when game is end)
            if (data.squidHuntGameSettlement?.finalSettlementPlayers) {
                data.squidHuntGameSettlement.finalSettlementPlayers?.forEach((playerInfo: ISquidPlayerInfo) => {
                    const { uid, winAmount } = playerInfo;
                    if (uid === this.playerid) {
                        this.squidWinLoseAmount = winAmount;
                    }
                });
                if (data.squidHuntGameSettlement.squid_value)
                    this.squidValue = data.squidHuntGameSettlement.squid_value[1];
            }
        }
    }

    updateSeatInfo(playerSeatInfo: Map<number, any>): void {
        if (playerSeatInfo.size > 0) {
            const psi: any = playerSeatInfo.get(this.playerid);
            if (psi) {
                this.seatNo = psi.seatNo;
                this.seatInfo = psi.seatInfo;
                this.jackpotType = psi.jackpotType;
            }
        }
    }
}
