[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "b4112a50-a6a7-4fa1-a66d-32729c0d00c3"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 5}, {"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_fitWidth": true, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}, {"__type__": "cc.Node", "_name": "PersistRoot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 548}, {"__id__": 549}], "_prefab": {"__id__": 550}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "26xBUW3+ZIXLAeDIi9/MQm"}, {"__type__": "cc.Node", "_name": "Update", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "efM7/uxNNHNrxVhFZlH+px"}, {"__type__": "6c9c7tXZ1tHSLspEOnPy0BU", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "useInlineBundle": true, "h5BundleManifest": {"__uuid__": "f6428827-2411-4183-aaf2-c2eb41b98a72"}, "androidBundleManifest": {"__uuid__": "d4ab288b-259f-4517-bd13-685c99f15fa6"}, "iosBundleManifest": {"__uuid__": "4eb3d178-d12b-40d8-8cc3-************"}, "_id": "f8AFrJKQ1Mqq4mgZR7C/jc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cdbhFoDblHJIqzjDitul8f", "sync": false}, {"__type__": "cc.Node", "_name": "Lobby", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 12}, {"__id__": 72}, {"__id__": 495}], "_active": true, "_components": [], "_prefab": {"__id__": 547}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "46gVmbouxHmrVjwNMEUAcj"}, {"__type__": "cc.Node", "_name": "LoginPanel", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 71}], "_prefab": {"__id__": 546}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a4BNPdGclF8b/QdZ6IngjP"}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}, {"__id__": 29}, {"__id__": 44}, {"__id__": 55}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}, {"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 92, "g": 92, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ccfR8EtUFMEbmM6qt84TMg"}, {"__type__": "cc.Node", "_name": "UsernameEditbox", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 19}, {"__id__": 23}], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86T1qda7JGRqzusFGdyXqQ"}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "066tdnoc9G0J6TWXHzmj5p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff0e91c7-55c6-4086-a39f-cb6e457b8c3b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c0nBp1FNpMqZTNKHRZ2dIh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_id": "717CMBs5lAgrBcEoQPbo49"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "bbs4HzEoJByK4CGML//tmL", "sync": false}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0eY7CmvxtHl4Ee5xVpCEzi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "a5team@0050", "_N$string": "a5team@0050", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "add9u0DolMV7HiPOj8QmIS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "cbHaVn8yhB844X6SoVXZUN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "894k0bUm5GhJZgmshAcDMb", "sync": false}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": false, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ebQlo9NKFLRrMO0j3sbmUL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Enter username here...", "_N$string": "Enter username here...", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "5cdhHHKaRGVqd9IwsBGlte"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "1dAa60TLZEfq2NJp5X7Tok"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a2nRQzfCpHQYCfEPUtdxQO", "sync": false}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_string": "a5team@0050", "returnType": 0, "maxLength": 256, "_tabIndex": 0, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_N$textLabel": {"__id__": 20}, "_N$placeholderLabel": {"__id__": 24}, "_N$background": {"__id__": 16}, "_N$inputFlag": 5, "_N$inputMode": 6, "_N$stayOnTop": false, "_id": "6c1iwe/zZOpZkDZ6l54Hmn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "17ujXnFFNEI4HG5agaMkHK", "sync": false}, {"__type__": "cc.Node", "_name": "PasswordEditbox", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 30}, {"__id__": 34}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d7zwZWXiVPPY1op7DhaXHm"}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f7kXc9YSNAIKJMyKamATMa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff0e91c7-55c6-4086-a39f-cb6e457b8c3b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "29foz5pzlEc5pL87Birlqe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_id": "47jejg6NJPFK4u0PEDtxqy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "b9GV21Zw9C+qP00RIPjDVP", "sync": false}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "10WRFOo11ADrH0F5pf+djz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "●●●●●●", "_N$string": "●●●●●●", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "a6zH5ZQjhJx42GLsx6WyTF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "1fINRp1Q1Cb4xsPLDT51H3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "76qnZcPH9J2rfLoUkBmI0q", "sync": false}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": false, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8f2XClCu9IiIAuuXnKWtjv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Enter password here...", "_N$string": "Enter password here...", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "9e3gjvAXxA4qabKb+HRM39"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "31EwBNXiZN2pTFs/UKK7jA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "55p/v2O8VNBo+lVPBhC2q4", "sync": false}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_string": "654321", "returnType": 0, "maxLength": 256, "_tabIndex": 0, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_N$textLabel": {"__id__": 35}, "_N$placeholderLabel": {"__id__": 39}, "_N$background": {"__id__": 31}, "_N$inputFlag": 0, "_N$inputMode": 6, "_N$stayOnTop": false, "_id": "82I+xAMR1GjZNJC6UMvCL4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "862ssh+0FOI4MWhb0ilQ0P", "sync": false}, {"__type__": "cc.Node", "_name": "LoginButton", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 45}], "_active": true, "_components": [{"__id__": 52}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bdT1UeSBDKpt2gLg4XgNG"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 44}, "_children": [{"__id__": 46}], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "57K52DVEhBmIqq1lPaoqie"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [{"__id__": 47}], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8aqXfws/dHC7v82YvNkPvh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "2crsXD38xICpiuFSoBlt70"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cc3ufLAzdN+5frAXfHvA/2", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "faLQMxPgpOqJ7/3VS1Q/i9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "aezi6eHVFD8pZpcHKbzfGm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "579SLj6gBDsqPFwKbzBGBy", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 53}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 45}, "_id": "6cM5vB1w9H6oqvFhGGogFo"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 12}, "component": "", "_componentId": "aa5461LSq5Mr6EBXJc0/jvc", "handler": "login", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "5dsaakQQVDBrExilWCHkFm", "sync": false}, {"__type__": "cc.Node", "_name": "autoLoginToggle", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 56}, {"__id__": 59}, {"__id__": 62}], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [164.419, -74, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9d6GC7zmtBQaRjVP+dD+Z8"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 57}], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "59TWLIoIZMKbcJynrkwm9j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6827ca32-0107-4552-bab2-dfb31799bb44"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2es82C6MBCHbe1yexsMo8f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "0dby/y/7lL7IaG0v+/cMS1", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edOMvQEe9IQbvZSu3K8rsn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "90004ad6-2f6d-40e1-93ef-b714375c6f06"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "87nF8LweBHZquzP+c+iG8/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "b1u8T0tGdNFIdijh+G1IBC", "sync": false}, {"__type__": "cc.Node", "_name": "autoLoginLabel", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-66.578, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "56afDDb+pFjKEaO6qvzu0U"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "自动登录", "_N$string": "自动登录", "_fontSize": 25, "_lineHeight": 40, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "faZubQxMVO2LTrz7S1kIU+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "f2QH5Y+LpNEbNvH0YQh+Vm", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 56}, "_N$isChecked": true, "toggleGroup": null, "checkMark": {"__id__": 60}, "checkEvents": [], "_id": "f53hrFXwxMwp3CpHWhLyHc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "43xafN6GhHDopQLHT+5qLb", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2dnGvNIexGkZQfClpGVb2I"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 400, "height": 200}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 10, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "4eK5euTq5H0qoQ+LBzlWh6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3eTycHb3hO8oJUWhl1Xmrr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "e37ZKXy6hG1aUfOmPOSxah", "sync": false}, {"__type__": "aa5461LSq5Mr6EBXJc0/jvc", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "usernameEditBox": {"__id__": 27}, "passwordEditBox": {"__id__": 42}, "loginButton": {"__id__": 52}, "miniGamePanel": {"__id__": 72}, "autoLoginToggle": {"__id__": 65}, "autoLoginLabel": {"__id__": 63}, "platform": 0, "environment": 0, "_id": "4ci5agNUlAtrhy3m1yAzwg"}, {"__type__": "cc.Node", "_name": "MininGamePanel", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 73}, {"__id__": 77}, {"__id__": 102}, {"__id__": 355}, {"__id__": 485}, {"__id__": 498}, {"__id__": 516}, {"__id__": 534}], "_active": false, "_components": [{"__id__": 544}], "_prefab": {"__id__": 545}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 100, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6e2YHkb3dLBYajTblSHXLi"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}], "_prefab": {"__id__": 76}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 100, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e4a3Wiul5FUqUWSIgc9zDg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "751UedLslB8q399LTrFEJY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": -30, "_bottom": -30, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "95H8WXcVFCf4/7NhEDxGFg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "96083d03-c332-4a3f-9386-d03e2d19e8ee"}, "fileId": "b9dd7OqiNKdqdco2x7+B2y", "sync": false}, {"__type__": "cc.Node", "_name": "Tab", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 78}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 210, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -443.338, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c05kZWs4dJd5bCHWXGqqE+"}, {"__type__": "cc.Node", "_name": "Texas", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 79}], "_active": true, "_components": [{"__id__": 86}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "64soQdTg9IV77J6TXv2Irg"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 78}, "_children": [{"__id__": 80}], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c2XbI6pWdNsKrdPpeVrV4x"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "14Cb+GQUdKX6lrfJ7nko6k"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Texas", "_N$string": "Texas", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "3c0RjlbvdPPqpW92YXg4x6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "55fGPCpbhGJo1KtjGBk+yg", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0eftBdXDtPLKVfZNjrzE5G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "30gxIleU9Crrv8akleyZGE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "d8mrROe2NFu6/RaYz4suTR", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 87}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 79}, "_id": "2fHXqqhoJIl4joUhr5nDLX"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 72}, "component": "", "_componentId": "c1f54m7435HtLfNeaijuCfD", "handler": "showTexasGameList", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "7dwylKOJhH2JU/ZE4jg2Jm", "sync": false}, {"__type__": "cc.Node", "_name": "MiniGame", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 90}], "_active": true, "_components": [{"__id__": 97}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7fV3EKpnxMDq3H6LnWs5VL"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 89}, "_children": [{"__id__": 91}], "_active": true, "_components": [{"__id__": 94}, {"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fdlDi3RcNPfaCKiuopPASM"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 90}, "_children": [], "_active": true, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7dHriuq5xPXaC7cJMa/by3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Mini Game", "_N$string": "Mini Game", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "e21YxsEmlACoFlgLtvnawD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "1fvzRAcnRCqbDmDRTwjET5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "984C4xRvBEvooLVhMvPLF2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "83hPxU9XZE/ohlOlxUn+7h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "27npl+OrVEiJRvL3auqolY", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 98}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 90}, "_id": "4d8YOSBzZPPrbfTDi7SgN6"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 72}, "component": "", "_componentId": "c1f54m7435HtLfNeaijuCfD", "handler": "showMiniGameList", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "06svt9Q79H55l33CugnLWo", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 210, "height": 0}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "83H3QXHftPw6zUfR0UvF9h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a0B81+7XFCJKsxtcFgfxt+", "sync": false}, {"__type__": "cc.Node", "_name": "MiniGameListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 103}, {"__id__": 110}], "_active": false, "_components": [{"__id__": 352}, {"__id__": 108}, {"__id__": 353}], "_prefab": {"__id__": 354}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.523, -29.625999999999976, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bqHwPY5VKT40VPYxsLu6n"}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 512, "_parent": {"__id__": 102}, "_children": [{"__id__": 104}], "_active": true, "_components": [{"__id__": 107}, {"__id__": 349}, {"__id__": 350}], "_prefab": {"__id__": 351}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0f4DcnIKlCNLhp7Bu6AnxI"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 105}], "_prefab": {"__id__": 106}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11, -31.25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "feaVQhzhhMgYGV4edP2F1J"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "86BZTgy0hI67BV6Zox7JEU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "17i3b9OUpNt4H4QM3ZDVep", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_scrollView": {"__id__": 108}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 105}, "_N$direction": 1, "_id": "3c5ahjBpZJgrFP842BHkYN"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 109}, "content": {"__id__": 109}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 107}, "_id": "6fGOs6Mp1MsZQesa4qxdFS"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 110}, "_children": [{"__id__": 114}], "_active": true, "_components": [{"__id__": 347}], "_prefab": {"__id__": 348}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 128, "g": 128, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2bx+8tDf9FqJ1CmKO4M/uW"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 102}, "_children": [{"__id__": 109}], "_active": true, "_components": [{"__id__": 111}, {"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "45EAVNGxNAlaYgffaR9mna"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "420wWVAdFNcbhY4vwpUFRk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 12, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "afJRsEK2dH+6qgwkCFA3YM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "82P0+WowZHZJNje0dxboQy", "sync": false}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [{"__id__": 115}, {"__id__": 153}, {"__id__": 191}, {"__id__": 229}, {"__id__": 267}, {"__id__": 305}], "_active": true, "_components": [{"__id__": 343}, {"__id__": 344}, {"__id__": 345}], "_prefab": {"__id__": 346}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 128, "g": 128, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "54NnHWG1FAQq42xmjqr3gn"}, {"__type__": "cc.Node", "_name": "GameListItemCowboy", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 116}, {"__id__": 123}, {"__id__": 134}], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}, {"__id__": 151}], "_prefab": {"__id__": 152}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, 285, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3epQYxNrBO+4+E9nlAEdu0"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 115}, "_children": [{"__id__": 117}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bfEI9cUd9GErULIOOn1qQH"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 118}], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "158OwWwPNNgoSH+XdwqbhR"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "28ruFW405GwJ5ROf+gRig6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4aexNNi9tPFqVRD75+cKVB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "78071C18NGd6587Hm/b4Dg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [{"__id__": 124}, {"__id__": 127}, {"__id__": 130}], "_active": true, "_components": [], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f8VAozY8FKKLbKBPfhI3lI"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": false, "_components": [{"__id__": 125}], "_prefab": {"__id__": 126}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1cQBkZ0KJGebALsjY5WlfH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0fBbIsC6lJm7GKFWA2hEP4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": false, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f6hu318uROcLLhA8wJK54x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bcMAGEEftNlYPZS+UFWEOB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": false, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fd9CVXLeBJnoJ5abmn7W6F"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "53FU6JMx1ByrCjo5k/ZyoC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [{"__id__": 135}], "_active": false, "_components": [{"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6750cPkQpCa5w5QVrE3AH4"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 134}, "_children": [{"__id__": 136}, {"__id__": 139}], "_active": true, "_components": [], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dbSxuqX8pECr+bSb2UGVNO"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 137}], "_prefab": {"__id__": 138}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8awDQFyx5OSphLk7FhjxHH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "6eWo4IQr1Ndb8Oa3ZT6ZSK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [{"__id__": 140}], "_active": true, "_components": [{"__id__": 143}, {"__id__": 144}], "_prefab": {"__id__": 145}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "81ehPTsGZGMY9Gk2H2PgYa"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 141}], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "38t0FqGXZE56XrXTqC+65C"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "87w4jEdRlBwZsJw5uG38Nf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 141}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "72EURXY0tFIKDdHutOK5h8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f6yJAV+LZCw5GUkjLZobND"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "progressBar": {"__id__": 143}, "barText": {"__id__": 137}, "loadingPanel": {"__id__": 135}, "_id": "f7TNxox3BIZaQ31blBqoq8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 134}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 116}, "_id": "c2ZZUy4pNGooiDc92i42qp"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "gameName": "Cowboy", "gameId": 10, "bundle": "cowboy", "gameNameLabel": {"__id__": 118}, "needDownloadSprite": {"__id__": 125}, "downloadFailSprite": {"__id__": 128}, "cannotDownloadSprite": {"__id__": 131}, "bundleDownloadControl": {"__id__": 151}, "_id": "bcggWODwlGlq7GyS9RXTKy"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "progressBar": {"__id__": 147}, "_id": "8d600BeQpPZ51RxeaF7DRR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 115}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "21NoKOEtNAKL7R4Sd2dci5", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemHumanboy", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 154}, {"__id__": 161}, {"__id__": 172}], "_active": true, "_components": [{"__id__": 187}, {"__id__": 188}, {"__id__": 189}], "_prefab": {"__id__": 190}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, 285, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "51ZOvX23hGW4HPzVkdJZgL"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 153}, "_children": [{"__id__": 155}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 159}], "_prefab": {"__id__": 160}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "96LTclfqRFL64R+Phnc5Fb"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 154}, "_children": [], "_active": true, "_components": [{"__id__": 156}], "_prefab": {"__id__": 157}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f94Q59qd5GDrwlrcTNX/N1"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "05oX/Th+NNz6Bv6NAFxVZL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dc3AU0vvpNxKnLThIg8BCK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "8bwXsmYzNBzIuOOGRcIzmF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 153}, "_children": [{"__id__": 162}, {"__id__": 165}, {"__id__": 168}], "_active": true, "_components": [], "_prefab": {"__id__": 171}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b57oHEJoVPspYLxn81+9Br"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": false, "_components": [{"__id__": 163}], "_prefab": {"__id__": 164}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7ccnn1WjJHObLNqKg4jMtP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d476Z/b4BCBo5sRgPkxyYz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": false, "_components": [{"__id__": 166}], "_prefab": {"__id__": 167}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1d6XPTQrRMtZkHMEdaQSMm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "24HtqcAeBEy5RRokrayL0i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": false, "_components": [{"__id__": 169}], "_prefab": {"__id__": 170}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8eLqwCRtpH6oWqnxArTXAt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "acfbf/qbpHK4wBLjzYof2f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 153}, "_children": [{"__id__": 173}], "_active": false, "_components": [{"__id__": 185}], "_prefab": {"__id__": 186}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c4Rbv4L4ZFo7IjsP3/TVNt"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 174}, {"__id__": 177}], "_active": true, "_components": [], "_prefab": {"__id__": 184}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a9qlV8KIJOPbiJPO042bEM"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 173}, "_children": [], "_active": true, "_components": [{"__id__": 175}], "_prefab": {"__id__": 176}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f9odrF01RKh7N3l7B4aEqk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 174}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "66s3X1rjVMgpK1xpZE4RLr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 173}, "_children": [{"__id__": 178}], "_active": true, "_components": [{"__id__": 181}, {"__id__": 182}], "_prefab": {"__id__": 183}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e8Y++z4a1N7rVAqStawCiJ"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 177}, "_children": [], "_active": true, "_components": [{"__id__": 179}], "_prefab": {"__id__": 180}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7aEpuQmNlCzZ4nkinqB31D"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ebN3MJVzBNp5/1d9CYgfl+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 179}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "a4W0z5Tg5LXr/ULYeyrP5x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "70wyH5RxVKYYHRplRRD45K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "progressBar": {"__id__": 181}, "barText": {"__id__": 175}, "loadingPanel": {"__id__": 173}, "_id": "dd+GmCk9RFs4h6kF5GUFUH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 154}, "_id": "84urJkXGZJD5z+Qn/Ede42"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "gameName": "HumanBoy", "gameId": 30, "bundle": "humanboy", "gameNameLabel": {"__id__": 156}, "needDownloadSprite": {"__id__": 163}, "downloadFailSprite": {"__id__": 166}, "cannotDownloadSprite": {"__id__": 169}, "bundleDownloadControl": {"__id__": 189}, "_id": "03k7Mt5dFAtacrgZixCwJz"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "progressBar": {"__id__": 185}, "_id": "10sx0qr/VKyovA6s3CMvKk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 153}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "9fVxo0kUJKgaQpH23fVeFp", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemPokerMaster", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 192}, {"__id__": 199}, {"__id__": 210}], "_active": true, "_components": [{"__id__": 225}, {"__id__": 226}, {"__id__": 227}], "_prefab": {"__id__": 228}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, 125, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "62gb7YpF9FKbxx2uK9L6EG"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 191}, "_children": [{"__id__": 193}], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}], "_prefab": {"__id__": 198}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5dGLhQgNtKnIi55ew6g4QU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 192}, "_children": [], "_active": true, "_components": [{"__id__": 194}], "_prefab": {"__id__": 195}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aaZA7zKqtMJ5fC62/LP2NZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "cclQPH9sxKfrqOgD/+cin1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1430+cyLZIcoEHWZELJGEL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "9avI9rnj1BKpxP7auXRfO5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [{"__id__": 200}, {"__id__": 203}, {"__id__": 206}], "_active": true, "_components": [], "_prefab": {"__id__": 209}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "30mPsDWMxDXJxy0n40osO1"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [], "_active": false, "_components": [{"__id__": 201}], "_prefab": {"__id__": 202}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86RlPCi1hPvbQYmbQ5jaD+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dbkjPonhNA5L4rF6kj42sv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [], "_active": false, "_components": [{"__id__": 204}], "_prefab": {"__id__": 205}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "54yDsfLrlHmL3FznoIjWuR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 203}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d1+cMiOzpIdbPc9UqaxmQJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [], "_active": false, "_components": [{"__id__": 207}], "_prefab": {"__id__": 208}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d2QvLagsVJLaauUbEeTsL4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "92kxNrfHFJso9HX1ePhOQA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [{"__id__": 211}], "_active": false, "_components": [{"__id__": 223}], "_prefab": {"__id__": 224}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bdru1MGx9He4SaCr+kBEZ3"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 210}, "_children": [{"__id__": 212}, {"__id__": 215}], "_active": true, "_components": [], "_prefab": {"__id__": 222}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "832G+X9Z9AVZ/+JSEqdkwT"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 211}, "_children": [], "_active": true, "_components": [{"__id__": 213}], "_prefab": {"__id__": 214}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b5y6z+Ve5FUL51mFGgZY+W"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "7b0rbjEzhMNYHhWtXrwj5U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 211}, "_children": [{"__id__": 216}], "_active": true, "_components": [{"__id__": 219}, {"__id__": 220}], "_prefab": {"__id__": 221}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c7SEURIS9IjYt9XQfe36TD"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 215}, "_children": [], "_active": true, "_components": [{"__id__": 217}], "_prefab": {"__id__": 218}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "99fZWj5qZARJh1uZmee8Jy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7dpfaQJ5hF1ZDIeoBf6Ct5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 217}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "fbeZKrcRZMgrj+Jg/WjZKd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d4JM8TsCtD+5Njrlmf5rbQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "progressBar": {"__id__": 219}, "barText": {"__id__": 213}, "loadingPanel": {"__id__": 211}, "_id": "44nAh6EU1JrJ0PqDSMGek3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 192}, "_id": "640UmZvGhFdb64Z6JPlmLZ"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "gameName": "PokerMaster", "gameId": 70, "bundle": "poker-master", "gameNameLabel": {"__id__": 194}, "needDownloadSprite": {"__id__": 201}, "downloadFailSprite": {"__id__": 204}, "cannotDownloadSprite": {"__id__": 207}, "bundleDownloadControl": {"__id__": 227}, "_id": "02l8Mg0VdFL4GdMVBgORee"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "progressBar": {"__id__": 223}, "_id": "7b0MVlyHxOdr3y9XRH8OEf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 191}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "73VHJV/0BOWr/yNdc4QLXg", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemVideoCowboy", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 230}, {"__id__": 237}, {"__id__": 248}], "_active": true, "_components": [{"__id__": 263}, {"__id__": 264}, {"__id__": 265}], "_prefab": {"__id__": 266}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, 125, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1aFfXc7LpEsphRhE1s29Md"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 229}, "_children": [{"__id__": 231}], "_active": true, "_components": [{"__id__": 234}, {"__id__": 235}], "_prefab": {"__id__": 236}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a2uc030vhF3JJP4aK3uBdb"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 232}], "_prefab": {"__id__": 233}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "69EN0qOY1MKrvavRSu1DCY"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "a8xWKxkdJPOrWFVGE8pNXZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "72Oxr0FFFK8bxapiG9/U6H"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "9fEb+PQ8JJJo1GhB4QMQL+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 229}, "_children": [{"__id__": 238}, {"__id__": 241}, {"__id__": 244}], "_active": true, "_components": [], "_prefab": {"__id__": 247}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fbVBDfmfdIfbjutfTmQ0Oo"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 237}, "_children": [], "_active": false, "_components": [{"__id__": 239}], "_prefab": {"__id__": 240}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5148FgKGVGKYs/LXWOB0+U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 238}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "38ngoK7npGdbey5VJ0QrsY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 237}, "_children": [], "_active": false, "_components": [{"__id__": 242}], "_prefab": {"__id__": 243}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2a1FQGVU1HU5rmCZM3Hj85"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6cLYqi9I5ObapMUJIt1N5C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 237}, "_children": [], "_active": false, "_components": [{"__id__": 245}], "_prefab": {"__id__": 246}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6foXZELcJIxIgNbxy7nKlz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 244}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4fhM/tJt9JsIJ8h/9aL7eS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 229}, "_children": [{"__id__": 249}], "_active": false, "_components": [{"__id__": 261}], "_prefab": {"__id__": 262}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "66PjnRWypBc7LHwFOkIi3r"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [{"__id__": 250}, {"__id__": 253}], "_active": true, "_components": [], "_prefab": {"__id__": 260}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8bhRzGpRtAn6+/vwiBdjuC"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 249}, "_children": [], "_active": true, "_components": [{"__id__": 251}], "_prefab": {"__id__": 252}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "22o4pYDShIN6u9QQH53MAP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 250}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "dbBIFZdupMD5wwMLLrwf1S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 249}, "_children": [{"__id__": 254}], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}], "_prefab": {"__id__": 259}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53rdn/m85I4589byqVAAu4"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 253}, "_children": [], "_active": true, "_components": [{"__id__": 255}], "_prefab": {"__id__": 256}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6c6LTWI1FIFIUs6Z5Y88c4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 254}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "49iyXQ7zFOkKG0sikqjEYu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 255}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "c0VgfMT6RBW5J8aAihpKif"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "aaXGTVIvdJ/7P9aQByuOha"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "progressBar": {"__id__": 257}, "barText": {"__id__": 251}, "loadingPanel": {"__id__": 249}, "_id": "075X4WfZdGSqfEMtflqkvR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 248}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 230}, "_id": "f1BDPW5fVLkKvQxexYntlQ"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "gameName": "VideoCowboy", "gameId": 50, "bundle": "video-cowboy", "gameNameLabel": {"__id__": 232}, "needDownloadSprite": {"__id__": 239}, "downloadFailSprite": {"__id__": 242}, "cannotDownloadSprite": {"__id__": 245}, "bundleDownloadControl": {"__id__": 265}, "_id": "bb4IfJJDpPq5mlhA14Rlk9"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "progressBar": {"__id__": 261}, "_id": "789STyk3VMWaWLLLgWOP9X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "1aIG4lw+NI8KYyHKh1lZ1w", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemTexas", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 268}, {"__id__": 275}, {"__id__": 286}], "_active": false, "_components": [{"__id__": 301}, {"__id__": 302}, {"__id__": 303}], "_prefab": {"__id__": 304}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, -20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "eeON9sXfVB7pa3/JpAFgWV"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 267}, "_children": [{"__id__": 269}], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}], "_prefab": {"__id__": 274}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9fuJGK2y1O8oct7GVbzXej"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 268}, "_children": [], "_active": true, "_components": [{"__id__": 270}], "_prefab": {"__id__": 271}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d99gEeC5RINLww2dNVRUnc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 269}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cash Game\n", "_N$string": "Cash Game\n", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "bfYY4N5l1JObfiyUzSHA4I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7dl5A9fgBGDJ3Ld6YUzKpW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "6c9DNpotNFjr2eX6CBUtvD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 267}, "_children": [{"__id__": 276}, {"__id__": 279}, {"__id__": 282}], "_active": true, "_components": [], "_prefab": {"__id__": 285}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "79ZThwUztOvqntNsur0XPw"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [], "_active": false, "_components": [{"__id__": 277}], "_prefab": {"__id__": 278}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f2fliucO9DyIz5UxlQrlUE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 276}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "80T06za6hC3pqL9RGQlJE2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [], "_active": false, "_components": [{"__id__": 280}], "_prefab": {"__id__": 281}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aelIHr5FpHkZ6yebx5sU+Z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 279}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "44BbGCgR9FoI1oLbLtzvnC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [], "_active": false, "_components": [{"__id__": 283}], "_prefab": {"__id__": 284}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "69G+cL6v1JDYZzfhvYW6Bo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "735+tfMG9GcqKoTdWNBjyL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 267}, "_children": [{"__id__": 287}], "_active": false, "_components": [{"__id__": 299}], "_prefab": {"__id__": 300}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "59wJHW8QFEo4EkE13xbZa5"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 286}, "_children": [{"__id__": 288}, {"__id__": 291}], "_active": true, "_components": [], "_prefab": {"__id__": 298}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c8RxcPl15MpYHEFnG/MLZ3"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 287}, "_children": [], "_active": true, "_components": [{"__id__": 289}], "_prefab": {"__id__": 290}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ac6JiWtMlCe5zG0ZoSI5/4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "23ulBQoglBgqP0y0U7Jrfm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 287}, "_children": [{"__id__": 292}], "_active": true, "_components": [{"__id__": 295}, {"__id__": 296}], "_prefab": {"__id__": 297}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "18LthQ7ZRLZYMiDH7JPZGm"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 291}, "_children": [], "_active": true, "_components": [{"__id__": 293}], "_prefab": {"__id__": 294}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5bgoq6zaxH6Yw9zgX3Z9wS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "46cAjDIJpOQJIHr2UwL7+s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 291}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 293}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "faWNSS5jNDSJ3ZdstRb/DE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 291}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "91vjZWACNCR6OwRbrqY+eV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "progressBar": {"__id__": 295}, "barText": {"__id__": 289}, "loadingPanel": {"__id__": 287}, "_id": "a1MHTuD7xP+7FqhnEojLDA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 286}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 268}, "_id": "59x9Jhn8dOiZqh5VrtsN6l"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "gameName": "Texas Portrait", "gameId": 2, "bundle": "texas-portrait", "gameNameLabel": {"__id__": 270}, "needDownloadSprite": {"__id__": 277}, "downloadFailSprite": {"__id__": 280}, "cannotDownloadSprite": {"__id__": 283}, "bundleDownloadControl": {"__id__": 303}, "_id": "7eGXLuqL1IFLJ6eNio0bkH"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "progressBar": {"__id__": 299}, "_id": "ac7jo2H5VIMZdcBiNl295t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "84z7KccGFGvJS1LKz//oH4", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemTexas copy", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 306}, {"__id__": 313}, {"__id__": 324}], "_active": false, "_components": [{"__id__": 339}, {"__id__": 340}, {"__id__": 341}], "_prefab": {"__id__": 342}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, -20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "432Kd7OgtOuJW4AVKjf8Jx"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 305}, "_children": [{"__id__": 307}], "_active": true, "_components": [{"__id__": 310}, {"__id__": 311}], "_prefab": {"__id__": 312}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "04C+5JR/dI0ollO10sNdSh"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 306}, "_children": [], "_active": true, "_components": [{"__id__": 308}], "_prefab": {"__id__": 309}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "baOV4uXvdDqJhT3lkzMSmJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cash Game\n", "_N$string": "Cash Game\n", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "f4XBCKASJC27UFOa71tdzB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 306}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4aB9UM3YpNybhyBCXg8egl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 306}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "7eNFQzLJFCA6y7DkiqTZm5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 305}, "_children": [{"__id__": 314}, {"__id__": 317}, {"__id__": 320}], "_active": true, "_components": [], "_prefab": {"__id__": 323}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b8CgmOGSJGK6XHRb4oJyMn"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 313}, "_children": [], "_active": false, "_components": [{"__id__": 315}], "_prefab": {"__id__": 316}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "82SfZyfe1Bc7WIbJjOSFgJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 314}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "64ZZQNE5hOEoYox8VgyiZA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 313}, "_children": [], "_active": false, "_components": [{"__id__": 318}], "_prefab": {"__id__": 319}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ecHA5+UwRP0b9OpEBT2PZr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 317}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e05Ab2wrJMw7/jJRVnH+Ke"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 313}, "_children": [], "_active": false, "_components": [{"__id__": 321}], "_prefab": {"__id__": 322}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fffNdx+m9KBYQ3XhTAk/6i"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 320}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4cK0YNauZI9Kj34w2fB5S2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 305}, "_children": [{"__id__": 325}], "_active": false, "_components": [{"__id__": 337}], "_prefab": {"__id__": 338}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b6vmVJGLJB77bxP7rryatV"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 324}, "_children": [{"__id__": 326}, {"__id__": 329}], "_active": true, "_components": [], "_prefab": {"__id__": 336}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4akLLyR51IorIM8XoWpoAh"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 325}, "_children": [], "_active": true, "_components": [{"__id__": 327}], "_prefab": {"__id__": 328}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a98Vj+IKNDeakjcdKqF4mW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 326}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "c7IRjcbzBEZaRNTeXiqkZB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 325}, "_children": [{"__id__": 330}], "_active": true, "_components": [{"__id__": 333}, {"__id__": 334}], "_prefab": {"__id__": 335}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "19WnMfyjpIh68ebA9bpKOg"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 329}, "_children": [], "_active": true, "_components": [{"__id__": 331}], "_prefab": {"__id__": 332}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f31MM05OVKOZdc7v5gjYw/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 330}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bcIqGRP3dNFr1hlr0yJD0Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 329}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 331}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "a5Vo1076xFLYhBcyv5Qimo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 329}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d8HJSMtMJKvqIByDbFl50e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 324}, "_enabled": true, "progressBar": {"__id__": 333}, "barText": {"__id__": 327}, "loadingPanel": {"__id__": 325}, "_id": "1aZPj0X0NGAL0ABqO1rxXy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 324}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 306}, "_id": "1b7KdVNL5P0bJa9q2hjlUu"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "gameName": "Texas Landscape", "gameId": 2, "bundle": "texas-landscape", "gameNameLabel": {"__id__": 308}, "needDownloadSprite": {"__id__": 315}, "downloadFailSprite": {"__id__": 318}, "cannotDownloadSprite": {"__id__": 321}, "bundleDownloadControl": {"__id__": 341}, "_id": "edCZl9URxKlYzID44zhvto"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "progressBar": {"__id__": 337}, "_id": "896/tki21Jb4ZBx1aliZ9c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 305}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "e188oBJsNIlY0enixbhrNN", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "41YAHqeGRETJpJ2a1NsWIC"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 568, "height": 750}, "_resize": 0, "_N$layoutType": 3, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 30, "_N$paddingRight": 0, "_N$paddingTop": 20, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 20, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "05dCl+oHxNhpt7gTDgwoz0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 200, "_originalHeight": 150, "_id": "eaJfaX87ZHYZ42mEyjHxjW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "d4msB0q4tNsaLOlKDei1ag", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 400, "_id": "e9V2p/h/tEaayvELMfdtU7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "4cBfOJ6lJDdrYiZ8HvCnKb", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": "28qom8NpFCoqUIUJ2YH6Nz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5fe5dcaa-b513-4dc5-a166-573627b3a159"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7br3BTX85Etq/s0C8oi/Yd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "72rkSqC7ZKWZZWzT+YfSKl", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "64jqUQd9RB9K2GiRoiZILq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 45.374000000000024, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "68aDILO21LZYnRH8POJWaC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "71N93ijeNHBrOhK69AdE/3", "sync": false}, {"__type__": "cc.Node", "_name": "TexasGameListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 356}, {"__id__": 415}, {"__id__": 463}], "_active": true, "_components": [{"__id__": 482}, {"__id__": 483}], "_prefab": {"__id__": 484}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -29.629999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3516fRo4hAi4PtqEadw2u3"}, {"__type__": "cc.Node", "_name": "GameTypeTab", "_objFlags": 0, "_parent": {"__id__": 355}, "_children": [{"__id__": 357}, {"__id__": 368}, {"__id__": 379}, {"__id__": 390}, {"__id__": 401}], "_active": true, "_components": [{"__id__": 412}, {"__id__": 413}], "_prefab": {"__id__": 414}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 540, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10, 353, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "91aE7fD0pCNLnOZA7CAo2N"}, {"__type__": "cc.Node", "_name": "NLHE", "_objFlags": 0, "_parent": {"__id__": 356}, "_children": [{"__id__": 358}], "_active": true, "_components": [{"__id__": 365}], "_prefab": {"__id__": 367}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-220, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8dql+xwwVBipohe+J51Fqm"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 357}, "_children": [{"__id__": 359}], "_active": true, "_components": [{"__id__": 362}, {"__id__": 363}], "_prefab": {"__id__": 364}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8fUTaNUnFIyKP827/ZoWK3"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 358}, "_children": [], "_active": true, "_components": [{"__id__": 360}], "_prefab": {"__id__": 361}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "54QDOFO85DoraXEfzqsrLe"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 359}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "NLHE", "_N$string": "NLHE", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "68lpB4pFpLhJobENOkQzZz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "73mf71IfJAoa03RHGCRfhR", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d0HHvDn/JKPb5jBWXVr2cY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "65xyAW/4pK+biRa6EOuGBt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "44CDsyl2hFnIWuFAqrBRbv", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 357}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 366}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 358}, "_id": "f6vuCpMYxPQ7hSoefS10AN"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectGameCategory", "customEventData": "0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "39tWcHsrlIxo/Bsh4ZsMUG", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 356}, "_children": [{"__id__": 369}], "_active": true, "_components": [{"__id__": 376}], "_prefab": {"__id__": 378}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-110, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "51DuQ8UbRO2YIxN4gH+wrN"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 368}, "_children": [{"__id__": 370}], "_active": true, "_components": [{"__id__": 373}, {"__id__": 374}], "_prefab": {"__id__": 375}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53V57Jhx1B14vBSSflbLXU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 371}], "_prefab": {"__id__": 372}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7ewwmJQClJGK2k9+bCLXXz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Short Deck", "_N$string": "Short Deck", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "bb/yPG18dBCoEgCW+x0HZx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "40ibnrkihM1YG5qdYu4R7N", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 369}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "90Y08jSy5ALIi0oPxui+Zx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 369}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "aeRz+q23dEZ53uMZYXqFvL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "5ec4H6BOJOO7ncAXWJ2z3a", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 377}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 369}, "_id": "044uvlvzNISJIhpzkkPglf"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectGameCategory", "customEventData": "1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "75VCxqwLtCRJ9F6RFB8MIQ", "sync": false}, {"__type__": "cc.Node", "_name": "PLO", "_objFlags": 0, "_parent": {"__id__": 356}, "_children": [{"__id__": 380}], "_active": true, "_components": [{"__id__": 387}], "_prefab": {"__id__": 389}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "37nekEHHxG8rtPpKpB6Y8j"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 379}, "_children": [{"__id__": 381}], "_active": true, "_components": [{"__id__": 384}, {"__id__": 385}], "_prefab": {"__id__": 386}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dbYnmegKZBRa79++xBQPZk"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 380}, "_children": [], "_active": true, "_components": [{"__id__": 382}], "_prefab": {"__id__": 383}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "67OGr1matHJYyZnKZR65wu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 381}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "PLO", "_N$string": "PLO", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "48GUtGXMFLXJKwhUn0/Qkb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "bbOvVbYmtHXYjXbL8tUYVj", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ebuAx58RtIioucBsym/tdx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "16gyKOSVFDeZD5q8Ik63yl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "6bLg+5jfBJHYt/rmYgiv31", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 379}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 388}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 380}, "_id": "f1krCuX+JDvbVTYElcRCG5"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectGameCategory", "customEventData": "2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "5a2AIHKu5HZINGsJU215Sv", "sync": false}, {"__type__": "cc.Node", "_name": "Splash", "_objFlags": 0, "_parent": {"__id__": 356}, "_children": [{"__id__": 391}], "_active": true, "_components": [{"__id__": 398}], "_prefab": {"__id__": 400}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bHmvCwVVHe66zhAciuG4X"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 390}, "_children": [{"__id__": 392}], "_active": true, "_components": [{"__id__": 395}, {"__id__": 396}], "_prefab": {"__id__": 397}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c5qs1jdpRLS7gCEKj5I2fJ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 391}, "_children": [], "_active": true, "_components": [{"__id__": 393}], "_prefab": {"__id__": 394}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "855epepkpJm7Cvy+S8mrdi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 392}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Splash", "_N$string": "Splash", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "3cu4AOWutIVp8BgBYW60Ni"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "dd3FuiLoNEQo00pgdNHDEf", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "9dLh0A1RZDtphjqtiWD5xe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "399EmoksFPMZyctOD8kB65"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "76+aTv/R5KVo/4N5NquI/n", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 390}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 399}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 391}, "_id": "80VUnvFe5LXpMfRXeWCHXj"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectGameCategory", "customEventData": "3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "604iqT6wxKKJfSQeRo7q8Q", "sync": false}, {"__type__": "cc.Node", "_name": "Jackfruit", "_objFlags": 0, "_parent": {"__id__": 356}, "_children": [{"__id__": 402}], "_active": true, "_components": [{"__id__": 409}], "_prefab": {"__id__": 411}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [220, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "52LAi2zPxEqrSI/fTkDBjU"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 401}, "_children": [{"__id__": 403}], "_active": true, "_components": [{"__id__": 406}, {"__id__": 407}], "_prefab": {"__id__": 408}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b6T21G8tlDR5M6au6ffGAa"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 402}, "_children": [], "_active": true, "_components": [{"__id__": 404}], "_prefab": {"__id__": 405}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3eI6hUApxNlanFdpwox2yl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 403}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Jackfruit", "_N$string": "Jackfruit", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "24wTM8wTRBoL3Si7/DJ5pH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a7WDhf2KdATolfCQWezQdo", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 402}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "79Az/10upDLIv1De8hNiBn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 402}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "dfScbkXz9KCIZYwVOxZVML"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "9eafNPpj9Jh4Orp3AQAXgj", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 401}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 410}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 402}, "_id": "abo3fljbFJO6e2llII3hRk"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectGameCategory", "customEventData": "4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "dbid0awPNIc4UaB4Wncfj3", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 356}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 540, "height": 0}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "a4smZILjlOLrOIDm1Unj5m"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 356}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 10, "_right": 0, "_top": 22, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "33UPM+XH9BmaTWOK3fORLB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "25dGATY3NPbYn3XDKzlp+Y", "sync": false}, {"__type__": "cc.Node", "_name": "BlindLevelTab", "_objFlags": 0, "_parent": {"__id__": 355}, "_children": [{"__id__": 416}, {"__id__": 427}, {"__id__": 438}, {"__id__": 449}], "_active": true, "_components": [{"__id__": 460}, {"__id__": 461}], "_prefab": {"__id__": 462}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-105, 304.498, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f6mJ0a8wpLq7FW63dEL0Oh"}, {"__type__": "cc.Node", "_name": "Micro", "_objFlags": 0, "_parent": {"__id__": 415}, "_children": [{"__id__": 417}], "_active": true, "_components": [{"__id__": 424}], "_prefab": {"__id__": 426}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-135, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6aSUpMjKJPOoH62n7Zp53y"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 416}, "_children": [{"__id__": 418}], "_active": true, "_components": [{"__id__": 421}, {"__id__": 422}], "_prefab": {"__id__": 423}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9e0r3XtLFFoLUPHzFGdM1X"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 417}, "_children": [], "_active": true, "_components": [{"__id__": 419}], "_prefab": {"__id__": 420}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5fv8BDSVFlJW96dRl8IPL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 418}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Micro", "_N$string": "Micro", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "56Gvw7nMRMnKItkF5JW9hT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "c5q8e53wVPQ4e5B75BfJui", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 417}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "60seHKmplJOJCcSda81k6A"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 417}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "7aTZRxxWpGlZiHlgyAxM5W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "4dOAQeo45Fw5jOpItx7LSb", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 416}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 425}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 417}, "_id": "c9HZgwFHpAvZQozyuLWAKp"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectBlindLevel", "customEventData": "0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "3fkiVP6CFC84C/lh5oT6DX", "sync": false}, {"__type__": "cc.Node", "_name": "Small", "_objFlags": 0, "_parent": {"__id__": 415}, "_children": [{"__id__": 428}], "_active": true, "_components": [{"__id__": 435}], "_prefab": {"__id__": 437}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-45, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7dAW94QeVGVZNW4PtWEVhL"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 427}, "_children": [{"__id__": 429}], "_active": true, "_components": [{"__id__": 432}, {"__id__": 433}], "_prefab": {"__id__": 434}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fezvQwXblFhoU7GFNTrku3"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 428}, "_children": [], "_active": true, "_components": [{"__id__": 430}], "_prefab": {"__id__": 431}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "63lkGDG2JMUZJMP/VJKekX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 429}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Small", "_N$string": "Small", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "ffUIhOwrhMII5A+y28RHXk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "95ApvXqhtM1oV2Dxq2Gg2e", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 428}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b5ZDKabRdHTL9hPfOyn72z"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 428}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "7d1pn9TXZADo0RVgR17feV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cawyGuhTFINYq9gUCY7v+r", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 427}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 436}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 428}, "_id": "b7HXcx4/hLOptbekLgvvvw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectBlindLevel", "customEventData": "1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "21FGwOJQNBTa9OYvy7/J75", "sync": false}, {"__type__": "cc.Node", "_name": "Medium", "_objFlags": 0, "_parent": {"__id__": 415}, "_children": [{"__id__": 439}], "_active": true, "_components": [{"__id__": 446}], "_prefab": {"__id__": 448}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [45, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "60toNOOFJAwaS/qDbEjWRU"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 438}, "_children": [{"__id__": 440}], "_active": true, "_components": [{"__id__": 443}, {"__id__": 444}], "_prefab": {"__id__": 445}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a114bP6X1BB7nHL84iCDj2"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 439}, "_children": [], "_active": true, "_components": [{"__id__": 441}], "_prefab": {"__id__": 442}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d4AXPQxJhGZrYQMgLpfKqk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 440}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Medium", "_N$string": "Medium", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "5d+N6QkKdN7Zv7Y7pNS/yq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a7w09Zy3FCX4db9+hkP9er", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 439}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bbMTyVvsJJqJiefMC9UG1n"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 439}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "edWF/ZlBlIFYhvjR59N8Wu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "c7XzyZ9LVGp6cHwnoeKZ75", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 438}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 447}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 439}, "_id": "fb6H+HdaxB4oONJzC20DhD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectBlindLevel", "customEventData": "2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "82S6Mwz8xIurshBKDbcGtj", "sync": false}, {"__type__": "cc.Node", "_name": "Large", "_objFlags": 0, "_parent": {"__id__": 415}, "_children": [{"__id__": 450}], "_active": true, "_components": [{"__id__": 457}], "_prefab": {"__id__": 459}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [135, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0eghe6zChFtLHTkoSRldVp"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 449}, "_children": [{"__id__": 451}], "_active": true, "_components": [{"__id__": 454}, {"__id__": 455}], "_prefab": {"__id__": 456}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "02J5dZpO1FCKIAax1AXnHg"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 450}, "_children": [], "_active": true, "_components": [{"__id__": 452}], "_prefab": {"__id__": 453}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b4cY8wqUtILZPqFAS24DuJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 451}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Large", "_N$string": "Large", "_fontSize": 16, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "38rmpathpPn6QiqOij315x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "1aCUmQwxdIXJ3E4xc/hUea", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 450}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3bRzBN3SVLIrND1linTSvF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 450}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "f1X7yfMVtNtYquYCD1/g1G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "13PqMXjClNXowkbwQlH6NN", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 449}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 458}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 450}, "_id": "20XTAmG3BOCb8vovz7LrtY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 355}, "component": "", "_componentId": "7c689rALahHhbCICGRJluF6", "handler": "selectBlindLevel", "customEventData": "3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "feVTz00uFE6ZWcKL050FAB", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 415}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 350, "height": 0}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "4e9u3qQT1FpozxQhfq7NEe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 415}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 10, "_right": 0, "_top": 70.50200000000001, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "47SbNRl01Lo4e67Ydf0dLL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "68fudpYYBMtZUYXdd68AM3", "sync": false}, {"__type__": "cc.Node", "_name": "TexasGameList", "_objFlags": 0, "_parent": {"__id__": 355}, "_children": [{"__id__": 464}, {"__id__": 471}], "_active": true, "_components": [{"__id__": 479}, {"__id__": 469}, {"__id__": 480}], "_prefab": {"__id__": 481}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.523, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2eHgo8/bBKgI42/mJAmXnp"}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 512, "_parent": {"__id__": 463}, "_children": [{"__id__": 465}], "_active": true, "_components": [{"__id__": 468}, {"__id__": 476}, {"__id__": 477}], "_prefab": {"__id__": 478}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4a6zUDMnpMipFyKQxonX68"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 464}, "_children": [], "_active": true, "_components": [{"__id__": 466}], "_prefab": {"__id__": 467}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11, -31.25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d2REmliIRKe6swz3XLOp4L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 465}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f48ogmtz9NTrflFwWjQE9d"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "17i3b9OUpNt4H4QM3ZDVep", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 464}, "_enabled": true, "_scrollView": {"__id__": 469}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 466}, "_N$direction": 1, "_id": "8c14rt3MxAcooUq0ffPV/0"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 463}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 470}, "content": {"__id__": 470}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 468}, "_id": "89a0F7YRBH2pPDukamFyCl"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 471}, "_children": [], "_active": true, "_components": [{"__id__": 474}], "_prefab": {"__id__": 475}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 128, "g": 128, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 325, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0cQUXjccJDuoG7UgHhVI0J"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 463}, "_children": [{"__id__": 470}], "_active": true, "_components": [{"__id__": 472}], "_prefab": {"__id__": 473}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e0PNp1YtNPmZ26pXnQmOTP"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 471}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "efEWGSVzFOh5n590qtUgvq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "82P0+WowZHZJNje0dxboQy", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 470}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 20, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "5eoaFpb+5KVrF0SuKcMIwH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "4cBfOJ6lJDdrYiZ8HvCnKb", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 464}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": "b6IWd6KkdIaot7qFrp7RNR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 464}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5fe5dcaa-b513-4dc5-a166-573627b3a159"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "46sXj5bMBJToewvQeUEsef"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "72rkSqC7ZKWZZWzT+YfSKl", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 463}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "40CMAhksdGVpvARyy+9Dwh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 463}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "91/EeX8l5Ioapjc/GDMT/L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 463}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "14j58+s51KDo4ZYk/al0kf", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 355}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 45.37, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "1ctDzyNXBF9YBKGlWxwYtT"}, {"__type__": "7c689rALahHhbCICGRJluF6", "_name": "", "_objFlags": 0, "node": {"__id__": 355}, "_enabled": true, "scrollView": {"__id__": 469}, "texasListItemPrefab": {"__uuid__": "c24d52a7-dd7e-4b61-ba52-d2f4c709bada"}, "categoryButtons": [{"__id__": 357}, {"__id__": 368}, {"__id__": 379}, {"__id__": 390}, {"__id__": 401}], "blindLevelButtons": [{"__id__": 416}, {"__id__": 427}, {"__id__": 438}, {"__id__": 449}], "blindLevelButtonsRoot": {"__id__": 415}, "_id": "81mAet6ZpHHZ+z6g8xf61b"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "95WIq6NRVGlKk2ap6r2YrP", "sync": false}, {"__type__": "cc.Node", "_name": "LanguageSettingButton", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 486}], "_active": true, "_components": [{"__id__": 493}], "_prefab": {"__id__": 497}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [222.095, 413.962, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bXicKz3lNwYhULWm0shkb"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 485}, "_children": [{"__id__": 487}], "_active": true, "_components": [{"__id__": 490}, {"__id__": 491}], "_prefab": {"__id__": 492}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "faqe5a3uxGC4DwKGMO6HbN"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 486}, "_children": [], "_active": true, "_components": [{"__id__": 488}], "_prefab": {"__id__": 489}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "47caMUgwxDkLyigp/qPM1b"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 487}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "語言", "_N$string": "語言", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "92w3zRsr1A3Lg5T0QLZo+H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "87++rKoV9KkJe20Nl38SlN", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "761hxU3u5BGZsT47jgxzhS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 486}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "107M5aFNxNkY73jOsawKtF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a83UFg49pKDI2IQT70utbU", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 485}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 494}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 486}, "_id": "4225SLB6FHZac/FlQYWq2o"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 495}, "component": "", "_componentId": "244f9nlJENGzI4gsBJoi3Ke", "handler": "show", "customEventData": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 496}, "_name": "LanguageSetting", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16aqyZw5tK0a1mjlU5DnXp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 495}, "asset": {"__uuid__": "6444dfe7-2f71-48ab-ba30-93e16f32399e"}, "fileId": "27CZnL139DdZaO5JdeT4VQ", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "655gHORS5PbZFW8pxV0lGl", "sync": false}, {"__type__": "cc.Node", "_name": "RoomListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 499}], "_active": false, "_components": [{"__id__": 514}], "_prefab": {"__id__": 515}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -94.795, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "14pPxLnINE56nasZr18LTu"}, {"__type__": "cc.Node", "_name": "RoomListPanel", "_objFlags": 0, "_parent": {"__id__": 498}, "_children": [{"__id__": 500}], "_active": true, "_components": [{"__id__": 511}, {"__id__": 512}], "_prefab": {"__id__": 513}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -11.460000000000036, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aeu62OPWBP0pJMgLZaKA7c"}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 499}, "_children": [{"__id__": 501}], "_active": true, "_components": [{"__id__": 507}, {"__id__": 508}, {"__id__": 509}], "_prefab": {"__id__": 510}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.725999999999999, 90, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6dO8J+3aNL1pQars41pb4U"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 500}, "_children": [{"__id__": 502}], "_active": true, "_components": [{"__id__": 505}], "_prefab": {"__id__": 506}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cd8m5xNu5HL6Sr2PHFmszF"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 501}, "_children": [], "_active": true, "_components": [{"__id__": 503}], "_prefab": {"__id__": 504}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 115.30999755859375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b11i5IGx9IeoKVaqHADeZq"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 502}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 20, "_N$spacingX": 0, "_N$spacingY": 30, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "c1uoKk2/lMvbJ8unNkEAS0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 498}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "98Mdwzj5xPmZV09zws4eBr", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 501}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "e5vxXi1j5EQ7IXjopPZsVt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 498}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "d1zTjQQfROT7mkp8JSekXt", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 500}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "65+L64b9pH5phTtpFVFqpC"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 500}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 502}, "content": {"__id__": 502}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "e6Y0w7zD5Nn4tSto/8PwzN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 500}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -145.72599999999997, "_right": -134.27400000000003, "_top": 5.684341886080802e-14, "_bottom": 180, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "4arCBD/uVPPLWoftbsAkCj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 498}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "37MAvaLv5Jj4mT2dCoFARj", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 499}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "99WL139TxKQptALamRKrD6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 499}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 5, "_left": 810, "_right": 810, "_top": 11.460000000000047, "_bottom": -11.460000000000047, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "12rb+wsXVHaZGX3e+c0Les"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 498}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4clgvRfx1IhaHw3pFTmAni", "sync": false}, {"__type__": "62c118m6DBL1agLPbw0bMd1", "_name": "", "_objFlags": 0, "node": {"__id__": 498}, "_enabled": true, "scrollView": {"__id__": 508}, "roomListItemPrefab": {"__uuid__": "8ef5863e-b798-472a-970d-855d7bd95ea1"}, "_id": "c9YH+Y+yVKEL0dI6FR1DPp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 498}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4fFFA7Wk1L35JexYlf9XOt", "sync": false}, {"__type__": "cc.Node", "_name": "TexasListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 517}], "_active": false, "_components": [{"__id__": 532}], "_prefab": {"__id__": 533}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -94.795, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aeui1ew0xDvIi5IaGKBubU"}, {"__type__": "cc.Node", "_name": "RoomListPanel", "_objFlags": 0, "_parent": {"__id__": 516}, "_children": [{"__id__": 518}], "_active": true, "_components": [{"__id__": 529}, {"__id__": 530}], "_prefab": {"__id__": 531}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -11.460000000000036, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "55gJXWfUlBDLnY3L33Wdv8"}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 517}, "_children": [{"__id__": 519}], "_active": true, "_components": [{"__id__": 525}, {"__id__": 526}, {"__id__": 527}], "_prefab": {"__id__": 528}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.725999999999999, 90, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6cqSTO5OZPabiiXayx3EHO"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 518}, "_children": [{"__id__": 520}], "_active": true, "_components": [{"__id__": 523}], "_prefab": {"__id__": 524}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "561EViZhpDk6U0apf4EARG"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 519}, "_children": [], "_active": true, "_components": [{"__id__": 521}], "_prefab": {"__id__": 522}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 115.30999755859375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3dPOi3uC9MoaCVq8fsENcm"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 520}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 20, "_N$spacingX": 0, "_N$spacingY": 30, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "76jPDuAcFITKC9f9wK1/Pv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 516}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "98Mdwzj5xPmZV09zws4eBr", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 519}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "48+XvmlxxCJK2mWANBN73j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 516}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "d1zTjQQfROT7mkp8JSekXt", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 518}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dcH1GGQ4ZMIKp07b37+ljP"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 518}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 520}, "content": {"__id__": 520}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "2bWIPO4MpLQbfhtaSAqOtH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 518}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -145.72599999999997, "_right": -134.27400000000003, "_top": 5.684341886080802e-14, "_bottom": 180, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "cclqweyP5JG6kvt+jseHiu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 516}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "37MAvaLv5Jj4mT2dCoFARj", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 517}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "50YBWwo41JG76hHD0JzXPz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 517}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 5, "_left": 810, "_right": 810, "_top": 11.460000000000047, "_bottom": -11.460000000000047, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "60Ifa4rYdBtb+syAnEbctP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 516}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4clgvRfx1IhaHw3pFTmAni", "sync": false}, {"__type__": "7c689rALahHhbCICGRJluF6", "_name": "", "_objFlags": 0, "node": {"__id__": 516}, "_enabled": true, "scrollView": {"__id__": 526}, "texasListItemPrefab": {"__uuid__": "c24d52a7-dd7e-4b61-ba52-d2f4c709bada"}, "categoryButtons": [], "blindLevelButtons": [], "_id": "7c9VjXyDdP3pmrsukk/3DQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 516}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4aDh2dSRJIpb+YCL0zcsIN", "sync": false}, {"__type__": "cc.Node", "_name": "ShieldButton", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 535}], "_active": false, "_components": [{"__id__": 542}], "_prefab": {"__id__": 543}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6dhsefIA1F66hsOKPwoc3e"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 534}, "_children": [{"__id__": 536}], "_active": true, "_components": [{"__id__": 539}, {"__id__": 540}], "_prefab": {"__id__": 541}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cfLTHf/B9GwrrUmVzFgNZo"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 535}, "_children": [], "_active": true, "_components": [{"__id__": 537}], "_prefab": {"__id__": 538}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dflggibh9AsZzaMvsv1efc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 536}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "button", "_N$string": "button", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "43K8aadJ1K86rL1ZV3IOn0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "22P7mP6RVLMpHJJ/vn/GM4", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 535}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2dwpS1fRJCw6gRPAqofrE1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 535}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "a936XXw9xGC4R5mC6qvsRY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cd90IcfjRETpUEph0p7Cnb", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 534}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 535}, "_id": "93YJTtR/BGYKEeojX90tu+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "aa7hQeXkVI06vQ/efI+7Ws", "sync": false}, {"__type__": "c1f54m7435HtLfNeaijuCfD", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "roomListControl": {"__id__": 514}, "texasListControl": {"__id__": 483}, "texasGameList": {"__id__": 355}, "miniGameList": {"__id__": 102}, "texasGameTab": {"__id__": 86}, "miniGameTab": {"__id__": 97}, "_id": "f4xoF1A9FOGqkmE88ICSDW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "69qWsmS9FH653cy06iAAEv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "deWZ4EiVFFTZ3xYPreHAB+", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "edduYMEoxEz7IVP9PQmywe", "sync": false}, {"__type__": "fbfc110zNpLv68CPa39Whm6", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "isMockSocket": false, "isLoadFeatureTestBundle": false, "_id": "a5n5C3fv5GWoUKEczTd//r"}, {"__type__": "d4a47U2kGxJsr6bc/k01Ofa", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "loginPanel": {"__id__": 12}, "miniGamePanel": {"__id__": 72}, "_id": "39mNcPA0NIloyAU9V29oRl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "", "sync": false}]