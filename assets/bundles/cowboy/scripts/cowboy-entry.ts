import * as pf from '../../../poker-framework/scripts/pf';
import registerEntry = pf.registerEntry;
import * as network from './network/cowboy-network-index';
import { macros } from './common/cowboy-macros';
import { CowboyService } from './domain/cowboy-service';
import type { CowboyRoom } from './domain/cowboy-room';

@registerEntry(macros.BUNDLE_NAME)
export class CowboyEntry extends pf.BundleEntryBase {
    private _socket: pf.Nullable<pf.client.ISocket> = null;
    private _cowboyService: pf.Nullable<CowboyService> = null;
    private _room: pf.Nullable<CowboyRoom> = null;

    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_GAME;
    }

    protected getLanguageStringPath() {
        let path = macros.Language_String_Path.ZH_CN;
        switch (pf.languageManager.currentLanguage) {
            case pf.LANGUAGE_GROUPS.en_US:
                path = macros.Language_String_Path.EN_US;
                break;
            case pf.LANGUAGE_GROUPS.yn_TH:
                path = macros.Language_String_Path.YN_TH;
                break;
            case pf.LANGUAGE_GROUPS.th_PH:
                path = macros.Language_String_Path.TH_PH;
                break;
            case pf.LANGUAGE_GROUPS.hi_IN:
                path = macros.Language_String_Path.HI_IN;
                break;
        }
        return path;
    }

    protected getAddressableConfigPath() {
        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
            ? macros.Addressable_Config_Path.ZH_CN
            : macros.Addressable_Config_Path.EN_US;
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onEnter`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        context.bundle = macros.BUNDLE_NAME;

        this._socket = context?.socket;

        if (!this._socket) {
            return Promise.reject(new pf.InvalidParameterError('options.socket is null or undefined!'));
        }

        if (options.roomId === undefined) {
            return Promise.reject(new pf.InvalidParameterError('options.roomId is undefined!'));
        }

        await this.loadConfigs();

        // const cowboySession = this._socket.createGameSession(network.CowboySession);

        // this._cowboyService = new CowboyService(cowboySession);
        // pf.serviceManager.register(this._cowboyService);

        // // store mini game state to game context
        // context.gameId = pf.client.GameId.CowBoy;
        // context.session = cowboySession;
        // context.service = this._cowboyService;

        // cc.log(`login ${macros.BUNDLE_NAME}`);
        // await this._cowboyService.login();

        // this._room = (await this._cowboyService.joinRoom(options.roomId)) as CowboyRoom;
        // cc.log(`${macros.BUNDLE_NAME} join room`);
        // context.room = this._room;
        // context.roomId = this._room.id;

        // download assets
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('cowboy');

        // NOTE:
        // make address progress not over 20%
        let addressalbeTotalCount = 0;

        await assetLoader.start((finish, total) => {
            if (addressalbeTotalCount === 0) {
                addressalbeTotalCount = total * 5;
            }
            options?.onProgress(finish, addressalbeTotalCount);
        });

        const asyncOp = new pf.AsyncOperation<void>();

        cc.log('load scene CowboyScene');
        this.bundle.loadScene(
            'CowboyScene',
            (finish, total) => {
                options?.onProgress(
                    finish + assetLoader.totalCount,
                    Math.max(total + assetLoader.totalCount, addressalbeTotalCount)
                );
            },
            (err, scene) => {
                if (err) {
                    cc.warn(err);
                    asyncOp.reject(err);
                } else {
                    cc.log('run scene CowboyScene');
                    this.loginGame(context, options.roomId)
                        .then(() => {
                            cc.director.runScene(
                                scene,
                                () => {
                                    if (this.onBeforeLoadScene) {
                                        this.onBeforeLoadScene();
                                    }
                                },
                                () => {
                                    asyncOp.resolve();
                                }
                            );
                        })
                        .catch((err) => {
                            asyncOp.reject(err);
                        });
                }
            }
        );

        return asyncOp.promise;
    }

    protected async onPreload(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload`);
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('cowboy-dynamic');
        await assetLoader.startPreload();
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload done`);
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onExit`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        // moved to CowboyControl::tryLeaveRoom()
        // try {
        //     await this._room.leaveRoom();
        // } catch (err) {
        //     cc.warn(err);
        // }
        pf.serviceManager.unregister(CowboyService);

        this._room = null;
        this._cowboyService = null;

        this._socket?.removeGameSession(network.PKWCowboySession);
        this._socket = null;

        context.bundle = '';
        context.session = null;
        context.service = null;
        context.room = null;
        context.exitCallback = null;
    }

    onUnload(): void {
        cc.log(`bundle ${macros.BUNDLE_NAME} onUnload`);
    }

    protected async loginGame(context: pf.services.MiniGameContext, roomId: number): Promise<void> {
        try {
            // for identifying which game the user is trying to play if errors happen
            context.gameId = pf.client.GameId.CowBoy;

            cc.log('create pkw cowboy session');
            const cowboySession = this._socket.createGameSession(network.PKWCowboySession, pf.client.GameId.CowBoy);

            this._cowboyService = new CowboyService(cowboySession);
            pf.serviceManager.register(this._cowboyService);

            cc.log(`login ${macros.BUNDLE_NAME}`);
            await this._cowboyService.login();

            cc.log(`${macros.BUNDLE_NAME} join room`);
            this._room = (await this._cowboyService.joinRoom(roomId)) as CowboyRoom;

            // store mini game state to game context
            context.bundle = macros.BUNDLE_NAME;
            // context.gameId = pf.client.GameId.CowBoy;
            context.session = cowboySession;
            context.service = this._cowboyService;
            context.room = this._room;
            context.roomId = this._room.id;
        } catch (err) {
            // NOTE:
            // Do not throw error to prevent exit bundle when login fail.
            // Let GameSession re join room when socket reconnect
            cc.warn(`login ${macros.BUNDLE_NAME} failed: ${(err as Error).message})`);

            if (this._cowboyService) {
                pf.serviceManager.unregister(CowboyService);
                this._socket.removeGameSession(network.PKWCowboySession);
                this._cowboyService = null;
            }

            throw err;
        }
    }
}
