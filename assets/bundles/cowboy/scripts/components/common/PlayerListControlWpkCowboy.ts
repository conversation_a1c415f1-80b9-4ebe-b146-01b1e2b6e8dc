/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/cowboy-macros';
import * as cr from '../../../../common-resource/scripts/common-resource';
import * as common from 'common';

// import AvatarControl = cr.components.AvatarControl;
import ScrollViewItemControl = common.components.ScrollViewItemControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;

const { ccclass, property } = cc._decorator;

@ccclass
export class PlayerListControlWpkCowboy extends MiniGamePlayerListControl {
    // 1
    // @property(cc.Label)
    // online_desc: cc.Label = null;
    // @property(cc.Label)
    // online_num: cc.Label = null;
    // @property(cc.Prefab)
    // item_prefab: cc.Prefab = null;

    // // 2
    // @property(cc.Label)
    // title_txt: cc.Label = null;
    // @property(cc.Prefab)
    // item_prefab2: cc.Prefab = null;
    // @property(cc.ScrollView)
    // scrollView: cc.ScrollView = null;
    // @property(cc.ScrollView)
    // scrollView2: cc.ScrollView = null;
    // @property(cc.Node)
    // chartbg: cc.Node = null;

    // @property(cc.Sprite)
    // arrow_img: cc.Sprite = null;
    // @property(cc.Sprite)
    // title_bg: cc.Sprite = null;
    // @property(cc.Sprite)
    // img2: cc.Sprite = null;

    // @property(cc.Node)
    // panel1: cc.Node = null;
    // @property(cc.Node)
    // panel2: cc.Node = null;
    // @property(cc.Node)
    // panel3: cc.Node = null;
    // @property(cc.Node)
    // layout0: cc.Node = null;
    // @property(cc.Node)
    // layout1: cc.Node = null;

    // @property(cc.Button)
    // arrow_btn: cc.Button = null;
    // @property(cc.Button)
    // page1_btn: cc.Button = null;
    // @property(cc.Button)
    // page2_btn: cc.Button = null;
    // @property(cc.Button)
    // close_btn: cc.Button = null;
    // @property(cc.Node)
    // head_img: cc.Node = null;
    // @property(AvatarControl)
    // avatarControl: AvatarControl = null;

    // curIdx: number = 0;
    // curTag: number = 1;
    // _name_list: cc.Node[] = [];
    // _gou_list: cc.Node[] = [];
    // _btn_list: cc.Node[] = [];
    // once: boolean = true;

    // @property(cc.SpriteAtlas) game_dznz_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) atlasChart: cc.SpriteAtlas = null;
    @property(cc.SpriteAtlas) atlasPlayerList: cc.SpriteAtlas = null;

    // private _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集
    private _atlasChart: cc.SpriteAtlas = null;

    // private _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;
    // private _authService: pf.Nullable<pf.services.AuthService> = null;
    // private _rankService: pf.Nullable<pf.services.RankService> = null;
    // private _rankInfo: pf.Nullable<pf.services.RankInfo> = null;

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room;

        this._authService = pf.serviceManager.get(pf.services.AuthService);
        this._rankService = pf.serviceManager.get(pf.services.RankService);

        // if (context.gameId === pf.client.GameId.CowBoy) {
        //     let Image_2 = cc.find('panel1/Image_2', this.layout1);
        //     Image_2.setContentSize(Image_2.width, 348);
        //     let panel2 = cc.find('panel2', this.layout1);
        //     panel2.setContentSize(panel2.width, 346);
        //     let cell1 = cc.find('cell1', panel2);
        //     let cell2 = cc.find('cell2', panel2);
        //     let cell3 = cc.find('cell3', panel2);
        //     let cell4 = cc.find('cell4', panel2);
        //     let cell5 = cc.find('cell5', panel2);
        //     cell1.active = false;
        //     cell2.setPosition(cell2.x, -186);
        //     cell3.active = false;
        //     cell4.setPosition(cell4.x, -266);
        //     cell5.setPosition(cell5.x, -346);
        // }
        // this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_LANGUAGE_ATLAS);
        this.game_dznz_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.DZNZ_ATLAS);
        this._atlasChart = pf.addressableAssetManager.getAsset(macros.Assets.CHART_ATLAS);

        this.panel2.active = false;
        this.panel1.zIndex = 2;
        this.panel2.zIndex = 3;
        this.panel3.zIndex = 1;

        cc.find('online_txt', this.layout0).getComponent(cc.Label).string =
            pf.languageManager.getString('Humanboy_list_online_zh_cn');

        // zh_CN only in wpk cowboy
        // if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
        //     cc.find('onlinenum_txt', this.layout0).setPosition(
        //         cc.find('online_txt', this.layout0).getPosition().x +
        //             cc.find('online_txt', this.layout0).getContentSize().width +
        //             7,
        //         cc.find('online_txt', this.layout0).getPosition().y
        //     );
        // }

        this.node.getChildByName('zhezhao_panel').on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                this.hideMenuPanel();
                event.stopPropagation();
            },
            this
        );

        this.scrollView2.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                this.hideMenuPanel();
                event.stopPropagation();
            },
            this
        );

        this.panel3.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                this.hideMenuPanel();
                event.stopPropagation();
            },
            this
        );

        // zh_CN only in wpk cowboy
        // let title_change = cc.find('Image_1', this.panel1).getChildByName('title_change');
        // title_change.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_change_rank');

        for (let i = 0; i < 6; i++) {
            let cell = cc.find('cell' + i, this.panel2);
            let name = cc.find('name_txt', cell);
            // name.color = cc.color(255, 255, 255);
            name.getComponent(cc.Label).node.color = cc.color(187, 115, 51);
            name.getComponent(cc.LabelOutline).color = cc.color(77, 46, 28);

            let gou = cc.find('gou_img', cell);
            gou.active = false;

            if (i === 1 || i === 3) {
                cell.active = false;
            }

            let btn = cc.find('Button_' + i, cell);
            btn.name = i.toString();
            btn.on('click', (): void => {
                pf.audioManager.playSoundEffect(macros.Audio.TAB);
                this.displayCell(parseInt(btn.name, 10));

                if (this.arrow_img.node.scaleY === -1) {
                    this.arrow_img.node.scaleY = 1;
                    this.img2.node.active = false;
                    this.panel2.active = false;
                } else {
                    this.arrow_img.node.scaleY = -1;
                    this.img2.node.active = true;
                    this.panel2.active = true;
                }
            });

            this._name_list.push(name);
            this._gou_list.push(gou);
            // this._btn_list.push(btn);
        }

        this.page1_btn.node.on('click', (): void => {
            pf.audioManager.playSoundEffect(macros.Audio.TAB);
            this.page1_btn.enabled = false;
            this.page2_btn.enabled = true;

            this.layout0.active = true;
            this.layout1.active = false;
            this.panel1.active = false;
            this.panel2.active = false;
            this.title_txt.node.active = false;

            // this.title_bg.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_title_player');
            this.page1_btn.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('btnLight');
            this.page2_btn.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('btnDark');

            this.page1_btn.node.getChildByName('txtSp').getComponent(cc.Sprite).spriteFrame =
                this.atlasPlayerList.getSpriteFrame('txtPlayerlistLight');
            this.page2_btn.node.getChildByName('txtSp').getComponent(cc.Sprite).spriteFrame =
                this.atlasPlayerList.getSpriteFrame('txtRongyaoDark');

            this.scrollView.scrollToTop();
        });

        this.page2_btn.node.on('click', (): void => {
            pf.audioManager.playSoundEffect(macros.Audio.TAB);
            this.page1_btn.enabled = true;
            this.page2_btn.enabled = false;

            this.layout0.active = false;
            this.layout1.active = true;
            this.panel1.active = true;
            this.img2.node.active = false;
            this.title_txt.node.active = true;
            this.arrow_img.node.scaleY = 1;

            // this.title_bg.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_title_honor');
            // cv.resMgr.loadSpriteTextureByPlist(this._atlas_hb_language, this.title_bg, 'humanboy_title_honor');
            this.page1_btn.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('btnDark');
            this.page2_btn.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('btnLight');

            this.page1_btn.node.getChildByName('txtSp').getComponent(cc.Sprite).spriteFrame =
                this.atlasPlayerList.getSpriteFrame('txtPlayerlistDark');
            this.page2_btn.node.getChildByName('txtSp').getComponent(cc.Sprite).spriteFrame =
                this.atlasPlayerList.getSpriteFrame('txtRongyaoLight');

            let sv: ScrollViewItemControl = this.scrollView2.getComponent(ScrollViewItemControl);
            if (this.once) {
                this.once = false;
                sv.bindPrefab(this.item_prefab2, 'HumanboyHonorItemControl', []);
                sv.generateItemPool();
                sv.bindScrollEventTarget(this);
            }

            sv.name = this.curIdx.toString();

            if (this._rankInfo?.list.length > 0) {
                sv.reloadView(this._rankInfo.list);
            }
        });

        this.arrow_btn.node.on('click', (): void => {
            pf.audioManager.playSoundEffect(macros.Audio.TAB);
            if (this.arrow_img.node.scaleY === -1) {
                this.arrow_img.node.scaleY = 1;
                this.img2.node.active = false;
                this.panel2.active = false;
            } else {
                this.arrow_img.node.scaleY = -1;
                this.img2.node.active = true;
                this.panel2.active = true;
            }
        });

        // this.title_bg.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_title_player');

        this.layout0.active = true;
        this.layout1.active = false;

        this.close_btn.node.on('click', (): void => {
            pf.audioManager.playSoundEffect(macros.Audio.CLOSE);
            this.node.active = false;
        });

        this._name_list[0].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_0');
        this._name_list[1].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_1');
        this._name_list[2].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_2');
        this._name_list[3].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_3');
        this._name_list[4].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_4');
        this._name_list[5].getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_rank_zh_cn_5');
    }

    onDisplayRank(rankInfo: pf.services.RankInfo) {
        if (this.layout1.active) {
            let sv: ScrollViewItemControl = this.scrollView2.getComponent(ScrollViewItemControl);
            sv.name = this.curIdx.toString();
            sv.reloadView(rankInfo.list);
        }

        if (rankInfo.owner.uid !== 0) {
            this.showSelf(rankInfo);
        } else {
            let name_text = cc.find('name_text', this.panel3);
            name_text.getComponent(cc.Label).string = this._authService.currentUser.nickname;
            let day_text = cc.find('day_text', this.panel3);
            let des_text = cc.find('des_text', this.panel3);
            let profit_text = cc.find('profit_text', this.panel3);
            // let head_img = cc.find("head_img", this.panel3);
            let myrank = cc.find('rank_num_text', this.panel3);
            myrank.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_myrank_zh_cn');
            // day_text.active = false;
            // this.head_img.destroyAllChildren();
            // this.head_img.removeAllChildren(true);

            if (rankInfo.owner.uid === this._miniGameRoom.selfPlayer.uid) {
                this.avatarControl.head.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('self_head_default_2');
            } else {
                this.avatarControl.head.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('other_head_default');
            }

            if (this.curIdx === 1 || this.curIdx === 3) {
                des_text.getComponent(cc.Label).string = pf.languageManager.getString(
                    'Humanboy_list_frequency_time_zh_cn'
                );
                profit_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                    pf.languageManager.getString('Humanboy_list_frequency_zh_cn'),
                    rankInfo.owner.frequency
                );
            } else {
                des_text.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_profit_zh_cn');
                profit_text.getComponent(cc.Label).string = cr.CurrencyUtil.clientAmountToDisplayString(
                    cr.CurrencyUtil.convertToClientAmount(rankInfo.owner.profit)
                );
            }
        }
    }

    onFinishReqData() {
        if (this.curIdx >= 0 && this.curIdx < 6) {
            let id = 0;
            if (this.curTag === 1) {
                id = 300001 + this.curIdx;
            } else if (this.curTag === 0) {
                id = 100001 + this.curIdx;
            } else if (this.curTag === 2) {
                id = 500001 + this.curIdx;
            }

            if (id > 0) {
                // TODO: Optimize rank data display performance
                cc.log('get rank');
                this._rankService.getRank(id).then((rankInfo) => {
                    this.onDisplayRank(rankInfo);

                    this._rankInfo = rankInfo;
                });
            }
        }
    }

    showSelf(rankInfo: pf.services.RankInfo) {
        let myrank = cc.find('rank_num_text', this.panel3);
        myrank.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_list_myrank_zh_cn');

        if (rankInfo.owner.rank === -1) {
            cc.find('selfrank_text', this.panel3).getComponent(cc.Label).string = '1000+';
            // cc.find('selfrank_text', this.panel3).getComponent(cc.Label).fontSize = 42;
        } else {
            // let count = 1;
            // let n = rankInfo.owner.rank;

            // while (n >= 10) {
            //     count = count + 1;
            //     n = n / 10;
            // }

            // if (count === 1) {
            //     cc.find('selfrank_text', this.panel3).getComponent(cc.Label).fontSize = 56;
            // } else if (count === 2) {
            //     cc.find('selfrank_text', this.panel3).getComponent(cc.Label).fontSize = 48;
            // } else if (count === 3) {
            //     cc.find('selfrank_text', this.panel3).getComponent(cc.Label).fontSize = 42;
            // } else if (count === 4) {
            //     cc.find('selfrank_text', this.panel3).getComponent(cc.Label).fontSize = 36;
            // }

            cc.find('selfrank_text', this.panel3).getComponent(cc.Label).string = rankInfo.owner.rank.toString();
        }

        if (this.curIdx === 1 || this.curIdx === 3) {
            cc.find('des_text', this.panel3).getComponent(cc.Label).string = pf.languageManager.getString(
                'Humanboy_list_frequency_time_zh_cn'
            );
            cc.find('profit_text', this.panel3).getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_list_frequency_zh_cn'),
                rankInfo.owner.frequency
            );
        } else {
            cc.find('des_text', this.panel3).getComponent(cc.Label).string =
                pf.languageManager.getString('Humanboy_list_profit_zh_cn');
            cc.find('profit_text', this.panel3).getComponent(cc.Label).string =
                cr.CurrencyUtil.clientAmountToDisplayString(
                    cr.CurrencyUtil.convertToClientAmount(rankInfo.owner.profit)
                );
        }

        cc.find('name_text', this.panel3).getComponent(cc.Label).string = rankInfo.owner.name;
        cc.find('money_text', this.panel3).getComponent(cc.Label).string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(rankInfo.owner.coin)
        );

        let t = rankInfo.owner.updateAt;
        if (t === 0) {
            cc.find('day_text', this.panel3).active = false;
        } else {
            cc.find('day_text', this.panel3).active = true;
            cc.find('day_text', this.panel3).getComponent(cc.Label).string = pf.TimeUtil.formatTime(
                rankInfo.owner.updateAt,
                pf.eTimeType.Year_Month_Day
            );
        }

        this.avatarControl.loadHeadImage(this._miniGameRoom.selfPlayer.head, this._miniGameRoom.selfPlayer.plat);
    }

    onBtnClose() {
        this.node.active = false;
    }

    displayCell(idx: number) {
        if (idx >= 0) {
            this.curIdx = idx;
        } else {
            const action = cc.flipY(false);
            this.arrow_img.node.runAction(action);
            cc.find('Image_2', this.panel1).active = false;
            this.panel2.active = false;
        }

        for (let i = 0; i < 6; i++) {
            let cell = cc.find('cell' + i, this.panel2);

            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            if (context.gameId === pf.client.GameId.CowBoy) {
                if (i === 1 || i === 3) {
                    cell.active = false;
                    continue;
                }
            }
            cell.active = true;
            // let name = cc.find('name_txt', cell);
            // name.getComponent(cc.Label).node.color = cc.color(255, 255, 255);

            // let gou = cc.find('gou_img', cell);
            // gou.active = false;
            const name = cc.find('name_txt', cell);
            name.getComponent(cc.Label).node.color = cc.color(187, 115, 51);
            name.getComponent(cc.LabelOutline).color = cc.color(77, 46, 28);
            const gou = cc.find('gou_img', cell);
            const choice = cc.find('choice', cell);
            choice.active = false;
            gou.active = false;
        }

        this.title_txt.string = pf.languageManager.getString(
            pf.StringUtil.formatC('Humanboy_list_rank_zh_cn_%d', this.curIdx)
        );
        // this._name_list[this.curIdx].color = cc.color(237, 211, 119);
        // this._gou_list[this.curIdx].active = true;
        this._name_list[this.curIdx].color = cc.color(255, 244, 208);
        this._name_list[this.curIdx].getComponent(cc.LabelOutline).color = cc.color(102, 65, 42);
        this._gou_list[this.curIdx].active = true;
        this._name_list[this.curIdx].parent.getChildByName('choice').active = true;

        this.onFinishReqData();
    }

    setCowboyData(gamePlayers: pf.services.GamePlayer[], playerNum: number) {
        this.curTag = 0;

        if (gamePlayers.length === 0) {
            this.online_desc.node.active = false;
            this.online_num.node.active = false;
        } else {
            this.online_desc.node.active = true;
            this.online_num.node.active = true;
            this.online_num.string = pf.StringUtil.formatC('%d', playerNum);
        }

        let sv: ScrollViewItemControl = this.scrollView.getComponent(ScrollViewItemControl);
        sv.bindPrefab(this.item_prefab, 'CowboyItemControl', []);
        sv.generateItemPool();
        sv.bindScrollEventTarget(this);
        sv.reloadView(gamePlayers);
    }

    setHumanboyData(gamePlayers: pf.services.GamePlayer[], playerNum: number) {
        // this.curTag = 1;
        // if (gamePlayers.length === 0) {
        //     this.online_desc.node.active = false;
        //     this.online_num.node.active = false;
        // } else {
        //     this.online_desc.node.active = true;
        //     this.online_num.node.active = true;
        //     this.online_num.string = pf.StringUtil.formatC('%d', playerNum);
        // }
        // let sv: ScrollViewItemControl = this.scrollView.getComponent(ScrollViewItemControl);
        // sv.bindPrefab(this.item_prefab, 'CowboyItemControl', []);
        // sv.generateItemPool();
        // sv.bindScrollEventTarget(this);
        // sv.reloadView(gamePlayers);
    }

    setVideoCowboyData(gamePlayers: pf.services.GamePlayer[], playerNum: number) {
        // this.curTag = 2;
        // if (gamePlayers.length === 0) {
        //     this.online_desc.node.active = false;
        //     this.online_num.node.active = false;
        // } else {
        //     this.online_desc.node.active = true;
        //     this.online_num.node.active = true;
        //     this.online_num.string = pf.StringUtil.formatC('%d', playerNum);
        // }
        // let sv: ScrollViewItemControl = this.scrollView.getComponent(ScrollViewItemControl);
        // sv.bindPrefab(this.item_prefab, 'CowboyItemControl', []);
        // sv.generateItemPool();
        // sv.bindScrollEventTarget(this);
        // sv.reloadView(gamePlayers);
    }

    setPokerMasterData(gamePlayers: pf.services.GamePlayer[], playerNum: number) {
        // this.curTag = 3;
        // if (gamePlayers.length === 0) {
        //     this.online_desc.node.active = false;
        //     this.online_num.node.active = false;
        // } else {
        //     this.online_desc.node.active = true;
        //     this.online_num.node.active = true;
        //     this.online_num.string = pf.StringUtil.formatC('%d', gamePlayers);
        // }
        // let sv: ScrollViewItemControl = this.scrollView.getComponent(ScrollViewItemControl);
        // sv.bindPrefab(this.item_prefab, 'CowboyItemControl', []);
        // sv.generateItemPool();
        // sv.bindScrollEventTarget(this);
        // sv.reloadView(gamePlayers);
    }

    private hideMenuPanel() {
        this.arrow_img.node.scaleY = 1;
        this.img2.node.active = false;
        this.panel2.active = false;
    }
}
