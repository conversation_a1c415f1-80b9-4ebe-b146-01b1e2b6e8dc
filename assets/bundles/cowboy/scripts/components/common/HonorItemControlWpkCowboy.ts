/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
// import { macros } from '../../common/common-resource-macros';
import * as cr from '../../../../common-resource/scripts/common-resource';
import * as common from 'common';
// import AvatarControl = cr.components.AvatarControl;
import ScrollViewItemControl = common.components.ScrollViewItemControl;
import HumanboyHonorItemControl = cr.components.HumanboyHonorItemControl;

const { ccclass, property } = cc._decorator;

@ccclass
export default class HonorItemControlWpkCowboy extends HumanboyHonorItemControl {
    // @property(cc.Node) head_img: cc.Node = null;
    // @property(AvatarControl) avatarControl: AvatarControl = null;
    // @property(cc.Sprite) rank_img: cc.Sprite = null;
    // @property(cc.Sprite) gold_img: cc.Sprite = null;
    // @property(cc.Label) name_text = null;
    // @property(cc.Label) money_text = null;
    // @property(cc.Label) profit_text = null;
    // @property(cc.Label) rank_text = null;
    // @property(cc.Label) des_text = null;
    // @property(cc.Label) day_text = null;

    // @property(cc.SpriteAtlas) chart_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) game_dznz_PLIST: cc.SpriteAtlas = null;
    @property(cc.SpriteAtlas) atlasPlayerList: cc.SpriteAtlas = null;

    // private _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;

    // protected onLoad(): void {
    //     const context = pf.app.getGameContext<pf.services.MiniGameContext>();
    //     this._miniGameRoom = context.room;
    // }

    updateSVReuseData(index: number, dataArray: any[], view: ScrollViewItemControl): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;
        let data = dataArray[index];
        if (!(data instanceof pf.services.RankData)) return;
        // if (!(data instanceof RankData)) return;

        // this.msg = data;

        if (index === 0) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.atlasPlayerList.getSpriteFrame('cup1');
        } else if (index === 1) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.atlasPlayerList.getSpriteFrame('cup2');
        } else if (index === 2) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.atlasPlayerList.getSpriteFrame('cup3');
        } else {
            this.rank_img.node.active = false;
            this.rank_text.node.active = true;
            this.rank_text.string = cr.CurrencyUtil.clientAmountToDisplayString(index + 1);
        }

        this.name_text.string = data.name;
        this.money_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(data.coin)
        );

        // let xx = parseInt(view.name, macros.RADIX_DECIMAL);
        let xx = parseInt(view.name, 10);
        // this.des_text.node.opacity = 153;
        // this.des_text.node.color = cc.Color.WHITE;

        if (xx === 1 || xx === 3) {
            this.des_text.string = pf.languageManager.getString('Humanboy_list_frequency_time_zh_cn');
            this.profit_text.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_list_frequency_zh_cn'),
                data.frequency
            );
        } else {
            this.des_text.string = pf.languageManager.getString('Humanboy_list_profit_zh_cn');
            this.profit_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
                cr.CurrencyUtil.convertToClientAmount(data.profit)
            );
        }

        this.day_text.string = pf.TimeUtil.formatTime(data.updateAt, pf.eTimeType.Year_Month_Day);

        // this.day_text.node.opacity = 153;
        // this.day_text.node.color = cc.Color.WHITE;

        const playerInfo = data.uid === this._miniGameRoom.selfPlayer.uid ? this._miniGameRoom.selfPlayer : data;
        const headPath = cr.CommonUtil.getHeadPath(playerInfo, this._miniGameRoom.selfPlayer.uid);
        this.avatarControl.loadHeadImage(headPath, data.plat);
    }
}
