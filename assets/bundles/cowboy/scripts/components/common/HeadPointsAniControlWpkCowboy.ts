/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import * as common from 'common';
import HeadPointsAniControl = common.components.HeadPointsAniControl;
const { ccclass, property } = cc._decorator;

@ccclass
export class HeadPointsAniControlWpkCowboy extends HeadPointsAniControl {
    playPointAni(points_num: number, time?: number) {
        let lab = cc.find('UI1/lab', this.node);
        // eslint-disable-next-line @typescript-eslint/no-inferrable-types
        let isK: boolean = false;
        if (points_num >= 1000000) {
            points_num = points_num * 0.001;
            isK = true;
        }

        let result = common.CurrencyUtil.convertServerAmountToDisplayString(points_num);

        if (isK) {
            const pos = result.indexOf('.');

            if (pos > 0) {
                result = result.slice(0, pos);
            }
        }

        lab.getComponent(cc.Label).string =
            pf.languageManager.getString('Head_Points_Text_zh_cn') + '+' + result + (isK ? 'K' : '');

        let ani = this.node.getComponent(cc.Animation);
        this.node.active = true;
        if (ani) {
            ani.stop();
            if (time && time !== -1 && time < ani.defaultClip.duration) {
                if (time === 0) {
                    time = 0.3;
                }
                this.gotoFrameAndPlay(ani, time);
            } else {
                ani.play();
            }
        }
    }

    // resetPointAni() {
    //     let ani = this.node.getComponent(cc.Animation);
    //     if (ani) {
    //         ani.stop();
    //         this.node.active = false;
    //     }
    // }

    // gotoFrameAndPlay(ani: cc.Animation, playTime: number) {
    //     ani.play(ani.defaultClip.name, ani.defaultClip.duration - playTime);
    // }
}
