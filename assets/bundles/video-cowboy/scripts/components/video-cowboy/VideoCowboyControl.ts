/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import Wallet = pf.services.Wallet;
import Deque = pf.Deque;
import PlayerOneBet = pf.services.PlayerOneBet;
import { macros } from '../../common/video-cowboy-macros';
import * as domain from '../../domain/video-cowboy-domain-index';
import * as network from '../../network/video-cowboy-network-index';

import * as cr from '../../../../common-resource/scripts/common-resource';
import * as common from 'common';

import LuckTurntableButtonControl = common.components.LuckTurntableButtonControl;
import MiniGameCommonDef = cr.MiniGameCommonDef;
import MiniGameMenuControl = cr.components.MiniGameMenuControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;
import MiniGameAdvancedSettingControl = cr.components.MiniGameAdvancedSettingControl;
import HumanboyDialogControl = cr.components.HumanboyDialogControl;
import HeadPointsAniControl = common.components.HeadPointsAniControl;
import IMiniGameDialog = cr.components.IMiniGameDialog;
import IMiniGameDialogConfig = cr.components.IMiniGameDialogConfig;
import MiniGameDialog = cr.components.MiniGameDialog;
import ConcreteMiniGameDialog = cr.components.ConcreteMiniGameDialog;
import ConsumingPromptControl = cr.components.ConsumingPromptControl;
import MiniGameExchangeControl = cr.components.MiniGameExchangeControl;
import MiniGameRuleControl = cr.components.MiniGameRuleControl;
import MiniGameAudioSettingControl = cr.components.MiniGameAudioSettingControl;
import PushNoticeData = pf.services.PushNoticeData;
import PushNoticeType = pf.services.PushNoticeType;
import HumanboyAdvancedAutoControl = cr.components.HumanboyAdvancedAutoControl;
import ConcreteAdvancedAuto = cr.components.ConcreteAdvancedAuto;
import MiniGameAdvancedAuto = cr.components.MiniGameAdvancedAuto;
import PokerCardControl = cr.components.MiniGamePokerCardControl;
import AvatarControl = common.components.AvatarControl;
import type { PlayerSettle } from '../../domain/video-cowboy-player-settle';
import VideoCowboyOpenCardControl from './VideoCowboyOpenCardControl';
import { VideoCowboyChartControl } from './VideoCowboyChartControl';
import LiveVideoComponent from './liveVideo/LiveVideoComponent';
import type { AppEvents } from 'main';

// 牛仔路子结构信息
export class CowboyWayOutInfo {
    iAreaIdx: number = -1; // 区域索引
    iWayOutLoseLimitCount: number = 0; // 路单描述文本"xxx局"未出上限(超过上限显示: "xxx+ 局未出", 默认0表示无上限)
    panelWayOut: cc.Node = null; // 路单层
    rtxtWayOut: cc.RichText = null; // 路子描述文本
    vWayOutImg: cc.Node[] = []; // 路子精灵数组
    vWayOutImgSrcPos: cc.Vec2[] = []; // 路子精灵原始位置数组
    eWayOutStyle: number = MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE; // 路子显示风格
}

export class OtherPlayerHead {
    uid: number = 0;
    bg: cc.Sprite = null;
    textCoin: cc.Label = null;
    nbFlag: cc.Node = null; // 富豪/神算子
    avatarControl: AvatarControl = null;
}

/* n个点围成的凸多边型数据 */
export class BetAreaLineInfo {
    minX: number = 0; // x最小值
    minY: number = 0; // y最小值
    maxX: number = 0; // x最大值
    maxY: number = 0; // y最大值
    /* 二一次方程y= ax+b, aArr为斜率数组，bArr为x=0时对应的y值数组 */
    aArr: number[] = [];
    bArr: number[] = [];
    x1: number[] = [];
    x2: number[] = [];
}

export class EffectLoop {
    audioId: number = 0;
    duringTime: number = 0;
    startPlayTime: number = 0;
    bGoOn: boolean = false;
    func: Function = null;
}

// TODO cowboy 也有 COWBOY_LOCAL_ZORDER，要拉成一个公共的
export enum COWBOY_LOCAL_ZORDER {
    COWBOY_LOCAL_ZORDER_DUMMY = 0,
    COWBOY_LOCAL_ZORDER_IMG_HEAD,
    COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT,
    COWBOY_LOCAL_ZORDER_COIN_NODE,
    COWBOY_LOCAL_ZORDER_ANIM_NODE,
    COWBOY_LOCAL_ZORDER_TIMELINE_NODE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT, // 高级续投选择面板
    COWBOY_LOCAL_ZORDER_REWRAD_TIP, // 中奖提示面板
    COWBOY_LOCAL_ZORDER_RED_PACKAGE, // 红包面板
    COWBOY_LOCAL_ZORDER_MENU_PANEL = 99,
    COWBOY_LOCAL_ZORDER_TOAST,
    COWBOY_LOCAL_ZORDER_GUIDE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class VideoCowboyControl extends cc.Component {
    @property(cc.Prefab) prefabOpenCard: cc.Prefab = null;
    @property(cc.Prefab) clock_prefab: cc.Prefab = null;
    // @property(cc.Font) time_xiazhu: cc.Font = null;
    // @property(cc.Font) time_xiazhu_1: cc.Font = null;
    private _openCardLayer: VideoCowboyOpenCardControl = null;
    private _btn_xianlu: cc.Node = null;
    private _statusLayerPosNode: cc.Node = null;
    private _videoStatusTips: cc.Node = null;
    private _betLineNode: cc.Node[] = [];
    private lineInfoArr: BetAreaLineInfo[][] = [];
    private _clock_node: cc.Node = null;
    private _clock_num_txt: cc.Label = null;
    private _clock_green: cc.Sprite = null;
    private _clock_circle: cc.ProgressBar = null;
    private _bTrueFullScreen: boolean = false;

    private _kaiju_Sprite: cc.Sprite = null;
    private _stopXz_Sprite: cc.Sprite = null;
    private _openCard_Sprite: cc.Sprite = null;
    private _jieSuan_Sprite: cc.Sprite = null;
    private _openCard_blink: cc.Sprite = null;
    private _jieSuan_blink: cc.Sprite = null;
    private _clock_total_time: number = 0.0;
    private _clock_canShow: boolean = false;
    private _clock_canChange: boolean = false;
    private _rightChartPanel: cc.Node = null;
    private _chartBg: cc.Node = null;

    // 抽中红包动画
    // @property(cc.Prefab) redPacket_prefab: cc.Prefab = null;
    @property(cc.Prefab) round_start_prefab: cc.Prefab = null;
    @property(cc.Prefab) fight_begin_prefab: cc.Prefab = null;
    @property(cc.Prefab) fight_end_prefab: cc.Prefab = null;
    @property(cc.Prefab) wait_for_next_round_prefab: cc.Prefab = null;
    // @property(cc.Prefab) win_flag_prefab: cc.Prefab = null;
    @property(cc.Prefab) special_card_type_prefab: cc.Prefab = null;
    // @property(cc.Prefab) win_player_light_prefab: cc.Prefab = null;
    @property(cc.Prefab) btnBet_0_prefab: cc.Prefab = null;
    @property(cc.Prefab) btnBet_3_prefab: cc.Prefab = null;
    @property(cc.Prefab) cow_win_prefab: cc.Prefab = null;
    @property(cc.Prefab) cow_lose_prefab: cc.Prefab = null;
    @property(cc.Prefab) boy_win_prefab: cc.Prefab = null;
    @property(cc.Prefab) boy_lose_prefab: cc.Prefab = null;

    // 子界面
    // @property(cc.Prefab) videoCowboyChart: cc.Prefab = null;
    // @property(cc.Prefab) humanboyExchange_prefab: cc.Prefab = null;
    // @property(cc.Prefab) cowboyRule: cc.Prefab = null;
    // @property(cc.Prefab) cowboySetting: cc.Prefab = null;
    // @property(cc.Prefab) cowboyExit: cc.Prefab = null;
    // @property(cc.Prefab) cowboyList: cc.Prefab = null;
    // @property(cc.Prefab) humanboyAdvancedSetting_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyDialog: cc.Prefab = null;
    // @property(cc.Prefab) humanboyGuid_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyMenu_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyAdvancedAuto_prefab: cc.Prefab = null;
    // @property(cc.Prefab) MiniGameAddAdvancedAuto_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyDialog_prefab: cc.Prefab = null;

    // @property(cc.Font) win_num_FNT: cc.Font = null;
    // @property(cc.Font) bet_btn_num_gray: cc.Font = null;
    // @property(cc.Font) bet_btn_num_yellow: cc.Font = null;
    // @property(cc.Prefab) luckButton_prefab: cc.Prefab = null;
    // @property(cc.Prefab) points_ani_prefab: cc.Prefab = null;
    // @property(cc.Prefab) consumingNotifyPrefab: cc.Prefab = null;
    @property(cc.Node) consumingNotifyHolder: cc.Node = null;

    // Atlas
    private game_dznz_PLIST: cc.SpriteAtlas = null;
    private special_card_type_PLIST: cc.SpriteAtlas = null;
    private video_cowboy_trend_PLIST: cc.SpriteAtlas = null;
    private chart_PLIST: cc.SpriteAtlas = null;
    private language_PLIST: cc.SpriteAtlas = null;
    private videoLanguage_PLIST: cc.SpriteAtlas = null;
    private en_animation_PLIST: cc.SpriteAtlas = null;
    private game_videonz_PLIST: cc.SpriteAtlas = null;

    private points_node: cc.Node = null;

    // 牌/牌型
    private _redCardPanel: cc.Node = null;
    private _blueCardPanel: cc.Node = null;
    private _publicCardPanel: cc.Node = null;
    private _oriRedHandCards: cc.Sprite[] = []; // 2
    private _oriBlueHandCards: cc.Sprite[] = []; // 2
    private _oriPublicCards: cc.Sprite[] = []; // 5
    private _oriRedHandCardsPos: cc.Vec2[] = []; // 2
    private _oriBlueHandCardsPos: cc.Vec2[] = []; // 2
    private _oriPublicCardsPos: cc.Vec2[] = []; // 5

    private _redHandCards: PokerCardControl[] = [];
    private _blueHandCards: PokerCardControl[] = [];
    private _publicCards: PokerCardControl[] = [];
    private _redCardType: cc.Sprite = null;
    private _blueCardType: cc.Sprite = null;
    private _redCardTypeBg: cc.Sprite = null;
    private _blueCardTypeBg: cc.Sprite = null;
    private _mapLevelCardTypeImage: Map<number, string> = new Map(); // 映射： 牌型 <. 图片

    private _gameContent: cc.Node = null;
    private _menuPanel: cc.Node = null;
    private _betContentBg: cc.Sprite = null;
    private _menuBg: cc.Sprite = null;
    private _bottomPanel: cc.Node = null;
    private _btnMenu: cc.Button = null;
    private _btnPlayerList: cc.Button = null;
    private self_panel: cc.Node = null;
    private _topBg: cc.Sprite = null;
    private _btnZouShi: cc.Button = null;

    private _betButtons: cc.Button[] = []; // 5
    private _betButtonTexts: cc.Node[] = []; // vector<@property(cc.Label) this._betButtonTexts;
    private _betButtonMasks: cc.Sprite[] = []; // 5
    private _vBottomBetBtns: MiniGameCommonDef.GameNodeScale[] = []; // 底部下注按钮数组(用于适配位置)
    private _betButtonNum: number = 5; // 下注按钮数量
    private _fBetBtnSrcScaleRate: number = 0.7; // 下注筹码原始缩放比例
    private _fBetBtnTarScaleRate: number = 0.85; // 下注筹码目标缩放比例
    private _fFlyCoinScaleRate: number = 1; // 创建的金币缩放比例

    private _curBetButtonIdx: number = 0;
    private bottom_bg: cc.Node = null;
    private _panel_betbtn: cc.Node = null;

    private _btnBetAuto: cc.Button = null;
    private _btnBetClean: cc.Button = null; // 清屏

    // areaIdx <. xxx
    private _betAreas: cc.Node[] = [];
    private _betCoinContents: cc.Node[] = [];
    private _textSelfBetNum: cc.Label[] = [];
    private _textTotalBetNum: cc.Label[] = [];
    private _oriTextSelfBetNumPos: cc.Vec2[] = [];
    private _oriTextTotalBetNumPos: cc.Vec2[] = [];
    private _sprBetAreaWinFlags: cc.Sprite[] = [];
    private _textBetAreaOdds: cc.Label[] = [];
    private _mapBetOptionArea: Map<number, number> = new Map(); // 映射：BetZoneOption <. index of this._betAreas

    private _betCountDownBg: cc.Sprite = null; // 闹钟背景
    private _textCountDown: cc.Label = null; // 闹钟时间文本
    private _oriBetCountDownBgPos: cc.Vec2 = new cc.Vec2(0, 0); // 闹钟背景初始位置

    // 个人信息
    private _textNickName: cc.Label = null;
    private _textCoin: cc.Label = null;
    private _selfHeadBg: cc.Node = null;
    private _selfCoin: cc.Sprite = null;
    protected selfAvatar: AvatarControl = null;

    private _otherPlayerHeads: OtherPlayerHead[] = [];

    private _leftPlayerPanel: cc.Node = null;
    private _rightPlayerPanel: cc.Node = null;

    private _heroBoy: cc.Sprite = null; // 牛仔
    private _heroCow: cc.Sprite = null; // 小牛

    // 动画
    private _nodeAnim: cc.Node = null; // 动态动画节点
    private _timelineNodeAnim: cc.Node = null; // 动画节点

    private _roundStartAnim: cc.Node = null;
    private _roundStartAction: cc.Animation = null;
    private _fightBeginAnim: cc.Node = null;
    private _fightBeginAction: cc.Animation = null;
    private _fightEndAnim: cc.Node = null;
    private _fightEndAction: cc.Animation = null;
    private _waitForNextRoundAnim: cc.Node = null;
    private _waitForNextRoundAction: cc.Animation = null;
    private _effNode: cc.Node = null;
    private _prizeAnim: cc.Node = null;
    private _prizeAction: cc.Animation = null;
    private _prizeActionIndex: number = 0;

    private _winFlagAnims: cc.Node[] = [];

    // 游戏提示语
    private _gameTipsBg: cc.Sprite = null;
    private _textGameTips: cc.Label = null;

    // 牛仔中奖信息
    private _rewardPanel: cc.Node = null;
    private _rewardTips: cc.RichText = null;
    private _rewardSize: cc.Size = null;

    // 历史纪录 新手提示
    private _recordDotsTemp: cc.Sprite[] = [];
    // 历史纪录
    private _recordDots: cc.Sprite[] = [];
    private _oriRecordDotsPos: cc.Vec2[] = [];
    private _lastRecordDotWorldPos: cc.Vec2 = new cc.Vec2(0, 0); // 显示的最后一个球的世界坐标
    private _recordNum: number = 10; // 历史纪录的数量

    private _humanboyGuid: cc.Node = null; // 引导面板
    private _humanboyMenu: cc.Node = null; // 菜单面板
    private _humanboyAdvancedSetting: cc.Node = null; // 高级设置面板
    private _eAutoBtnStyle: number = MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE; // 续投按钮样式
    protected eGameboyScreenType: MiniGameCommonDef.eGameboyScreenType =
        MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW; // 屏幕类型

    private _videoCowboyChart: cc.Node = null;
    private _videoCowboyChartControl: VideoCowboyChartControl = null;
    private _humanboyExchange: MiniGameExchangeControl = null;
    private _humanboyAdvancedAuto: cc.Node = null; // 高级续投面板

    private _cowboyRule: cc.Node = null;
    private _cowboySetting: cc.Node = null;
    private _cowboyExit: cc.Node = null;
    private _videoCowboyList: cc.Node = null;
    //  _cowboyList: CowboyList = null;
    // Chart * this._chart = null;
    // CowboyMilitary * _minitary = null;
    // Dznzsetting * _dznzsetting = null;
    // Dznzexit * _dznzexit = null;
    // _rule: CowboyRule = null;

    // toast
    private _toastNode: cc.Node = null;

    private _HEAD_IMG_TAG: string = 'cowboy_head_tag';

    private _openIphoneXAdapter: boolean = true; // iPhoneX适配开关
    private _leftTime: number = 0;

    private _btn_redpacket_festival: cc.Node = null; // 红包节按钮
    private _btn_redpacket_festival_layer: cc.Node = null; // 红包节按钮提示层
    private _luckButton: LuckTurntableButtonControl = null; // 红包节 实例

    // 音效/背景音乐
    private s_cowboyBGM: string = macros.Audio.BGM; // 1背景
    private s_kaipai: string = macros.Audio.KAIPAI; // 2发牌、开牌
    private s_fapai: string = macros.Audio.FAPAI; // 2发牌、开牌
    // private s_chuzhan_kaizhan: string = macros.Audio.START_ROUND; // 3出站开战时间提示
    private s_betin: string = macros.Audio.BET; // 4投金币
    private s_betin_many: string = macros.Audio.BET_MANY; // 4投金币
    private s_win_lose: string = macros.Audio.WIN_LOSE; // 5输赢
    private s_get_win_coin: string = macros.Audio.GET_WIN_COIN; // 6收金币
    private s_button: string = macros.Audio.PRESS; // 6收金币
    private s_time_tick: string = macros.Audio.TIME_TICK; // 时间
    private s_begin_bet: string = macros.Audio.BEGIN_BET; // 开始下注
    private s_end_bet: string = macros.Audio.END_BET; // 停止下注
    private s_special_card_type: string = macros.Audio.SPECIAL_CARD_TYPE_BIG; // 特殊牌型音效
    // public silenceMusic: string = 'pkw/zh_CN/game/dzpoker/audio/silence';

    _waitForNextRoundOutTheshould: number = 2; // 剩余时间小于此值播放退出动画

    // 新进房间动画节奏时间
    private _betCountDownEndDuration: number = 0.3;
    private _fightEndDuration: number = 1.05;
    private _showHandCardsDuration: number = 1.0;
    private _showPublicCardsDuration: number = 1.5;
    private _hideLoseBetCoinsDuration: number = 1.5;
    private _specialCardTypeDuration: number = 8; // 特殊牌型动画时间,在显示win动画之前
    private _betWinFlagsAndFlyCoinsDuration: number = 2.7;
    private _showNextRoundDuration: number = 3;

    private LABEL_SIZE: number = 10; // 临时数据，待处理
    private trend_anim: cc.AnimationClip = null;

    private _coinNode: cc.Node = null;
    private _coinNodeByArea: cc.Node[][] = [];
    private _circlePool: cc.NodePool = new cc.NodePool();
    private _squarePool: cc.NodePool = new cc.NodePool();

    private _areaCoinMax: number[] = [200, 200, 200, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100];
    private _isAddEvent: boolean = false;

    private _mapWayOutInfo: Map<number, CowboyWayOutInfo> = new Map(); // 路单结构信息数组
    private _fActExecute_WayOut: number = 1.0; // 显示路子动画 执行时间

    private _cowWinAnim: cc.Node = null; // 牛赢动画
    private _cowWinAction: cc.Animation = null;
    private _fActExecute_CowWin: number = 3.0;

    private _cowLoseAnim: cc.Node = null; // 牛输动画
    private _cowLoseAction: cc.Animation = null;
    private _fActExecute_CowLose: number = 3.0;

    private _boyWinAnim: cc.Node = null; // 牛仔赢动画
    private _boyWinAction: cc.Animation = null;
    private _fActExecute_BoyWin: number = 3.0;

    private _boyLoseAnim: cc.Node = null; // 牛仔输动画
    private _boyLoseAction: cc.Animation = null;
    private _fActExecute_BoyLose: number = 3.0;

    private _vCoinOptimizationDeque: Deque<PlayerOneBet> = new Deque();
    private _msInterval: number = 1; // 定时器间隔(单位: 秒)
    private _msNowTime: number = 0; // 当前时间
    private _msLastTime: number = 0; // 上次时间
    private _nLeftTime: number = 0; // 剩余时间
    private _nMinTime: number = 0; // 最小时间
    private _isIpad: boolean = false;
    private _isEnterBackground: boolean = false;
    private _effectMap: Map<string, EffectLoop> = new Map();
    private _isViewX: boolean = false;
    private _bSwitchTable: boolean = false;
    private _advanceAutoAddBet: cc.Node = null; // 高级续投面板
    private _consumingNotify: ConsumingPromptControl = null;

    private _videoCowboyRoom: pf.Nullable<domain.VideoCowboyRoom> = null;
    private _walletService: pf.services.WalletService = null;
    private _pushNotificationService: pf.services.PushNotificationService = null;
    private _authService: pf.services.AuthService = null;
    private _luckTurntableService: pf.services.LuckTurntableService = null;
    private _calmDownService: pf.services.CalmDownService = null;

    private _boundLuckTurntableStartOrEnd = this.showLuckButton.bind(this);
    private _boundLuckTurntableResult = this.onTurntableResultNotice.bind(this);
    private _boundEnterBackgroundHandler = this.OnAppEnterBackground.bind(this);
    private _boundEnterForegroundHandler = this.OnAppEnterForeground.bind(this);
    private _boundUpdateGoldHandler = this._onMsgUpdateWorldServerGold.bind(this);
    // TODO 待確認需不需要這些
    // private _boundRebateEventStop = this.hideRebateActivity.bind(this);
    // private _boundGetRebateEventStatus = this._getRebateEventStatus.bind(this);
    private _boundPushNotification = this._onPushNotification.bind(this);

    private _platform: string = '';

    private _IS_IPHONEX_SCREEN: boolean = false;

    private _fActExecute_WinFlag: Readonly<number> = 2.5; // win动画 执行时间

    private _liveVideoComponent?: LiveVideoComponent = null;

    onLoad() {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        if (context) {
            this._videoCowboyRoom = context.room as domain.VideoCowboyRoom;
            this._platform = context.platform || 'pkw';
        }
        this._walletService = pf.serviceManager.get(pf.services.WalletService);
        this._pushNotificationService = pf.serviceManager.get(pf.services.PushNotificationService);
        this._authService = pf.serviceManager.get(pf.services.AuthService);
        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);
        this._calmDownService = pf.serviceManager.get(pf.services.CalmDownService);
        pf.app.setCurrentScene(macros.SCENE_NANE);

        // (<any>window).HMFExtension.setSwitchFullScreenUseStatusLandscape();
        // (<any>window).HMFAppSetting.setStatusBarHiden();
        // cv.config.setCanShowMsg(true);

        // this.language_PLIST = cv.resMgr.getSpriteAtlas(
        //     cv.config.getLanguagePath('game/pokermaster/cowboyPlist/language')
        // );
        // this.videoLanguage_PLIST = cv.resMgr.getSpriteAtlas(
        //     cv.config.getLanguagePath('game/videoCowboyPlist/language')
        // );
        // cv.config.setCurrentScene(cv.Enum.SCENE.VIDEOCOWBOY_SCENE);
        // cv.config.adaptScreenHen(this.node);

        // cv.resMgr.adaptWidget(this.node, true);
        // layer.initScent();  未处理

        // cv.pushNotice.hideNoticeLayer();

        this.initLiveVideo();
        // (<any>window).HMFAppSetting.isPokerMasterExitWithRechargeSuccess = false;

        this.updateGlobalCarousel();
    }

    // on "init" you need to initialize your instance
    start() {
        cr.UIUtil.adaptWidget(this.node, true);

        let WIDTH = cc.winSize.width;
        let HEIGHT = cc.winSize.height;
        this._IS_IPHONEX_SCREEN = WIDTH > HEIGHT ? WIDTH / HEIGHT > 2436.0 / 1126 : HEIGHT / WIDTH > 2436.0 / 1126;

        // if (cv.SHOP && cv.SHOP.msgNode) cv.SHOP.msgNode.active = false;

        // cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_VIDEOCOWBOY);
        // VideoCowboyManager.addPlist('game_dznz_PLIST', this.game_dznz_PLIST);
        // VideoCowboyManager.addPlist('special_card_type_PLIST', this.special_card_type_PLIST);
        // VideoCowboyManager.addPlist('cowboy_trend_anim_PLIST', this.cowboy_trend_anim_PLIST);
        // VideoCowboyManager.addPlist('chart_PLIST', this.chart_PLIST);
        // VideoCowboyManager.addPlist('language_PLIST', this.language_PLIST);
        // VideoCowboyManager.addPlist('en_animation_PLIST', this.en_animation_PLIST);
        this.game_dznz_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.GAME_DZNZ_ATLAS);
        this.special_card_type_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.SPECIAL_CARD_TYPE_ATLAS);
        this.video_cowboy_trend_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.VIDEO_COWBOY_TREND_ATLAS);
        this.chart_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.CHART_ATLAS);
        this.language_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_LANGUAGE_ATLAS);
        this.videoLanguage_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.VIDEO_COWBOY_LANGUAGE_ATLAS);
        this.en_animation_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.EN_ANIMATION_ATLAS);
        this.game_videonz_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.GAME_VIDEONZ_ATLAS);

        this._gameContent = this.node.getChildByName('game_content');
        this._bottomPanel = this.node.getChildByName('bottomPanel');
        // let contentWidget = this._gameContent.getComponent(cc.Widget);
        // if (cv.config.IS_WIDESCREEN) {
        //     contentWidget.left = 0;
        //     contentWidget.right = 0;
        // }
        // else {
        //     contentWidget.top = 0;
        //     contentWidget.bottom = 0;
        // }
        // cv.resMgr.adaptWidget(this._gameContent, true);

        // console.log('this._bottomPanel.position = ' + this._bottomPanel.position);

        this._heroBoy = cc.find('node_boy/img', this._gameContent).getComponent(cc.Sprite);
        this._heroCow = cc.find('node_cow/img', this._gameContent).getComponent(cc.Sprite);

        // 暂时不要提示
        this._gameTipsBg = this.node.getChildByName('game_tips_bg').getComponent(cc.Sprite);
        this._textGameTips = this._gameTipsBg.node.getChildByName('text_game_tips').getComponent(cc.Label);
        this._gameTipsBg.node.active = false;

        // 牛仔中奖信息
        this._rewardPanel = this.node.getChildByName('rewardPanel');
        this._rewardTips = this._rewardPanel.getChildByName('notice_text').getComponent(cc.RichText);
        this._rewardSize = this._rewardTips.node.getContentSize();
        this._rewardPanel.active = false;

        this._panel_betbtn = this._bottomPanel.getChildByName('panel_betbtn');

        this._coinNode = new cc.Node();
        this.node.addChild(this._coinNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_COIN_NODE);
        this._coinNode.setContentSize(pf.system.view.width, pf.system.view.height);

        this._nodeAnim = new cc.Node();
        this.node.addChild(this._nodeAnim, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
        this._nodeAnim.setContentSize(pf.system.view.width, pf.system.view.height);

        this._timelineNodeAnim = new cc.Node();
        this.node.addChild(this._timelineNodeAnim, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TIMELINE_NODE);
        this._timelineNodeAnim.setContentSize(pf.system.view.width, pf.system.view.height);

        this._openCardLayer = cc.instantiate(this.prefabOpenCard).getComponent(VideoCowboyOpenCardControl);
        this.node.addChild(this._openCardLayer.node);
        this._openCardLayer.language_PLIST = this.language_PLIST;
        this._openCardLayer.game_dznz_PLIST = this.game_dznz_PLIST;

        this._rightChartPanel = this.node.getChildByName('rightChartPanel');

        this.initTimelineAnims();
        this.initCowboyAnims();
        this.initCowboyToastNode();
        this.initButtonEvents();
        this.initCards();
        this.initBetArea();
        this.initBetButtons();
        this.initBetCountDown();
        this._initBetClock();
        this.initCheckXianLu();
        this.initPlayersInfo();
        this.initHistoryDots();

        this.adaptiveScreen(); // iPad/iPhoneX等宽窄屏适配
        this.initRedPackage(); // 初始化红包按钮入口
        this.adaptiveBetBtnPanel(); // 适配下注按钮面板布局

        this.initWinFlagAnims();
        this.initTrendChangeAnim();
        this.initGuide();
        this.initChart();

        this._addObservers();

        this.clearRound(); // 清除场景动态信息
        this.betButtonSelected(0, true); // 默认第一个按钮选中
        this.playCowboyBGM();

        // cv.viewAdaptive.isselfchange = false;
        // cv.viewAdaptive.videoCowboyRoomId = 0;
        // cv.MessageCenter.register('on_cowboy_gamedata_syn_notify', this.OnGameDataSynNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_update_trend_notify', this.OnTrendUpdate.bind(this), this.node);
        // cv.MessageCenter.register('on_update_playerlist_notify', this.OnPlayerListUpdate.bind(this), this.node);
        // cv.MessageCenter.register('on_cowboy_sound_switch_notify', this.OnSoundSwitchNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_selfinfo_notify', this.OnSelfInfo.bind(this), this.node);
        // cv.MessageCenter.register('showMedalMsg', this.OnCowboyRewardTips.bind(this), this.node);
        // cv.MessageCenter.register(
        //     'on_videoCowboy_HandleStopBetNotify',
        //     this._HandleStopBetNotify.bind(this),
        //     this.node
        // );
        // cv.MessageCenter.register(
        //     'on_videoCowboy_HandleSkipRoundNotify',
        //     this._HandleSkipRoundNotify.bind(this),
        //     this.node
        // );
        // cv.MessageCenter.register(
        //     'on_videoCowboy_HandleCancelRoundNotify',
        //     this._HandleCancelRoundNotify.bind(this),
        //     this.node
        // );
        // cv.MessageCenter.register('videoCowboy_ShowCardNotify', this.playKaiPai.bind(this), this.node);
        // cv.MessageCenter.register('toRealBackMainScene', this.onRealBackMainScene.bind(this), this.node);
        // cv.MessageCenter.register('on_videocowboy_showVideoStatusTips', this.showVideoStatusTips.bind(this), this.node);
        // cv.MessageCenter.register('onExitCowboyLiveVideo', this.onExitCowboyLiveVideo.bind(this), this.node);
        // cv.MessageCenter.register('onLeave_room_succ', this.onExitCowboyLiveVideo.bind(this), this.node);
        // cv.MessageCenter.register('goldViewShop', this.onGoldViewShop.bind(this), this.node);

        // 私语版本，走私语切换后台注册
        // if (cv.config.isSiyuType()) {
        //     cv.MessageCenter.register('on_syOnEnterBackground', this.OnAppEnterBackground.bind(this), this.node);
        //     cv.MessageCenter.register('on_syOnEnterForeground', this.OnAppEnterForeground.bind(this), this.node);
        // } else {
        // cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        // cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        // }
        let statusLayer_pos = cc.find('btn_xianlu/statusLayer_pos', this.node);
        let worldPos = statusLayer_pos.parent.convertToWorldSpaceAR(statusLayer_pos.position);
        // cv.StatusView.showElectricImgs(false);

        // 重发 JoinRoom 消息, 同步场景UI
        // cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
        // cv.roomManager.RequestJoinRoom();

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        // context.exitCallback = this.exitGame.bind(this);
        context.exitCallback = this.tryLeaveRoom.bind(this);

        if (this._videoCowboyRoom.isDataSynced) {
            this.OnGameDataSynNotify();
        } else {
            this._videoCowboyRoom.addListener('dataSync', this.OnGameDataSynNotify.bind(this));
        }
    }

    onDestroy() {
        pf.bundleManager.releaseAll(macros.BUNDLE_NAME);
        // this.cleanData();

        // (<any>window).HMFExtension.setSwitchFullScreenUseStatusPortrait();
        // (<any>window).HMFAppSetting.setStatusBarShow();
        // cv.config.setCanShowMsg(false);
    }

    private initLiveVideo(): void {
        cc.warn('[VideoCowboyControl] initLiveVideo');

        if (pf.system.isBrowser) {
            cc.warn('[VideoCowboyControl] LiveVideo is not available in the browser environment.');
            return;
        }

        const liveVideoNode = this.node.getChildByName('liveVideo');
        if (liveVideoNode) {
            this._liveVideoComponent = liveVideoNode.addComponent(LiveVideoComponent);
            liveVideoNode.active = true;
        }
    }

    /**
     * 游戏进入后台时触发的事件
     */
    OnAppEnterBackground(): void {
        // 私语版本, 切回后台后，将所有音频暂停
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // if (cc.sys.os === cc.sys.OS_ANDROID) {
        // cv.AudioMgr.stopMusic();
        // cv.AudioMgr.pauseAll();
        // } else {
        // if (!cr.UIUtil.isPlayMusic()) {
        //     cv.AudioMgr.play(this.silenceMusic, true, 0.5, true);
        // }
        // }
        // cr.UIUtil.setEnterbackState(true);

        // this._effectMap.forEach((key: string, value: EffectLoop, i?: number) => {
        //     cc.audioEngine.stopEffect(value.audioId);
        //     this.unschedule(value.func);
        // });
        // this._effectMap.clear();
        this._liveVideoComponent?.onStopVideo();
        pf.audioManager.stopAll();
        this._isEnterBackground = true;

        // 解决结算飞金币时疯狂秒切前后台卡死的bug, 原因是依赖"this"的定时器回调后金币对象已被销毁
        // 停止根节点所有定时器和动画回调(暂时只能写在房间同步逻辑之前, 否则会造成音效循环播放bug)
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    /**
     * 游戏进入前台运行时触发的事件
     */
    OnAppEnterForeground(): void {
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // cr.UIUtil.setEnterbackState(false);
        // if (cc.sys.os === cc.sys.OS_ANDROID) {
        // cv.AudioMgr.resumeAll();
        // this.OnSoundSwitchNotify();
        // } else {
        // if (!cr.UIUtil.isPlayMusic()) {
        //     cv.AudioMgr.stop(cv.AudioMgr.getAudioID(this.silenceMusic));
        // }
        // }
        // }
        this._liveVideoComponent?.onPlayVideo();
        pf.audioManager.playMusic(macros.Audio.BGM);

        this._isEnterBackground = false;
    }

    protected update(dt: number): void {
        if (this._vCoinOptimizationDeque.size() <= 0) return;
        this._updateCoinOptimization(dt);
    }

    /**
     * 刷新"金币最优队列"(每帧创建, 稳定帧率)
     */
    private _updateCoinOptimization(dt: number): void {
        let nTotalCount: number = this._vCoinOptimizationDeque.size();
        if (nTotalCount <= 0) return;

        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            let nCount = 0;

            // 剩余时间 > 1s 逐帧喷吐金币
            if (this._leftTime > 1) {
                nCount = nTotalCount / cc.game.getFrameRate();
                nCount = Math.ceil(nCount);
            }
            // 否则, 一次性喷吐剩余金币(弥补金币数量多、卡帧导致喷吐金币不完整的情况)
            else {
                nCount = nTotalCount;
            }

            // console.log(pf.StringUtil.formatC("VideoCowboyGame_Coin: sec = %02d, dt = %05f, total = %05f, count = %05f", this._getLeftTime(), dt, nTotalCount, nCount));

            for (let i = 0; i < nCount; ++i) {
                let t: PlayerOneBet = this._vCoinOptimizationDeque.pop_front();
                this.showBetInAnim(t);
            }
        } else {
            // 更新剩余的金币数等(在卡帧情况下, 计时误差等情况下, 飞金币被强行停止, 但数据要保持最新, 因为这是一个逐帧队列, 不是及时更新)
            for (let i = 0; i < nTotalCount; ++i) {
                let t: PlayerOneBet = this._vCoinOptimizationDeque.pop_front();
                this.updatePlayerCoin(t.uid);
                this.updateBetArea(t.betOption);
            }

            // 清除队列
            this._vCoinOptimizationDeque.clear();
        }
    }

    private _addObservers() {
        // cv.MessageCenter.register('on_cowboy_game_round_end_notify', this.OnGameRoundEndNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('gameRoundEnd', this.OnGameRoundEndNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_deal_notify', this.OnDealNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('deal', this.OnDealNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_start_bet_notify', this.OnStartBetNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('startBet', this.OnStartBetNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_bet_notify', this.OnBetNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('bet', this.OnBetNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_auto_bet_notify', this.OnAutoBetNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('autoBet', this.OnAutoBetNotify.bind(this));
        // cv.MessageCenter.register(
        //     'on_cowboy_auto_bet_notify_handle_over',
        //     this.OnAutoBetNotifyHandleOver.bind(this),
        //     this.node
        // );
        // cv.MessageCenter.register('on_cowboy_leave_room_succ', this.OnLeaveRoomSucc.bind(this), this.node);
        // cv.MessageCenter.register('on_cowboy_auto_bet_succ', this.OnAutoBetSucc.bind(this), this.node);
        // cv.MessageCenter.register(
        //     'on_cowboy_room_param_change_notify',
        //     this.OnRoomParamChangeNotify.bind(this),
        //     this.node
        // );
        this._videoCowboyRoom.addListener('roomParamChange', this.OnRoomParamChangeNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_server_error', this.OnServerError.bind(this), this.node);
        this._videoCowboyRoom.addListener('serverError', this.OnServerError.bind(this));
        // cv.MessageCenter.register('on_cowboy_kick_notify', this.OnKickNotify.bind(this), this.node);
        this._videoCowboyRoom.addListener('kicked', this.OnKickNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_join_room_failed', this.OnJoinRoomFailed.bind(this), this.node);
        // cv.MessageCenter.register('showShopPanel', this.openShop.bind(this), this.node);

        // cv.MessageCenter.register('showLuckButton', this.showLuckButton.bind(this), this.node); // 红包节
        // cv.MessageCenter.register('turntableResultNotice', this.onTurntableResultNotice.bind(this), this.node);

        // cv.MessageCenter.register('update_gold', this._onMsgUpdateWorldServerGold.bind(this), this.node); // world服金币有变动通知
        // cv.MessageCenter.register(
        //     'on_cowboy_bet_amount_level_change',
        //     this._onMsgBetAmountLevelChange.bind(this),
        //     this.node
        // ); // 下注级别变更
        this._videoCowboyRoom.addListener('betCoinOptionsChange', this._onMsgBetAmountLevelChange.bind(this)); // 下注级别变更
        // cv.MessageCenter.register('on_cowboy_advance_autobet_set', this._onMsgAdvanceAutobetSet.bind(this), this.node); // 设置高级续投次数成功
        this._videoCowboyRoom.addListener('advanceAutoBetCountSet', this._onMsgAdvanceAutobetSet.bind(this)); // 设置高级续投次数成功
        // cv.MessageCenter.register('on_advance_autobet', this._onMsgAdvanceAutobet.bind(this), this.node); // 高级续投
        this._videoCowboyRoom.addListener('advanceAutoBet', this._onMsgAdvanceAutobet.bind(this)); // 高级续投
        // cv.MessageCenter.register(
        //     'on_cowboy_advance_autobet_cancel',
        //     this._onMsgAdvanceAutobetCancel.bind(this),
        //     this.node
        // ); // 取消高级续投成功
        this._videoCowboyRoom.addListener('advanceAutoBetCancel', this._onMsgAdvanceAutobetCancel.bind(this)); // 取消高级续投成功
        // cv.MessageCenter.register(
        //     'on_cowboy_advance_autobet_limit_reached',
        //     this._onMsgAdvanceAutobetLimitReached.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        this._videoCowboyRoom.addListener(
            'advanceAutoBetLimitReached',
            this._onMsgAdvanceAutobetLimitReached.bind(this)
        ); // 高级续投接近或者已达上限
        this._videoCowboyRoom.addListener('advanceAutoBetCountAdd', this.onMsgAdvanceAutobetAdd.bind(this));

        // cv.MessageCenter.register('on_cowboy_gamedata_syn_notify', this.OnGameDataSynNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_update_trend_notify', this.OnTrendUpdate.bind(this), this.node);
        this._videoCowboyRoom.addListener('trendNotify', this.OnTrendUpdate.bind(this));
        // cv.MessageCenter.register('on_update_playerlist_notify', this.OnPlayerListUpdate.bind(this), this.node);
        // cv.MessageCenter.register('on_cowboy_sound_switch_notify', this.OnSoundSwitchNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_selfinfo_notify', this.OnSelfInfo.bind(this), this.node);
        // cv.MessageCenter.register('showMedalMsg', this.OnCowboyRewardTips.bind(this), this.node);
        // cv.MessageCenter.register('goldViewShop', this.onGoldViewShop.bind(this), this.node);
        // cv.MessageCenter.register('onNoticeOpenCalmDownWindow', this.onCalmDownShowTip.bind(this), this.node);

        this._videoCowboyRoom.addListener('leftGameCoin', this._onMsgConsumingNotify.bind(this));

        this._videoCowboyRoom.addListener('leaveRoom', this.exitGame.bind(this));

        // cv.MessageCenter.register('onCalmDownMsg', this.onCalmDownShowTip.bind(this), this.node);
        // cv.MessageCenter.register('on_advance_autobet_add', this.onMsgAdvanceAutobetAdd.bind(this), this.node);
        // cv.MessageCenter.register(
        //     'onResponseRebateEventStatus',
        //     this._onResponseRebateEventStatus.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        // cv.MessageCenter.register(
        //     'onResponseRebateReceiveReward',
        //     this._onResponseRebateReceiveReward.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        // cv.MessageCenter.register('on_rebate_reward_popup', this.showRebateRewardPopup.bind(this), this.node);
        // cv.MessageCenter.register('onRebateEventStop', this.hideRebateActivity.bind(this), this.node);

        // 私语版本，走私语切换后台注册
        // if (cv.config.isSiyuType()) {
        //     cv.MessageCenter.register('on_syOnEnterBackground', this.OnAppEnterBackground.bind(this), this.node);
        //     cv.MessageCenter.register('on_syOnEnterForeground', this.OnAppEnterForeground.bind(this), this.node);
        // } else {
        //     cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        //     cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        // }
        this._videoCowboyRoom.addListener('stopBetNotify', this._HandleStopBetNotify.bind(this));
        this._videoCowboyRoom.addListener('showCardNotify', this.playKaiPai.bind(this));
        this._videoCowboyRoom.addListener('skipRoundNotify', this._HandleSkipRoundNotify.bind(this));
        this._videoCowboyRoom.addListener('cancelRoundNotify', this._HandleCancelRoundNotify.bind(this));

        pf.app.events().addListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.events().addListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._walletService.addListener('userGoldNum', this._boundUpdateGoldHandler);

        this._luckTurntableService.addListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableResult', this._boundLuckTurntableResult);

        // TODO 確認 videocowboy 要不要 _rebateService
        // this._rebateService.addListener('eventStatusStop', this._boundRebateEventStop);
        // this._rebateService.addListener('refreshEventStatus', this._boundGetRebateEventStatus);

        this._pushNotificationService.addListener('pushNotification', this._boundPushNotification);
    }

    private _removeObservers() {
        // cv.MessageCenter.unregister('on_cowboy_gamedata_syn_notify', this.node);
        // cv.MessageCenter.unregister('on_update_trend_notify', this.node);
        // cv.MessageCenter.unregister('on_update_playerlist_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_sound_switch_notify', this.node);
        // cv.MessageCenter.unregister('on_selfinfo_notify', this.node);
        // cv.MessageCenter.unregister('showMedalMsg', this.node);// TODO: not dealt with
        // cv.MessageCenter.unregister('goldViewShop', this.node);

        // cv.MessageCenter.unregister('on_cowboy_game_round_end_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_deal_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_start_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_notify_handle_over', this.node);
        // cv.MessageCenter.unregister('on_cowboy_leave_room_succ', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_succ', this.node);
        // cv.MessageCenter.unregister('on_cowboy_room_param_change_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_server_error', this.node);
        // cv.MessageCenter.unregister('on_cowboy_kick_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_join_room_failed', this.node);
        // cv.MessageCenter.unregister('showShopPanel', this.node);

        // cv.MessageCenter.unregister('showLuckButton', this.node);
        // cv.MessageCenter.unregister('turntableResultNotice', this.node);

        // cv.MessageCenter.unregister('update_gold', this.node);
        // cv.MessageCenter.unregister('on_cowboy_bet_amount_level_change', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_set', this.node);
        // cv.MessageCenter.unregister('on_advance_autobet', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_cancel', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_limit_reached', this.node);
        // cv.MessageCenter.unregister('onNoticeOpenCalmDownWindow', this.node);
        // cv.MessageCenter.unregister('onCalmDownMsg', this.node);
        // cv.MessageCenter.unregister('on_advance_autobet_add', this.node);

        // cv.MessageCenter.unregister('onResponseRebateEventStatus', this.node);
        // cv.MessageCenter.unregister('onResponseRebateReceiveReward', this.node);
        // cv.MessageCenter.unregister('on_rebate_reward_popup', this.node);
        // cv.MessageCenter.unregister('onRebateEventStop', this.node);

        // if (cv.config.isSiyuType()) {
        //     cv.MessageCenter.unregister('on_syOnEnterBackground', this.node);
        //     cv.MessageCenter.unregister('on_syOnEnterForeground', this.node);
        // } else {
        //     cc.game.off(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        //     cc.game.off(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        // }
        pf.app.events().removeListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.events().removeListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._walletService.removeListener('userGoldNum', this._boundUpdateGoldHandler);

        this._luckTurntableService.removeListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableResult', this._boundLuckTurntableResult);

        // TODO 確認 videocowboy 要不要 _rebateService
        // this._rebateService.removeListener('eventStatusStop', this._boundRebateEventStop);
        // this._rebateService.removeListener('refreshEventStatus', this._boundGetRebateEventStatus);

        this._pushNotificationService.removeListener('pushNotification', this._boundPushNotification);
    }

    playEffectForPath(path: string): void {
        if (this._effectMap.has(path)) {
            let obj = this._effectMap.get(path);
            let state = cc.audioEngine.getState(obj.audioId);
            let isPlay: boolean = state === cc.audioEngine.AudioState.PLAYING;
            if (obj.bGoOn === true && isPlay) return;
            let currentTime: number = new Date().getTime();
            if (isPlay === false) {
                if (state === cc.audioEngine.AudioState.PAUSED) {
                    console.log(' PAUSED', ', ', obj);
                    cc.audioEngine.resumeEffect(obj.audioId);

                    obj.startPlayTime = currentTime;
                    this.schedule(obj.func, obj.duringTime);
                } else {
                    console.log(' !PAUSED state = ', state, ', ', obj);
                }
                return;
            }
            console.log(' PLAYING', ', ', obj);
            if (currentTime > obj.startPlayTime + obj.duringTime * 0.5 * 1000) {
                obj.bGoOn = true;
            }
        } else {
            // let clip: cc.AudioClip = cv.resMgr.get(path, cc.AudioClip);
            let clip: cc.AudioClip;
            cc.audioEngine.setEffectsVolume(0.09);
            let audioId = cc.audioEngine.playEffect(clip, true);
            let duringTime = cc.audioEngine.getDuration(audioId);
            // eslint-disable-next-line new-cap
            let obj = new EffectLoop();
            obj.audioId = audioId;
            obj.bGoOn = false;
            obj.duringTime = duringTime;
            obj.startPlayTime = new Date().getTime();
            obj.func = (delay: number) => {
                let obj = this._effectMap.get(path);
                obj.startPlayTime = new Date().getTime();
                if (obj.bGoOn === false) {
                    cc.audioEngine.pauseEffect(obj.audioId);
                    console.log('yyx123 setCurrentTime');
                    cc.audioEngine.setCurrentTime(obj.audioId, 0);
                    this.unschedule(obj.func);
                }
                obj.bGoOn = false;
            };
            this._effectMap.set(path, obj);
            cc.audioEngine.setFinishCallback(audioId, () => {
                console.log('yyx123 setFinishCallback');
                cc.audioEngine.stopEffect(audioId);
                this.unschedule(obj.func);
                this._effectMap.delete(path);
            });

            this.schedule(obj.func, obj.duringTime);
        }
    }

    ingorePutInQuenue(path: string) {
        // 私语平台，开始下注，停止下注不放在队列播放
        if (
            path !== this.s_begin_bet &&
            path !== this.s_end_bet &&
            path !== this.s_win_lose &&
            path !== this.s_special_card_type &&
            path !== this.s_time_tick
        ) {
            return true;
        }

        return false;
    }

    playSoundEffect(name: string): void {
        pf.audioManager.playSoundEffect(name);
        // if (cr.UIUtil.isSoundEffectOpen() && this._isEnterBackground == false) {
        //     if (cc.sys.isBrowser && this.ingorePutInQuenue(path)) {
        //         this.playEffectForPath(path);
        //     } else {
        //         cv.AudioMgr.playEffect(path, false, 0.09);
        //     }
        // }
    }

    playCowboyBGM(): void {
        pf.audioManager.playMusic(macros.Audio.BGM);
        // if (cr.UIUtil.isPlayMusic()) {
        //     cv.AudioMgr.playMusic(this.s_cowboyBGM, true, 0.09);
        //     //        SimpleAudioEngine.getInstance().playBackgroundMusic(s_cowboyBGM, true);
        // }
    }

    stopCowboyBGM(): void {
        pf.audioManager.stopMusic();
        // cv.AudioMgr.stopMusic();
        // SimpleAudioEngine.getInstance().stopBackgroundMusic();
    }

    backToCowboyListScene(): void {
        // this._videoCowboyRoom.Reset();
        // cv.netWorkManager.closeGameConnect();

        // cv.viewAdaptive.isselfchange = false;
        // cv.viewAdaptive.videoCowboyRoomId = 0;

        // 回到牛仔房间列表界面
        // cv.roomManager.reset();

        // TODO 待確認這些邏輯是否可以全部捨棄
        // 原生版本扑克大师,牛仔在下注过程中，充值退出会失败，此参数表示是否成功充值的方式退出
        // const isPokerMasterExitWithRechargeSuccess = (<any>window).HMFAppSetting.isPokerMasterExitWithRechargeSuccess;
        // let exitWhere = (<any>window).HMFAppSetting.pokerMasterExitWhere;
        // if (isPokerMasterExitWithRechargeSuccess) {
        //     exitWhere = 3;
        // }
        // let scene: string;
        // if (exitWhere == 1) {
        //     scene = 'pkwgame'; // 大厅
        // } else if (exitWhere == 2) {
        //     scene = 'openWalletSet'; // 打开钱包
        // } else if (exitWhere == 3) {
        //     scene = 'rechargeInCowboy'; // 打开钱包并充值
        // } else {
        //     scene = 'littleGame'; // 小游戏列表
        // }
        // const sceneData = { scene };
        // (<any>window).SceneMgr.setDataFromLastScene(sceneData);

        // cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene) => {
        //     if (cv.roomManager.isEnterMTT) return;
        //     cv.MessageCenter.send('switchSceneToMiniGame');
        // });
        this.tryLeaveRoom();
    }

    cleanData(): void {
        this._removeObservers();

        // this.stopCowboyBGM();
        this.unschedule(this.onLeftTimeUpdate);

        // 清除路单数组信息
        this._clearWayOutInfo();

        pf.audioManager.stopAll();

        this._winFlagAnims.forEach((value: cc.Node, key: number) => {
            if (!value.isValid) return;
            let anim = value.getComponent(cc.Animation);
            let vClips: cc.AnimationClip[] = anim.getClips();
            for (const clip of vClips) {
                anim.stop(clip.name);
            }
        });
    }

    backToMainScene(backToMainTips?: string): void {
        const checkedTip = backToMainTips === undefined ? '' : backToMainTips;
        // this._videoCowboyRoom.Reset();
        // cv.netWorkManager.closeGameConnect();
        // this._videoCowboyRoom.backToMainTips = backToMainTips;
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context.backToMainTips = checkedTip;

        this.tryLeaveRoom();

        // 回到大厅
        // if (!VideoCowboyManager.isPlayVideo()) {
        //     //cc.sys.isBrowser ||
        //     cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);
        // } else {
        //     cv.MessageCenter.send('onExitCowboyLiveVideo');
        // }
    }

    // 清除场景动态信息
    clearRound(): void {
        this.hideAllWinPlayerLightAnim();
        this.hideAllCardsAndCardType();
        this.hideWinCards();
        this.clearAllBetArea();
        this.hideBetCountDown();
        this.showCowboyNormalAnim();
        this._nodeAnim.stopAllActions();
        this._nodeAnim.removeAllChildren(true);
        this._nodeAnim.destroyAllChildren();
        this.hideAllTimelineAnims();
        this.hideGameTips();
        this.hideHistoryMoveAnim();
        this.hideTrendChangeAnim();
        this.clearAllCowboyToasts();
        this._openCardLayer.reset();
        this.showBetClock(false);
        this.hideKaiJuSprite();
        this.hideStopXiazhuSprite();
        this.hideKaiPaiSprite();
        this.hideJieSuanSprite();
        this.handleClockChangeColor();
        this._updateBetButtonState();
        this.resetPointAni();
    }

    protected createPokerCard(): PokerCardControl {
        const card = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.POKER_CARD));
        if (card === null) {
            return null;
        }
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(pf.addressableAssetManager.getAsset(macros.Assets.CARD_BACK));
        return ctrl;
    }

    clearSceneAfterJieSuan(): void {
        this.hideAllWinPlayerLightAnim();
        this.hideAllCardsAndCardType();
        this.hideWinCards();
        this.clearAllBetArea();
        this.hideBetCountDown();
        this.showCowboyNormalAnim();
        this._nodeAnim.destroyAllChildren();
        this._nodeAnim.removeAllChildren(true);
        this._nodeAnim.stopAllActions();

        this._roundStartAction.pause();
        this._roundStartAnim.active = false;

        this._fightBeginAction.pause();
        this._fightBeginAnim.active = false;

        this._fightEndAction.pause();
        this._fightEndAnim.active = false;

        this.hideGameTips();
        this.hideHistoryMoveAnim();
        this.hideTrendChangeAnim();
        this.clearAllCowboyToasts();

        this.showBetClock(false);
        this.hideKaiJuSprite();
        this.hideStopXiazhuSprite();
        this.hideKaiPaiSprite();
        this.hideJieSuanSprite();
        this.handleClockChangeColor();
    }

    // /* 牌型：（redLevel/blueLevel）
    // 皇家同花顺：10
    // 同花顺    ：9
    // 四条      ：8
    // 葫芦      ：7
    // 同花      ：6
    // 顺子      ：5
    // 三条      ：4
    // 两对      ：3
    // 一对      ：2
    // 高牌      ：1
    // */
    initCards(): void {
        this._redCardPanel = this._gameContent.getChildByName('red_card_panel');
        this._blueCardPanel = this._gameContent.getChildByName('blue_card_panel');
        this._oriRedHandCards.push(this._redCardPanel.getChildByName('handcard_0').getComponent(cc.Sprite));
        this._oriRedHandCards.push(this._redCardPanel.getChildByName('handcard_1').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._blueCardPanel.getChildByName('handcard_0').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._blueCardPanel.getChildByName('handcard_1').getComponent(cc.Sprite));
        for (let i = 0; i < 2; i++) {
            let RedHandpos: cc.Vec2 = cc.v2(this._oriRedHandCards[i].node.getPosition());
            let BlueHandpos: cc.Vec2 = cc.v2(this._oriBlueHandCards[i].node.getPosition());
            this._oriRedHandCardsPos.push(RedHandpos);
            this._oriBlueHandCardsPos.push(BlueHandpos);
            this._redHandCards.push(this.createPokerCard());
            this._blueHandCards.push(this.createPokerCard());
            if (this._redHandCards[i] !== null) this._redHandCards[i].ResetFromNode(this._oriRedHandCards[i].node);
            if (this._blueHandCards[i] !== null) this._blueHandCards[i].ResetFromNode(this._oriBlueHandCards[i].node);
        }

        this._publicCardPanel = this._gameContent.getChildByName('public_card_panel');
        for (let i = 0; i < 5; i++) {
            this._oriPublicCards.push(
                this._publicCardPanel.getChildByName(pf.StringUtil.formatC('handcard_%d', i)).getComponent(cc.Sprite)
            );
            let pos: cc.Vec2 = cc.v2(this._oriPublicCards[i].node.getPosition());
            this._oriPublicCardsPos.push(pos);
            this._publicCards.push(this.createPokerCard());
            if (this._publicCards[i] !== null) this._publicCards[i].ResetFromNode(this._oriPublicCards[i].node);
        }

        this._redCardType = this._redCardPanel.getChildByName('red_card_type').getComponent(cc.Sprite);
        this._blueCardType = this._blueCardPanel.getChildByName('blue_card_type').getComponent(cc.Sprite);
        this._redCardTypeBg = this._redCardPanel.getChildByName('red_card_type_bg').getComponent(cc.Sprite);
        this._blueCardTypeBg = this._blueCardPanel.getChildByName('blue_card_type_bg').getComponent(cc.Sprite);
        this._redCardTypeBg.node.zIndex = 1;
        this._blueCardTypeBg.node.zIndex = 1;
        this._redCardType.node.zIndex = 2;
        this._blueCardType.node.zIndex = 2;

        this._mapLevelCardTypeImage.set(1, 'gaopai');
        this._mapLevelCardTypeImage.set(2, 'yidui');
        this._mapLevelCardTypeImage.set(3, 'liangdui');
        this._mapLevelCardTypeImage.set(4, 'santiao');
        this._mapLevelCardTypeImage.set(5, 'shunzi');
        this._mapLevelCardTypeImage.set(6, 'tonghua');
        this._mapLevelCardTypeImage.set(7, 'hulu');
        this._mapLevelCardTypeImage.set(8, 'jingang');
        this._mapLevelCardTypeImage.set(9, 'tonghuashun');
        this._mapLevelCardTypeImage.set(10, 'huangtong');
    }

    hideAllCardsAndCardType(): void {
        this.setAllCardsVisible(false);

        this._redCardType.node.active = false;
        this._blueCardType.node.active = false;
        this._redCardTypeBg.node.active = false;
        this._blueCardTypeBg.node.active = false;
    }

    setAllHandCardsVisible(visible: boolean): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].node.active = visible;
            this._blueHandCards[i].node.active = visible;
            this._redHandCards[i].node.stopAllActions();
            this._blueHandCards[i].node.stopAllActions();
            if (visible) {
                this._redHandCards[i].node.setPosition(this._oriRedHandCardsPos[i]);
                this._blueHandCards[i].node.setPosition(this._oriBlueHandCardsPos[i]);
            }
        }
    }

    setAllPublicCardsVisible(visible: boolean): void {
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].node.active = visible;
            this._publicCards[i].node.stopAllActions();
            if (visible) {
                this._publicCards[i].node.setPosition(this._oriPublicCardsPos[i]);
            }
        }
    }

    setAllCardsVisible(visible: boolean): void {
        this.setAllHandCardsVisible(visible);
        this.setAllPublicCardsVisible(visible);
    }

    // 直接更新牌：公共牌、手牌
    updateAllCardsBack(): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].SetFace(false);
            this._blueHandCards[i].SetFace(false);
        }

        for (let i = 0; i < 5; i++) {
            this._publicCards[i].SetFace(false);
        }
    }

    // 直接更新牌：公共牌、手牌
    updateCards(): void {
        // update cards
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        // 更新所有正面牌
        if (
            this._videoCowboyRoom.roundInfo.redHandCards.length === 2 &&
            this._videoCowboyRoom.roundInfo.blueHandCards.length === 2
        ) {
            for (let i = 0; i < 2; i++) {
                this._redHandCards[i].SetContent(
                    this._videoCowboyRoom.roundInfo.redHandCards[i].number,
                    this._videoCowboyRoom.roundInfo.redHandCards[i].suit
                );
                this._blueHandCards[i].SetContent(
                    this._videoCowboyRoom.roundInfo.blueHandCards[i].number,
                    this._videoCowboyRoom.roundInfo.blueHandCards[i].suit
                );
                this._redHandCards[i].SetFace(true);
                this._blueHandCards[i].SetFace(true);
            }
        }

        let publicCardNum = this._videoCowboyRoom.roundInfo.publicCards.length;
        if (publicCardNum === 1) {
            console.log('1111111111-》publicCardNum == 1');
            return;
        }
        for (let i = 0; i < 5; i++) {
            if (i < publicCardNum) {
                this._publicCards[i].SetContent(
                    this._videoCowboyRoom.roundInfo.publicCards[i].number,
                    this._videoCowboyRoom.roundInfo.publicCards[i].suit
                );
                this._publicCards[i].SetFace(true);
            }
        }
    }

    // 直接更新牌型
    updateCardType(): void {
        this._redCardType.node.active = true;
        this._blueCardType.node.active = true;
        this._redCardTypeBg.node.active = true;
        this._blueCardTypeBg.node.active = true;
        let redCardTypeImage = this._mapLevelCardTypeImage.get(this._videoCowboyRoom.roundInfo.roundResult.redLevel);
        let blueCardTypeImage = this._mapLevelCardTypeImage.get(this._videoCowboyRoom.roundInfo.roundResult.blueLevel);

        // 0 平 1 牛仔胜 -1 小牛胜
        if (this._videoCowboyRoom.roundInfo.roundResult.result === 0) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._redCardType, redCardTypeImage + '');
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.game_dznz_PLIST,
            //     this._blueCardType,
            //     blueCardTypeImage + ''
            // );
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._redCardTypeBg, 'win_cardtype_bg');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._blueCardTypeBg, 'win_cardtype_bg');
            this._redCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(redCardTypeImage);
            this._blueCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(blueCardTypeImage);
            this._redCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('win_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('win_cardtype_bg');
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === 1) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._redCardType, redCardTypeImage + '');
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.game_dznz_PLIST,
            //     this._blueCardType,
            //     blueCardTypeImage + '_gray'
            // );
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._redCardTypeBg, 'win_cardtype_bg');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._blueCardTypeBg, 'lose_cardtype_bg');
            this._redCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(redCardTypeImage);
            this._blueCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(blueCardTypeImage + '_gray');
            this._redCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('win_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('lose_cardtype_bg');
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === -1) {
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.game_dznz_PLIST,
            //     this._redCardType,
            //     redCardTypeImage + '_gray'
            // );
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.game_dznz_PLIST,
            //     this._blueCardType,
            //     blueCardTypeImage + ''
            // );
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._redCardTypeBg, 'lose_cardtype_bg');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._blueCardTypeBg, 'win_cardtype_bg');
            this._redCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(redCardTypeImage + '_gray');
            this._blueCardType.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(blueCardTypeImage);
            this._redCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('lose_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('win_cardtype_bg');
        }
    }

    updateWinCards(): void {
        // 先全部置灰
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].Gray(true);
            this._blueHandCards[i].Gray(true);
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].Gray(true);
        }

        // 高亮赢的5张牌
        // let winCardNum = this._videoCowboyRoom.winCards.length;
        // for (let i = 0; i < winCardNum; i++) {
        //     let winCard = this._videoCowboyRoom.winCards[i];
        //     let isFind = false;
        //     for (let j = 0; j < 2; j++) {
        //         if (
        //             this._redHandCards[j].GetNumber() == winCard.number &&
        //             this._redHandCards[j].GetSuit() == winCard.suit
        //         ) {
        //             this._redHandCards[j].Gray(false);
        //             isFind = true;
        //             break;
        //         }

        //         if (
        //             this._blueHandCards[j].GetNumber() == winCard.number &&
        //             this._blueHandCards[j].GetSuit() == winCard.suit
        //         ) {
        //             this._blueHandCards[j].Gray(false);
        //             isFind = true;
        //             break;
        //         }
        //     }

        //     if (!isFind) {
        //         for (let j = 0; j < 5; j++) {
        //             if (
        //                 this._publicCards[j].GetNumber() == winCard.number &&
        //                 this._publicCards[j].GetSuit() == winCard.suit
        //             ) {
        //                 this._publicCards[j].Gray(false);
        //                 isFind = true;
        //                 break;
        //             }
        //         }
        //     }
        // }
        this._videoCowboyRoom.roundInfo.roundResult.Cards.forEach((winCard) => {
            let isFind = false;
            for (let j = 0; j < 2; j++) {
                if (
                    this._redHandCards[j].GetNumber() === winCard.number &&
                    this._redHandCards[j].GetSuit() === winCard.suit
                ) {
                    this._redHandCards[j].Gray(false);
                    isFind = true;
                    break;
                }

                if (
                    this._blueHandCards[j].GetNumber() === winCard.number &&
                    this._blueHandCards[j].GetSuit() === winCard.suit
                ) {
                    this._blueHandCards[j].Gray(false);
                    isFind = true;
                    break;
                }
            }

            if (!isFind) {
                for (let j = 0; j < 5; j++) {
                    if (
                        this._publicCards[j].GetNumber() === winCard.number &&
                        this._publicCards[j].GetSuit() === winCard.suit
                    ) {
                        this._publicCards[j].Gray(false);
                        isFind = true;
                        break;
                    }
                }
            }
        });
    }

    hideWinCards(): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].Gray(false);
            this._blueHandCards[i].Gray(false);
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].Gray(false);
        }
    }

    updateWinFlags(): void {
        let arr = this._videoCowboyRoom.roundInfo.matchOptions;
        let len = arr.length;
        // 显示win标记/隐藏区域数字和金币
        for (let i = 0; i < len; i++) {
            let areaIdx = this.getAreaIdxByBetOption(arr[i]);
            this.clearBetArea(areaIdx);
            this.hideWinFlagAnim(areaIdx);
            // this._sprBetAreaWinFlags[areaIdx]..node.active = (true);
            this.showWinFlagAnim(areaIdx);
        }
    }

    hideWinFlags(): void {
        let len = this._winFlagAnims.length;
        for (let i = 0; i < len; i++) {
            this.hideWinFlagAnim(i);
            this._sprBetAreaWinFlags[i].node.active = false;
        }
    }

    initCowboyToastNode(): void {
        this._toastNode = new cc.Node();
        this.node.addChild(this._toastNode);
        this._toastNode.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
        this._toastNode.setContentSize(pf.system.view.width, pf.system.view.height);
    }

    showCowboyToast(text: string): void {
        let toastBg = new cc.Node().addComponent(cc.Sprite);
        // cv.resMgr.setSpriteFrame(toastBg.node, 'pkw/zh_CN/game/cowboy/cowboy_tips_bg');
        pf.addressableAssetManager
            .loadAsset(macros.Dynamic_Assets.COWBOY_TIPS_BACKGROUND)
            .then((asset: cc.SpriteFrame) => {
                toastBg.node.getComponent(cc.Sprite).spriteFrame = asset;
            });
        // cb.loadSpriteTextureByPlist(this.game_dznz_PLIST, toastBg, "");
        // toastBg.node.setPosition(pf.system.view.width, cv.config.HEIGHT / 2);
        this._toastNode.addChild(toastBg.node);

        let textToast = new cc.Node().addComponent(cc.Label);
        textToast.verticalAlign = cc.Label.VerticalAlign.CENTER;
        textToast.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        textToast.fontSize = 45;
        textToast.lineHeight = 45 * 2;
        // textToast.setColor(Color3B.WHITE);
        textToast.node.color = cc.color(140, 204, 156);
        // textToast.node.setPosition(toastBg.node.width / 2, toastBg.node.height / 2);
        textToast.string = text;
        toastBg.node.addChild(textToast.node);

        toastBg.node.runAction(cc.sequence(cc.delayTime(0.1), cc.moveBy(1.0, cc.v2(0, 120))));
        toastBg.node.runAction(
            cc.sequence(cc.delayTime(0.4), cc.fadeOut(0.8).easing(cc.easeInOut(1.0)), cc.destroySelf())
        );
    }

    clearAllCowboyToasts(): void {
        this._toastNode.destroyAllChildren();
        this._toastNode.removeAllChildren(true);
    }

    onLeftTimeUpdate(f32Delta: number): void {
        this._leftTime--;
        this._leftTime = this._leftTime < 0 ? 0 : this._leftTime;

        if (this._clock_total_time > 5 && this._leftTime === 5) {
            this._clock_canChange = true;
            this.handleClockChangeColor();
        }
    }

    resetLeftTimer(): void {
        this._leftTime = this._videoCowboyRoom.gameState.leftSeconds;
        this.unschedule(this.onLeftTimeUpdate);
        this.schedule(this.onLeftTimeUpdate, 1.0);
    }

    /// //////////////animation begin//////////////////
    initTimelineAnims(): void {
        // 开局动画
        this._roundStartAnim = this.initAni(this._timelineNodeAnim, this.round_start_prefab);
        this._roundStartAction = this._roundStartAnim.getComponent(cc.Animation);
        // this._roundStartAnim.runAction(this._roundStartAction);

        // // 出战动画
        this._fightBeginAnim = this.initAni(this._timelineNodeAnim, this.fight_begin_prefab);
        this._fightBeginAction = this._fightBeginAnim.getComponent(cc.Animation);
        // this._fightBeginAnim.runAction(this._fightBeginAction);

        // // 开战动画
        this._fightEndAnim = this.initAni(this._timelineNodeAnim, this.fight_end_prefab);
        this._fightEndAction = this._fightEndAnim.getComponent(cc.Animation);
        // this._fightEndAnim.runAction(this._fightEndAction);

        // // 等待下一局动画
        this._waitForNextRoundAnim = this.initAni(this._timelineNodeAnim, this.wait_for_next_round_prefab);
        let CZ_2 = this._waitForNextRoundAnim.getChildByName('CZ_2');
        if (CZ_2 && this.videoLanguage_PLIST) {
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.videoLanguage_PLIST,
            //     CZ_2.getComponent(cc.Sprite),
            //     'waitNextRound_ani'
            // );
            CZ_2.getComponent(cc.Sprite).spriteFrame = this.videoLanguage_PLIST.getSpriteFrame('waitNextRound_ani');
        }
        this._waitForNextRoundAction = this._waitForNextRoundAnim.getComponent(cc.Animation);
        // this._waitForNextRoundAnim.runAction(this._waitForNextRoundAction);
    }

    initAni(parent: cc.Node, ani_prefab: cc.Prefab): cc.Node {
        let node = cc.instantiate(ani_prefab);
        // node.setPosition(pf.system.view.width / 2, cv.config.HEIGHT / 2);
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            let prefabArr: cc.Prefab[] = [
                this.fight_begin_prefab,
                this.fight_end_prefab,
                this.wait_for_next_round_prefab
            ];
            let imgPthArr: string[] = ['CZ', 'KZ', 'xyj'];
            let prefabLen = prefabArr.length;

            for (let i = 0; i < prefabLen; i++) {
                if (ani_prefab === prefabArr[i]) {
                    // VideoCowboyManager.loadSpriteTextureByPlist(
                    //     this.en_animation_PLIST,
                    //     cc.find('CZ_2', node).getComponent(cc.Sprite),
                    //     imgPthArr[i]
                    // );
                    cc.find('CZ_2', node).getComponent(cc.Sprite).spriteFrame = this.en_animation_PLIST.getSpriteFrame(
                        imgPthArr[i]
                    );
                    break;
                }
            }
        }
        node.active = false;
        parent.addChild(node);
        return node;
    }

    initWinFlagAnims(): void {
        let len = this._sprBetAreaWinFlags.length;
        // win旗子动画
        for (let i = 0; i < len; i++) {
            let winNode = this._sprBetAreaWinFlags[i].node;
            let winAnimPrefab: cc.Prefab = null;
            if (this._platform === 'pkw') {
                winAnimPrefab = pf.addressableAssetManager.getAsset(macros.Assets.COMMON_WIN_FLAG);
            } else if (this._platform === 'wpk') {
                winAnimPrefab = pf.addressableAssetManager.getAsset(macros.Assets.WIN_FLAG);
            }
            let winAnim: cc.Node = this.initAni(this.node, winAnimPrefab);
            let winAction: cc.Animation = winAnim.getComponent(cc.Animation);
            winAnim.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE;
            let pos = winAnim.parent.convertToNodeSpaceAR(
                winNode.parent.convertToWorldSpaceAR(cc.v2(0 + winNode.x, winNode.y))
            );
            winAnim.setPosition(pos.x, pos.y - 160);
            this._winFlagAnims.push(winAnim);
        }
    }

    showWinFlagAnim(areaIdx: number): void {
        this._winFlagAnims[areaIdx].active = true;
        if (this._platform === 'pkw') {
            let action: cc.Animation = this._winFlagAnims[areaIdx].getComponent(cc.Animation);
            let animation0: cc.AnimationState = action.getAnimationState('animation0');
            let animation1: cc.AnimationState = action.getAnimationState('animation1');
            animation0.speed = this._getAnimClipSpeedByDuring(animation0.clip, this._fActExecute_WinFlag);
            animation0.wrapMode = cc.WrapMode.Normal;
            action.play();
            action.on(
                cc.Animation.EventType.FINISHED,
                (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                    action.off(cc.Animation.EventType.FINISHED);
                    animation1.wrapMode = cc.WrapMode.Loop;
                    animation1.play();
                },
                this
            );
        } else if (this._platform === 'wpk') {
            this._winFlagAnims[areaIdx].getComponent(sp.Skeleton).setAnimation(0, 'Win', true);
        }
    }

    hideWinFlagAnim(areaIdx: number): void {
        if (areaIdx >= pf.DataUtil.getArrayLength(this._winFlagAnims)) return;
        this._winFlagAnims[areaIdx].active = false;
        if (this._platform === 'pkw') {
            this._winFlagAnims[areaIdx].getComponent(cc.Animation).play();
            let vClips: cc.AnimationClip[] = this._winFlagAnims[areaIdx].getComponent(cc.Animation).getClips();
            for (const clip of vClips) {
                this._winFlagAnims[areaIdx].getComponent(cc.Animation).stop(clip.name);
            }
        } else if (this._platform === 'wpk') {
            this._winFlagAnims[areaIdx].getComponent(sp.Skeleton).setToSetupPose();
        }
    }

    showSpecialCardTypeAnim(stayLastFrame?: boolean, lastDuration?: number): void {
        const checkedIsStayLastFrame = stayLastFrame === true ? true : false;
        const checkedLastDuration = lastDuration === undefined ? 0 : lastDuration;
        this.clearSpecialCardTypeAnim();
        if (!this.isResultSpecialCardType()) return;

        let specialBetOption = -1;

        // 优先判断：金刚/同花顺/皇家
        let matchArr = this._videoCowboyRoom.roundInfo.matchOptions;
        let matchLen = matchArr.length;
        for (let i = 0; i < matchLen; i++) {
            let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
            let betOption = this.getBetOptionByAreaIdx(areaIdx);

            if (betOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
                specialBetOption = betOption;
                break;
            }
        }
        if (specialBetOption < 0) {
            let matchArr = this._videoCowboyRoom.roundInfo.matchOptions;
            let matchLen = matchArr.length;
            for (let i = 0; i < matchLen; i++) {
                let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
                let betOption = this.getBetOptionByAreaIdx(areaIdx);

                if (betOption === network.BetZoneOption.HOLE_A) {
                    specialBetOption = betOption;
                    break;
                }
            }
        }

        if (specialBetOption < 0) return;

        // 胜利牌型
        // 0 平 1 牛仔胜 -1 小牛胜
        let winLevel = 0;
        if (this._videoCowboyRoom.roundInfo.roundResult.result === 0) {
            winLevel = this._videoCowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === 1) {
            winLevel = this._videoCowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === -1) {
            winLevel = this._videoCowboyRoom.roundInfo.roundResult.blueLevel;
        }

        let specialCardType = '';
        let specialCardOdd = '';
        if (specialBetOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
            if (winLevel === 8) {
                // 金刚
                specialCardType = 'special_jingang';
                specialCardOdd = 'special_card_odd_248';
            } else if (winLevel === 9) {
                // 同花顺
                specialCardType = 'special_tonghuashun';
                specialCardOdd = 'special_card_odd_248';
            } else if (winLevel === 10) {
                // 皇家同花顺
                specialCardType = 'special_huangtong';
                specialCardOdd = 'special_card_odd_248';
            } else {
                console.log('showSpecialCardTypeAnim, show special cardtype anim error1');
                return;
            }
        } else if (specialBetOption === network.BetZoneOption.HOLE_A) {
            // 对A
            specialCardType = 'special_duia';
            specialCardOdd = 'special_card_odd_100';
        } else {
            console.log('showSpecialCardTypeAnim, show special cardtype anim error2');
            return;
        }

        let winAnim: cc.Node = this._nodeAnim.getChildByName('special_card_type_anim');
        let winAction: cc.Animation = null;
        if (!winAnim) {
            // 创建动画
            winAnim = this.initAni(this._nodeAnim, this.special_card_type_prefab);
            winAction = winAnim.getComponent(cc.Animation);
            winAnim.name = 'special_card_type_anim';
            // this._winFlagAnims.push(winAnim);
            // this._winFlagActions.push(winAction);
        }
        winAnim.active = true;
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.SPECIAL_CARD_TYPE_ATLAS);
        // let atlas: cc.SpriteAtlas =
        //     pf.languageManager.currentLanguage == cv.Enum.LANGUAGE_TYPE.zh_CN
        //         ? this.special_card_type_PLIST
        //         : this.en_animation_PLIST;
        // VideoCowboyManager.loadSpriteTextureByPlist(
        //     atlas,
        //     winAnim.getChildByName('special_card_type').getComponent(cc.Sprite),
        //     specialCardType
        // );
        winAnim.getChildByName('special_card_type').getComponent(cc.Sprite).spriteFrame =
            atlas.getSpriteFrame(specialCardType);
        // VideoCowboyManager.loadSpriteTextureByPlist(
        //     atlas,
        //     winAnim.getChildByName('special_card_odd').getComponent(cc.Sprite),
        //     specialCardOdd
        // );
        winAnim.getChildByName('special_card_odd').getComponent(cc.Sprite).spriteFrame =
            atlas.getSpriteFrame(specialCardOdd);

        if (checkedIsStayLastFrame) {
            this.gotoFrameAndPlay(winAction, 480, 480, false);
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(checkedLastDuration),
                    cc.callFunc(() => {
                        winAnim.active = false;
                        this.showBetWinFlagsAndFlyCoinsAnim();
                    })
                )
            );
        } else {
            this.playSoundEffect(this.s_special_card_type);
            this.gotoFrameAndPlay(winAction, 0, 480, false);
            winAction.on('finished', (event: cc.Event): void => {
                winAction.off('finished');
                winAnim.active = false;
                this.showBetWinFlagsAndFlyCoinsAnim();
            });
        }
    }

    isResultSpecialCardType(): boolean {
        let arr = this._videoCowboyRoom.roundInfo.matchOptions;
        let len = pf.DataUtil.getArrayLength(arr);
        for (let i = 0; i < len; i++) {
            let areaIdx = this.getAreaIdxByBetOption(arr[i]);
            let betOption = this.getBetOptionByAreaIdx(areaIdx);
            if (
                betOption === network.BetZoneOption.HOLE_A ||
                betOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4
            ) {
                return true;
            }
        }
        return false;
    }

    clearSpecialCardTypeAnim(): void {
        let special_card_type_anim = this._nodeAnim.getChildByName('special_card_type_anim');
        if (special_card_type_anim) {
            special_card_type_anim.removeFromParent(true);
            special_card_type_anim.destroy();
        }
    }

    initTrendChangeAnim(): void {
        // let frameVector: cc.SpriteFrame[] = [];
        // let frameVector: [cc.SpriteFrame] = [null];
        // for (let i = 0; i < 8; i++) {
        //     let frameName = pf.StringUtil.formatC("cowboy_trend_%d", i);
        //     let spriteFrame = this.cowboy_trend_anim_PLIST.getSpriteFrame(frameName);
        //     frameVector.push(spriteFrame);
        // }
        this.trend_anim = cc.AnimationClip.createWithSpriteFrames(this.video_cowboy_trend_PLIST.getSpriteFrames(), 10);
        this.trend_anim.wrapMode = cc.WrapMode.Loop;
    }

    showTrendChangeAnim(): void {
        this._btnZouShi.node.active = false;
        let trend_anim = this._topBg.node.getChildByName('trend_anim');
        if (trend_anim) {
            trend_anim.removeFromParent(true);
            trend_anim.destroy();
        }
        let sprTrend = new cc.Node().addComponent(cc.Sprite);
        // VideoCowboyManager.loadSpriteTextureByPlist(this.video_cowboy_trend_PLIST, sprTrend, 'cowboy_trend_0');
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_TREND_ANIM_ATLAS);
        sprTrend.spriteFrame = atlas.getSpriteFrame('cowboy_trend_0');
        sprTrend.node.name = 'trend_anim';
        sprTrend.node.setPosition(this._btnZouShi.node.x - 1, this._btnZouShi.node.y + 3);
        this._topBg.node.addChild(sprTrend.node);
        let ani = sprTrend.node.addComponent(cc.Animation);
        // sprTrend.node.addChild(ani.node);
        ani.addClip(this.trend_anim, 'trend_anim');
        ani.play('trend_anim');
        // sprTrend.node.runAction(cc.repeatForever(ani.play()));
    }

    hideTrendChangeAnim(): void {
        // this._btnZouShi.node.active = (true);
    }

    showRecordDotBezierAnim(): void {
        let lightRecordDot: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
        let sprRecordDot: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
        let bornPoint = cc.v2(0, 0);
        let ctrlPoint1 = cc.v2(0, 0);
        let ctrlPoint2 = cc.v2(0, 0);

        // 0 平 1 牛仔胜 - 1 小牛胜
        if (this._videoCowboyRoom.roundInfo.roundResult.result === 1) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, lightRecordDot, 'record_red_fire_light');
            lightRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_red_fire_light');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, sprRecordDot, 'record_red_fire');
            sprRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_red_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.RED_WIN);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition());
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(300 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(600 + bornPoint.x, 160 + bornPoint.y);
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === -1) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, lightRecordDot, 'record_blue_fire_light');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, sprRecordDot, 'record_blue_fire');
            lightRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_blue_fire_light');
            sprRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_blue_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.BLUE_WIN);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition());
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(-80 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(-120 + bornPoint.x, 160 + bornPoint.y);
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === 0) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, lightRecordDot, 'record_draw_fire_light');
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, sprRecordDot, 'record_draw_fire');
            lightRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_draw_fire_light');
            sprRecordDot.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_draw_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.EQUAL);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition());
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(80 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(160 + bornPoint.x, 160 + bornPoint.y);
        }
        if (!sprRecordDot) {
            return;
        }

        // 光效
        let endPos = this._nodeAnim.convertToNodeSpaceAR(this._lastRecordDotWorldPos);
        this._nodeAnim.addChild(lightRecordDot.node);
        lightRecordDot.node.active = false;
        lightRecordDot.node.setPosition(endPos);

        this._nodeAnim.addChild(sprRecordDot.node);
        sprRecordDot.node.setPosition(bornPoint);
        let bezierCfg = [ctrlPoint1, ctrlPoint2, endPos];

        sprRecordDot.node.scale = 0;
        sprRecordDot.node.opacity = 0;
        sprRecordDot.node.runAction(cc.scaleTo(0.3, 1.0));
        sprRecordDot.node.runAction(cc.fadeIn(0.3));

        this.showTrendChangeAnim();
        let len = pf.DataUtil.getArrayLength(this._recordDots);
        sprRecordDot.node.runAction(
            cc.sequence(
                cc.delayTime(0.32),
                cc.bezierTo(0.5, bezierCfg),
                cc.callFunc(() => {
                    // 0.5.5
                    sprRecordDot.node.removeFromParent(true);
                    sprRecordDot.node.destroy();
                    if (len > 0) {
                        this._recordDots[len - 1].node.active = true;
                    }

                    lightRecordDot.node.active = true;
                    lightRecordDot.node.runAction(
                        cc.sequence(cc.fadeOut(0.18), cc.fadeIn(0.18), cc.fadeOut(0.18), cc.destroySelf())
                    );
                })
            )
        );

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    if (len > 0) {
                        this._recordDots[len - 1].node.active = false;
                    }
                    this.showHistoryMoveAnim();
                })
            )
        );

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(2.0),
                cc.callFunc(() => {
                    this.hideTrendChangeAnim();
                })
            )
        );
    }

    // 显示玩家胜利头像框光环动画
    showWinPlayerLightAnim(uid: number): void {
        let playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            this.updatePlayerWinCount(uid, true);
            return;
        }

        for (const head of playerHeads) {
            // 自己不显示光环
            if (head === this._selfHeadBg) {
                continue;
            }

            let winPlayerLightAnim: cc.Node = head.getChildByName('win_player_light');
            if (!winPlayerLightAnim) {
                winPlayerLightAnim = this.initAni(
                    head,
                    pf.addressableAssetManager.getAsset(macros.Assets.WIN_PLAYER_LIGHT)
                );
                winPlayerLightAnim.name = 'win_player_light';
                winPlayerLightAnim.setPosition(cc.v2(0, 25));
                winPlayerLightAnim.zIndex = 10;
            }

            let winPlayerLightAction: cc.Animation = winPlayerLightAnim.getComponent(cc.Animation);

            // winPlayerLightAnim.runAction(winPlayerLightAction);
            // winPlayerLightAction.play();
            winPlayerLightAnim.active = true;
            this.gotoFrameAndPlay(winPlayerLightAction, 0, 20, true);
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    this.updatePlayerWinCount(uid, true);
                })
            )
        );
    }

    hideAllWinPlayerLightAnim(): void {
        let len = this._otherPlayerHeads.length;
        for (let i = 0; i < len; i++) {
            let node = this._otherPlayerHeads[i].bg.node.getChildByName('win_player_light');
            if (node) {
                node.removeFromParent(true);
                node.destroy();
            }
        }
    }

    // 显示玩家连胜动画
    updatePlayerWinCount(uid: number, bAnim?: boolean): void {
        const checkedIsPlayAnim = bAnim === true ? true : false;
        let playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            return;
        }

        for (const head of playerHeads) {
            // 富豪No1 和 神算子 不显示连胜
            if (this._otherPlayerHeads[0].bg.node === head || this._otherPlayerHeads[1].bg.node === head) {
                continue;
            }

            let win_player_win_count = this.node.getChildByName('win_player_win_count_' + head.uuid);
            if (win_player_win_count) {
                win_player_win_count.removeFromParent(true);
                win_player_win_count.destroy();
            }
            let keepWinCount = this._videoCowboyRoom.getPlayerKeepWinCountByUid(uid);
            if (keepWinCount >= 3) {
                keepWinCount = keepWinCount > 10 ? 11 : keepWinCount;
                let sprWinCount = new cc.Node().addComponent(cc.Sprite);
                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     sprWinCount,
                //     pf.StringUtil.formatC('win_count_%d', keepWinCount)
                // );
                sprWinCount.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(
                    pf.StringUtil.formatC('win_count_%d', keepWinCount)
                );
                sprWinCount.node.name = 'win_player_win_count_' + head.uuid;
                let offsetY = head === this._selfHeadBg ? 40 : 70;
                let tmpPos = head.convertToWorldSpaceAR(cc.v2(0, offsetY));
                tmpPos = this.node.convertToNodeSpaceAR(tmpPos);
                sprWinCount.node.setPosition(tmpPos);
                this.node.addChild(sprWinCount.node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT);

                if (bAnim) {
                    // animation
                    let targetPos = sprWinCount.node.getPosition();
                    let bornPos = targetPos;
                    let headMidWorldPos = head.getParent().convertToWorldSpaceAR(head.getPosition());
                    headMidWorldPos = head.convertToNodeSpaceAR(headMidWorldPos);
                    if (headMidWorldPos.x < pf.system.view.width / 2) {
                        bornPos = cc.v2(-200 + bornPos.x, 0 + bornPos.y);
                    } else {
                        bornPos = cc.v2(200 + bornPos.x, 0 + bornPos.y);
                    }
                    sprWinCount.node.setPosition(bornPos);
                    sprWinCount.node.runAction(
                        cc.moveTo(0.18, targetPos).easing(cc.easeBackOut()).easing(cc.easeSineOut())
                    );
                }
            }
        }
    }

    // 更新所有玩家连胜状态
    updateAllPlayerWinCount(): void {
        this.updatePlayerWinCount(this._authService.currentUser.userId);

        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._otherPlayerHeads.length; i++) {
            let otherPlayersInfo = this._videoCowboyRoom.otherPlayers;
            if (i < otherPlayersInfo.length) {
                let info = otherPlayersInfo[i];
                this.updatePlayerWinCount(info.uid);
            }
        }
    }

    // 隐藏所有timeline动画
    hideAllTimelineAnims(): void {
        this.gotoFrameZeroAndPause(this._roundStartAction);
        this._roundStartAnim.active = false;

        this.gotoFrameZeroAndPause(this._fightBeginAction);
        this._fightBeginAnim.active = false;

        this.gotoFrameZeroAndPause(this._fightEndAction);
        this._fightEndAnim.active = false;

        this.gotoFrameZeroAndPause(this._waitForNextRoundAction);
        this._waitForNextRoundAnim.active = false;

        let len = this._winFlagAnims.length;
        for (let i = 0; i < len; i++) {
            let spine: sp.Skeleton = this._winFlagAnims[i].getComponent(sp.Skeleton);
            if (spine) {
                this._winFlagAnims[i].getComponent(sp.Skeleton).setToSetupPose();
            }
            this._winFlagAnims[i].active = false;
        }
    }

    gotoFrameZeroAndPause(ani: cc.Animation) {
        // 未处理
        ani.play();
        ani.stop();
    }

    // 更新玩家金币，在金币飞到自己动画结束之前(减去当前局赢的)
    updatePlayerCoinBeforeSettle(): void {
        // 自己
        let it = this._videoCowboyRoom.roundInfo.playerSettles.get(this._authService.currentUser.userId);
        if (it) {
            // this.updateSelfCoin(it.curCoin - it.totalWinAmount);
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin - it.totalWinAmount);
        }

        // 其他玩家
        for (const head of this._otherPlayerHeads) {
            if (head.bg.node.active && head.uid > 0) {
                let it = this._videoCowboyRoom.roundInfo.playerSettles.get(head.uid);
                if (it) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin - it.totalWinAmount);
                }
            }
        }
    }

    // 更新牌
    updateAllCardsBeforeSettle(): void {
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        /* let publicCardNum = this._videoCowboyRoom.publicCards.length;
        for (let i = 0; i < 1; i++)
        {
            if (i < publicCardNum)
            {
                this._publicCards[i].SetContent((this._videoCowboyRoom.publicCards[i].number), (this._videoCowboyRoom.publicCards[i].suit));
                this._publicCards[i].SetFace(true);
            }
        } */
    }

    // 更新所有牌，除了公共牌
    updateAllCardsExceptPublicBeforeSettle(): void {
        // update cards
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        // 更新所有正面牌
        if (
            this._videoCowboyRoom.roundInfo.redHandCards.length === 2 &&
            this._videoCowboyRoom.roundInfo.blueHandCards.length === 2
        ) {
            for (let i = 0; i < 2; i++) {
                this._redHandCards[i].SetContent(
                    this._videoCowboyRoom.roundInfo.redHandCards[i].number,
                    this._videoCowboyRoom.roundInfo.redHandCards[i].suit
                );
                this._blueHandCards[i].SetContent(
                    this._videoCowboyRoom.roundInfo.blueHandCards[i].number,
                    this._videoCowboyRoom.roundInfo.blueHandCards[i].suit
                );
                this._redHandCards[i].SetFace(true);
                this._blueHandCards[i].SetFace(true);
            }
        }

        /* let publicCardNum = this._videoCowboyRoom.publicCards.length;
        for (let i = 0; i < 1; i++)
        {
            if (i < publicCardNum)
            {
                this._publicCards[i].SetContent((this._videoCowboyRoom.publicCards[i].number), (this._videoCowboyRoom.publicCards[i].suit));
                this._publicCards[i].SetFace(true);
            }
        } */
    }

    // 清除没有中的区域金币
    clearLoseBetCoins(): void {
        let len = this._betCoinContents.length;
        let arr = this._videoCowboyRoom.roundInfo.matchOptions;
        let matchLen = this._videoCowboyRoom.roundInfo.matchOptions.length;
        for (let i = 0; i < len; i++) {
            let betOption = this.getBetOptionByAreaIdx(i);
            let isOptionWin = false;
            for (let i = 0; i < matchLen; i++) {
                if (betOption === arr[i]) {
                    isOptionWin = true;
                    break;
                }
            }

            if (!isOptionWin) {
                this._betCoinContents[i].destroyAllChildren();
                this._betCoinContents[i].removeAllChildren(true);
                console.log('clearLoseBetCoins-.清理areaIndex = ' + i);
                this.hideAreaCoin(i, false);
            }
        }
    }

    // 开局动画
    showRoundStartAnim(): void {
        // this._roundStartAnim.active = (true);
        // this.gotoFrameAndPlay(this._roundStartAction, 0, 90, false);
        // this._roundStartAction.on("finished", (event: cc.Event): void => {
        //     this._roundStartAction.off("finished");
        this._roundStartAnim.active = false;
        this.sendCardsAnim();
        // }, this);
    }

    // 发牌动画
    sendCardsAnim(): void {
        this.setAllCardsVisible(false);
        this.updateAllCardsBack();

        let handCards: PokerCardControl[] = [];
        let handCardsTargetPos: cc.Vec2[] = [];
        let redOffset = cc.v2(30, -30);
        let blueOffset = cc.v2(-30, -30);
        let publicOffset = cc.v2(-30, -30);
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].node.setPosition(
                this._oriRedHandCardsPos[i].x + redOffset.x,
                this._oriRedHandCardsPos[i].y + redOffset.y
            );
            this._blueHandCards[i].node.setPosition(
                this._oriBlueHandCardsPos[i].x + blueOffset.x,
                this._oriBlueHandCardsPos[i].y + blueOffset.y
            );
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].node.setPosition(
                this._oriPublicCardsPos[i].x + publicOffset.x,
                this._oriPublicCardsPos[i].y + publicOffset.y
            );
            this._publicCards[i].SetFace(false);
        }
        handCards.push(this._redHandCards[0]);
        handCards.push(this._blueHandCards[0]);
        handCards.push(this._redHandCards[1]);
        handCards.push(this._blueHandCards[1]);
        handCardsTargetPos.push(this._oriRedHandCardsPos[0]);
        handCardsTargetPos.push(this._oriBlueHandCardsPos[0]);
        handCardsTargetPos.push(this._oriRedHandCardsPos[1]);
        handCardsTargetPos.push(this._oriBlueHandCardsPos[1]);
        // 发手牌动画
        let duration = 0.14;
        let easeRate = 1.0;
        let len = handCards.length;
        for (let i = 0; i < len; i++) {
            let moveAction = cc.moveTo(duration, handCardsTargetPos[i]).easing(cc.easeInOut(easeRate));
            let showAction = cc.callFunc(() => {
                handCards[i].node.active = true;
                handCards[i].SetFace(false);
            });
            let moveComplete = cc.callFunc(() => {
                if (i === len - 1) {
                    // 发公共牌动画
                    for (let j = 0; j < 5; j++) {
                        let publicMoveAction = cc
                            .moveTo(duration, this._oriPublicCardsPos[j])
                            .easing(cc.easeInOut(easeRate));
                        let publicShowAction = cc.callFunc(() => {
                            this._publicCards[j].node.active = true;
                            this._publicCards[j].SetFace(false);
                            // this.playCowboyEffect(this.s_fapai);
                        });
                        let publicMoveComplete = cc.callFunc(() => {
                            if (j === 4) {
                                // 翻第一张公共牌动画
                                // let publicCardNum = this._videoCowboyRoom.publicCards.length;
                                // if (publicCardNum > 0)
                                // {
                                /* SEInt32 eNum = this._videoCowboyRoom.publicCards[0].number();
                                SEInt32 eSuit = this._videoCowboyRoom.publicCards[0].suit();
                                this._publicCards[0]..node.active = (true);
                                this._publicCards[0].SetContent((eNum), (eSuit));
                                this._publicCards[0].SetFace(false);
                                this._publicCards[0].Turn(true);
                                this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.2), cc.callFunc(function() {
                                    this.playCowboyEffect(this.s_kaipai);
                                }))); */
                                // 开战动画
                                // this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.2), cc.callFunc(function () {
                                //     this.showFightBeginAnim();
                                // }.bind(this))));
                                // }
                            }
                        });
                        this.scheduleOnce(() => {
                            this._publicCards[j].node.active = true;
                            this._publicCards[j].node.runAction(
                                cc.sequence(publicShowAction, publicMoveAction, publicMoveComplete)
                            );
                        }, j * duration);
                    }
                }
            });
            this.scheduleOnce(() => {
                handCards[i].node.active = true;
                handCards[i].node.runAction(cc.sequence(showAction, moveAction, moveComplete));
            }, i * duration);

            // this.scheduleOnce(function () {
            //     this.playCowboyEffect(this.s_fapai);
            // }.bind(this), 0.04 + i * duration);
        }
    }

    // 翻手牌动画
    showHandCardsAnim(): void {
        this.showHideLoseBetCoinsAnim();
        return;
        // TODO 原邏輯 return 了
        // 显示所有牌
        // this.setAllCardsVisible(true);

        // 翻手牌
        // if (this._videoCowboyRoom.redHandCards.length == 2 && this._videoCowboyRoom.blueHandCards.length == 2) {
        //     for (let i = 0; i < 2; i++) {
        //         this._redHandCards[i].SetContent(
        //             this._videoCowboyRoom.redHandCards[i].number,
        //             this._videoCowboyRoom.redHandCards[i].suit
        //         );
        //         // this._redHandCards[i].node.runAction(cc.sequence(cc.rotateTo(0.3, 0, 180), cc.callFunc(()=>{
        //         //     this._redHandCards[i].node.setRotation(0);
        //         this._redHandCards[i].SetFace(true); //})));

        //         this._blueHandCards[i].SetContent(
        //             this._videoCowboyRoom.blueHandCards[i].number,
        //             this._videoCowboyRoom.blueHandCards[i].suit
        //         );
        //         this._blueHandCards[i].node.runAction(
        //             cc.sequence(
        //                 cc.delayTime(0.5),
        //                 cc.callFunc(() => {
        //                     //, cc.rotateTo(0.3, 0, 180)
        //                     // this._blueHandCards[i].node.setRotation(0);
        //                     this._blueHandCards[i].SetFace(true);
        //                 })
        //             )
        //         );
        //         // this._blueHandCards[i].Turn(true, 0.5);

        //         if (i == 0) {
        //             this.playCowboyEffect(this.s_kaipai);
        //             this._nodeAnim.runAction(
        //                 cc.sequence(
        //                     cc.delayTime(0.5),
        //                     cc.callFunc(
        //                         function () {
        //                             this.playCowboyEffect(this.s_kaipai);
        //                         }.bind(this)
        //                     )
        //                 )
        //             );
        //         }
        //     }
        // }

        // 翻公共牌
        // this._nodeAnim.runAction(
        //     cc.sequence(
        //         cc.delayTime(this._showHandCardsDuration),
        //         cc.callFunc(() => {
        //             this.showPublicCardsAnim();
        //         })
        //     )
        // );
    }

    // 翻公共牌动画
    showPublicCardsAnim(): void {
        let publicCardNum = this._videoCowboyRoom.roundInfo.publicCards.length;
        for (let i = 0; i < 5; i++) {
            if (i < publicCardNum) {
                let flopOverFunc = cc.callFunc(() => {
                    if (i === 4) {
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(0.3),
                                cc.callFunc(() => {
                                    this.hideGameTips();
                                    this.showCardTypeAnim();
                                    this.updateWinCards();
                                    this.showCowboyLoseAnim();
                                    // showHistoryMoveAnim();
                                })
                            )
                        );
                        this.playSoundEffect(this.s_kaipai);
                    }
                });
                this._publicCards[i].SetFace(false);
                this._publicCards[i].node.runAction(
                    cc.sequence(
                        cc.moveTo(0.3, this._oriPublicCardsPos[0]),
                        cc.callFunc(() => {
                            this._publicCards[i].SetContent(
                                this._videoCowboyRoom.roundInfo.publicCards[i].number,
                                this._videoCowboyRoom.roundInfo.publicCards[i].suit
                            );
                            this._publicCards[i].SetFace(true);
                        }),
                        cc.moveTo(0.2, this._oriPublicCardsPos[i]),
                        flopOverFunc
                    )
                );
            }
        }

        // 隐藏没有中的区域金币动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._showPublicCardsDuration),
                cc.callFunc(() => {
                    this.showHideLoseBetCoinsAnim();
                })
            )
        );
    }

    // 翻牌型动画
    showCardTypeAnim(): void {
        this.updateCardType();

        this._redCardType.node.scale = 0;
        this._blueCardType.node.scale = 0;
        this._redCardTypeBg.node.scale = 0;
        this._blueCardTypeBg.node.scale = 0;
        let scaleAction = cc.scaleTo(0.18, 1.0).easing(cc.easeInOut(1.0));
        this._redCardType.node.runAction(scaleAction);
        this._blueCardType.node.runAction(scaleAction.clone());
        this._redCardTypeBg.node.runAction(scaleAction.clone());
        this._blueCardTypeBg.node.runAction(scaleAction.clone());

        this._videoCowboyRoom.showTheNewestTrend = true;
    }

    // 出战动画
    showFightBeginAnim(): void {
        // this._fightBeginAnim..node.active = (true);
        // this._fightBeginAction.gotoFrameAndPlay(0, 60, false);
        // this._fightBeginAction..on("finished", (event: cc.Event): void =>
        // {
        //	this._fightBeginAnim.active = false;
        // });
        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.4), cc.callFunc(function() {
        //	//this.playCowboyEffect(s_chuzhan_kaizhan);
        //	this.playCowboyEffect(this.s_begin_bet);
        // })));

        this.playSoundEffect(this.s_begin_bet);
        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.5), cc.callFunc(function() {
        // this._fightBeginAnim.active = (true);
        // this.gotoFrameAndPlay(this._fightBeginAction, 0, 60, false);
        // this._fightBeginAction.on("finished", (event: cc.Event): void => {
        //     this._fightBeginAction.off("finished");
        this._fightBeginAnim.active = false;
        this.showBetCoutDownBeginAnim();
        // }, this);
        // })));
    }

    // 开战动画
    showFightEndAnim(): void {
        // 开战动画.翻牌动画.显示win标记，金币收回动画.等待下一局动画
        this._fightEndAnim.active = true;
        this.gotoFrameAndPlay(this._fightEndAction, 0, 60, false);
        this._fightEndAction.on('finished', (event: cc.Event): void => {
            this._fightEndAction.off('finished');
            this._fightEndAnim.active = false;
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3),
                    cc.callFunc(() => {
                        // 翻牌动画
                        this.showHandCardsAnim();
                    })
                )
            );
        });
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.4),
                cc.callFunc(() => {
                    // this.playCowboyEffect(s_chuzhan_kaizhan);
                    this.playSoundEffect(this.s_end_bet);
                })
            )
        );
    }

    // 下注倒计时开始动画
    showBetCoutDownBeginAnim(): void {
        this.updateBetCoutDown();

        if (this._leftTime > 0) {
            this.playKaiJu();
            this._updateBetButtonState();

            // 检测是否正在使用高级续投
            this._checkAdvanceAutoReq();
        }
        // 动画
        // this._betCountDownBg.node.stopAllActions();
        // this._betCountDownBg.node.setPosition(this._betCountDownBg.node.x, cv.config.HEIGHT);
        // this._betCountDownBg.node.runAction(cc.moveTo(0.5, this._oriBetCountDownBgPos).easing(cc.easeBackOut()));
    }

    // 下注倒计时结束动画
    showBetCoutDownEndAnim(): void {
        this._updateBetButtonState();
        this.showOpenCardTips();

        // 动画
        // this._betCountDownBg.node.stopAllActions();
        // let move = cc.moveTo(0.5, cc.v2(this._oriBetCountDownBgPos.x, cv.config.HEIGHT)).easing(cc.easeBackIn());
        // let pkCall = cc.callFunc(function () {
        this.hideBetCountDown();
        this.showBetClock(false);
        this.playStopXiazhu();
        // }.bind(this));
        // this._betCountDownBg.node.runAction(cc.sequence(move, pkCall));

        // // 开战动画
        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(this._betCountDownEndDuration), cc.callFunc(function () {
        //     this.showFightEndAnim();
        // }.bind(this))));
    }

    // 显示隐藏没有中的区域金币动画
    showHideLoseBetCoinsAnim(): void {
        let arr = this._videoCowboyRoom.roundInfo.matchOptions;
        let len = pf.DataUtil.getArrayLength(arr);
        let betLen = pf.DataUtil.getArrayLength(this._betCoinContents);
        for (let i = 0; i < betLen; i++) {
            let betOption = this.getBetOptionByAreaIdx(i);
            let isOptionWin = false;
            for (let i = 0; i < len; i++) {
                if (betOption === arr[i]) {
                    isOptionWin = true;
                    break;
                }
            }

            if (!isOptionWin) {
                console.log('清理areaIndex = ' + i);
                this.hideAreaCoin(i);
            }
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._hideLoseBetCoinsDuration),
                cc.callFunc(() => {
                    // 特殊牌型动画
                    if (this.isResultSpecialCardType()) {
                        this.showSpecialCardTypeAnim();
                    } else {
                        // 显示win标记/金币回收动画
                        this.showBetWinFlagsAndFlyCoinsAnim();
                    }
                })
            )
        );
    }

    hideAreaCoin(areaIdx: number, isRunAction?: boolean) {
        const checkedIsRunAction = isRunAction === false ? false : true;
        let areaCoin = this._coinNodeByArea[areaIdx];
        let coinLen = pf.DataUtil.getArrayLength(areaCoin);
        for (let i = coinLen - 1; i >= 0; i--) {
            let node = areaCoin[i];
            // node.runAction(cc.sequence(cc.fadeOut(0.5), cc.hide()));
            if (checkedIsRunAction) {
                node.runAction(cc.fadeOut(0.5));
            } else {
                node.opacity = 0;
            }
            // node.active = false;
        }
    }

    handleCoin() {
        // let coinLen = this._coinNode.childrenCount;
        // this._circleCoinArr = [];
        // this._squareCoinArr = [];
        let len = this._coinNode.childrenCount;
        let arr = this._coinNode.children;
        for (let i = len - 1; i >= 0; i--) {
            arr[i].active = true;
            arr[i].stopAllActions();
            this.nodePutToPool(arr[i]);
        }
        let areaLen = this._coinNodeByArea.length;
        for (let i = 0; i < areaLen; i++) {
            this._coinNodeByArea[i] = [];
        }
        // this._coinNode.removeAllChildren(true);
        // for (let i = coinLen - 1; i >= 0; i--) {
        //     let node = this._coinNode.children[i];
        //     node.active = false;
        //     let name = node.name;
        //     if (name == "btnBet_0") {
        //         this._circleCoinArr.push(node);
        //     }
        //     else {
        //         this._squareCoinArr.push(node);
        //     }
        // }
    }

    // 显示win标记，金币收回动画
    showBetWinFlagsAndFlyCoinsAnim(): void {
        // 显示win标记/隐藏区域数字和金币
        let matchArr = this._videoCowboyRoom.roundInfo.matchOptions;
        let matchLen = pf.DataUtil.getArrayLength(matchArr);

        for (let i = 0; i < matchLen; i++) {
            let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
            this.clearBetArea(areaIdx);
            this.showWinFlagAnim(areaIdx);
        }

        // 路单曲线动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    this._showAllWayOutAnim();
                    this.showRecordDotBezierAnim();
                })
            )
        );

        // 动画:在哪些选项赢了(增加除主界面8个人输赢外其它玩家列表的输赢)
        let tmpSettles = this._videoCowboyRoom.roundInfo.playerSettles;
        tmpSettles.set(0xffffffff, this._videoCowboyRoom.roundInfo.otherPlayersSettle);
        tmpSettles.forEach((value: PlayerSettle, key: number): void => {
            let uid = key;
            let playerHeads: cc.Node[] = this.getPlayerCoinNodesByUid(uid);

            // 桌面没有该玩家
            if (playerHeads.length === 0) {
                console.log('playerSettles uid: %d not in gameplayers, use player list button', uid);
                playerHeads.push(this._btnPlayerList.node);
            }

            for (let i = 0; i < playerHeads.length; i++) {
                // 自己是富豪/神算子， 只回收一次金币到自己头像
                if (uid === this._authService.currentUser.userId && i > 0) {
                    continue;
                }

                let headImg = playerHeads[i];
                let worldPos = headImg.parent.convertToWorldSpace(headImg.getPosition());
                console.log('世界坐标系---------.' + worldPos);
                let headMidWorldPos = headImg.parent.convertToWorldSpaceAR(headImg.getPosition());
                headMidWorldPos = this._nodeAnim.convertToNodeSpaceAR(headMidWorldPos);
                let zoneSettleDetails = value.settle;
                for (const detail of zoneSettleDetails) {
                    let zoneSettleDetail = detail;
                    if (zoneSettleDetail.winAmount > 0) {
                        let option = zoneSettleDetail.option;
                        let areaIdx = this.getAreaIdxByBetOption(option);

                        // 自己赢显示win标记,隐藏区域数字
                        let coinContent = this._betLineNode[areaIdx];
                        let sz = coinContent.getContentSize();
                        let betDetails = this.getBetDetailAmounts(zoneSettleDetail.winAmount);
                        let len = betDetails.length;
                        for (let k = 0; k < len; k++) {
                            let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, betDetails[k], true);
                            let coinFlyBorn = coinContent.convertToWorldSpaceAR(
                                this.getOneAreaPos(areaIdx, this.isCircleCoin(betDetails[k]))
                            );
                            coinFlyBorn = this._nodeAnim.convertToNodeSpaceAR(coinFlyBorn);
                            this._nodeAnim.addChild(flyCoin.node);
                            flyCoin.node.setPosition(coinFlyBorn);
                            // flyCoin.node.active = false;

                            // 延迟一会(win动画结束)开始飞金币
                            this.scheduleOnce(() => {
                                flyCoin.node.active = true;
                                flyCoin.node.runAction(
                                    cc.sequence(
                                        cc.delayTime(0.2 + k * 0.025),
                                        cc.moveTo(0.6, headMidWorldPos).easing(cc.easeOut(0.8)),
                                        cc.callFunc(() => {
                                            this.nodePutToPool(flyCoin.node);
                                        })
                                    )
                                );
                            }, 0.7);
                        }
                    }
                }

                // 总共赢的
                let totalWinAmount = value.totalWinAmount;
                if (totalWinAmount >= 100) {
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(0.3),
                            cc.callFunc(() => {
                                let winToastBg = new cc.Node().addComponent(cc.Sprite);
                                // VideoCowboyManager.loadSpriteTextureByPlist(
                                //     this.game_dznz_PLIST,
                                //     winToastBg,
                                //     'win_coin_bg'
                                // );
                                winToastBg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('win_coin_bg');
                                winToastBg.node.setPosition(headMidWorldPos);
                                console.log(
                                    '世界坐标系---------.' + worldPos + ', ' + (pf.system.view.width / 2).toString()
                                );
                                console.log(headImg !== this._selfCoin.node);
                                if (headImg === this._btnPlayerList.node) {
                                    winToastBg.node.setAnchorPoint(cc.v2(1, 0.5));
                                } else {
                                    if (worldPos.x < pf.system.view.width / 2) {
                                        if (headImg !== this._selfCoin.node) {
                                            winToastBg.node.setAnchorPoint(cc.v2(0, 0.5));
                                        }
                                    } else {
                                        winToastBg.node.setAnchorPoint(cc.v2(1, 0.5));
                                        // winToastBg.node.setPosition(headMidWorldPos.x-winToastBg.node.width, 0);
                                    }
                                }

                                this._nodeAnim.addChild(winToastBg.node);

                                // std.string formatCoin = this.getShortOwnCoinString(totalWinAmount);
                                // TextBMFont* textWinToast = cocos2d.ui.TextBMFont.create("+" + formatCoin, "cowboy/fnt/win_num.fnt");
                                let textWinToast = new cc.Node().addComponent(cc.Label);
                                textWinToast.font = pf.addressableAssetManager.getAsset(macros.Assets.WIN_NUM_FNT);
                                textWinToast.fontSize = 10;
                                textWinToast.string =
                                    '+' +
                                    cr.CurrencyUtil.clientAmountToDisplayString(
                                        cr.CurrencyUtil.convertToClientAmount(totalWinAmount)
                                    );
                                winToastBg.node.addChild(textWinToast.node);
                                textWinToast.node.scale = 1.4;
                                if (headImg === this._btnPlayerList.node) {
                                    textWinToast.node.setPosition(-winToastBg.node.width / 2, 0);
                                } else {
                                    if (worldPos.x > pf.system.view.width / 2) {
                                        textWinToast.node.setPosition(-winToastBg.node.width / 2, 0);
                                    } else if (headImg !== this._selfCoin.node) {
                                        textWinToast.node.setPosition(winToastBg.node.width / 2, 0);
                                    }
                                }

                                winToastBg.node.runAction(
                                    cc.sequence(cc.delayTime(1.3), cc.moveBy(1.5, cc.v2(0, 50)), cc.destroySelf())
                                );
                                winToastBg.node.runAction(
                                    cc.sequence(cc.delayTime(1.4), cc.fadeOut(1.5).easing(cc.easeInOut(1.0)))
                                );
                                textWinToast.node.runAction(
                                    cc.sequence(cc.delayTime(1.4), cc.fadeOut(1.5).easing(cc.easeInOut(1.0)))
                                );
                                this._nodeAnim.runAction(
                                    cc.sequence(
                                        cc.delayTime(0.3),
                                        cc.callFunc(() => {
                                            this.updateRoundEndPlayerCoin();
                                        })
                                    )
                                );

                                this.playSoundEffect(this.s_get_win_coin);
                            })
                        )
                    );

                    // 赢的玩家头像光环
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(0.7),
                            cc.callFunc(() => {
                                this.showWinPlayerLightAnim(uid);
                            })
                        )
                    );
                } else {
                    // 更新玩家连胜状态
                    this.updateRoundEndPlayerCoin();
                    this.updatePlayerWinCount(uid, true);
                }
            }
        });

        // 维护状态:非0代表系统即将维护
        if (this._videoCowboyRoom.roundInfo.stopWorld !== 0) {
            let bTrue = this._videoCowboyRoom.roundInfo.idleRoomId > 0;
            if (!bTrue) {
                this.showCowboyToast(pf.languageManager.getString('Cowboy_server_will_stop_text'));
            }
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(this._betWinFlagsAndFlyCoinsDuration),
                    cc.callFunc(() => {
                        if (bTrue) {
                            this.showSwitchTable();
                        } else {
                            this.backToCowboyListScene();
                        }
                    })
                )
            );
        } else {
            // 下一局即将开始
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(this._betWinFlagsAndFlyCoinsDuration),
                    cc.callFunc(() => {
                        // cv.MessageCenter.send('on_videoCowboy_willstart_notify');
                        this.showNextRoundTips();
                    })
                )
            );
        }
    }

    // 单局结束时更新玩家金币
    updateRoundEndPlayerCoin(): void {
        // 自己
        let it = this._videoCowboyRoom.roundInfo.playerSettles.get(this._authService.currentUser.userId);
        if (it) {
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin);
        }

        // 其他玩家
        for (const head of this._otherPlayerHeads) {
            if (head.bg.node.active && head.uid > 0) {
                let it = this._videoCowboyRoom.roundInfo.playerSettles.get(head.uid);
                if (it) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin);
                }
            }
        }
    }

    getBetChipIdx(coin: number): number {
        let ret = -1;
        let amountLevels = this._videoCowboyRoom.roomParams.amountLevel;
        for (let i = 0; i < amountLevels.length; i++) {
            if (coin === amountLevels[i]) {
                return i;
            }
        }
        return ret;
    }

    getBetDetailAmounts(gold: number): number[] {
        let vBetCoinOption: number[] = this._videoCowboyRoom.betSettings.betCoinOptions;
        return MiniGameCommonDef.disinteBetAmounts(gold, vBetCoinOption);
    }

    getAllBetDetailAmounts(option: number): number[] {
        // let it = this._videoCowboyRoom.allZoneBet.get(option);
        // if (it) {
        //     return it;
        // } else {
        //     let vec: number[] = [];
        //     return vec;
        // }
        const betZone = this._videoCowboyRoom.betZones.get(option);
        if (betZone) {
            return betZone.optionInfo.amounts;
        } else {
            return [];
        }
    }

    nodePutToPool(node: cc.Node): void {
        if (!node) return;

        console.log('#########################nodePutToPool#############################' + this._circlePool.size());
        let name = node.name;
        if (name === 'btnBet_0') {
            this._circlePool.put(node);
        } else if (name === 'btnBet_3') {
            this._squarePool.put(node);
        }
    }

    getRandPos(sz: cc.Size, flyCoin: cc.Node): cc.Vec2 {
        let width_offset = flyCoin.width * 0.3;
        let height_offset = flyCoin.height * 0.3;
        return cc.v2(
            this.SERangeRandomf(0 + width_offset, sz.width - width_offset) - sz.width * 0.5,
            this.SERangeRandomf(0 + height_offset, sz.height - height_offset) - sz.height * 0.5
        );
    }

    createFlyCoin(areaIdx: number, coin: number, isWin?: boolean): cc.Sprite {
        const checkedIsWin = isWin === true ? true : false;
        let isCircleCoin = this.isCircleCoin(coin);
        let node: cc.Node = null;

        let len = pf.DataUtil.getArrayLength(this._coinNodeByArea[areaIdx]);
        if (len >= this._areaCoinMax[areaIdx]) {
            let removeNode = this._coinNodeByArea[areaIdx][0];
            this.nodePutToPool(removeNode);
            this._coinNodeByArea[areaIdx].splice(0, 1);
        }

        if (isCircleCoin) {
            if (this._circlePool.size() > 0) {
                node = this._circlePool.get();
            } else {
                node = cc.instantiate(this.btnBet_0_prefab);
            }
        } else {
            if (this._squarePool.size() > 0) {
                node = this._squarePool.get();
            } else {
                node = cc.instantiate(this.btnBet_3_prefab);
            }
        }

        if (!checkedIsWin) {
            this._coinNode.addChild(node);

            this._coinNodeByArea[areaIdx].push(node);
        }
        node.scale = this._fFlyCoinScaleRate;
        node.active = true;
        node.opacity = 255;

        this.setCoinText(node.getChildByName('textBet'), cr.CurrencyUtil.convertToClientAmount(coin), true, true);
        return node.getComponent(cc.Sprite);
    }

    setCoinText(node: cc.Node, num: number, isYellow: boolean, isFlyCoin: boolean = false): void {
        let str = cr.CurrencyUtil.applyDisplayRatioToFormattedString(num);
        let len = pf.DataUtil.getArrayLength(str);
        node.setContentSize(30 * len, 48);
        // cv.resMgr.adaptWidget(node);
        cr.UIUtil.adaptWidget(node);
        node.destroyAllChildren();
        node.removeAllChildren(true);
        let scale = 1;
        if (isFlyCoin) {
            const llAmountLevel = this._videoCowboyRoom.llCoinUICritical;
            scale = 0.7;
            if (num >= llAmountLevel) {
                // 金砖筹码
                if (num >= 1000) {
                    scale = 0.4;
                }
            } else {
                if (num >= 10000) {
                    scale = 0.25;
                } else if (num >= 1000) {
                    scale = 0.3;
                } else if (num >= 100) {
                    scale = 0.45;
                } else {
                    scale = 0.5;
                }
            }
        }
        for (let i = 0; i < len; i++) {
            let tempNode = new cc.Node();
            tempNode.setContentSize(30, 48);
            node.addChild(tempNode);
            node.scale = scale;
            let spr = new cc.Node().addComponent(cc.Sprite);
            spr.spriteFrame = this.game_dznz_PLIST.getSpriteFrame(
                (isYellow ? 'coin_yellow_' : 'coin_gray_') + str.charAt(i)
            );
            tempNode.addChild(spr.node);
        }
    }

    isCircleCoin(gold: number): boolean {
        let llRealGold = cr.CurrencyUtil.convertToClientAmount(gold);
        // let fileName = llRealGold < this._videoCowboyRoom.llCoinUICritical ? "bet_coin_clicked" : "bet_block_clicked";
        return llRealGold < this._videoCowboyRoom.llCoinUICritical;
    }

    // 下注动画，金币飞到池中动画
    showBetInAnim(oneBet: PlayerOneBet, ignoreEffect?: boolean): void {
        const checkedIsIgnoreEffect = ignoreEffect === true ? true : false;
        // let oneBet = this._videoCowboyRoom.curPlayerBet;
        this.updatePlayerCoin(oneBet.uid);
        this.updateBetArea(oneBet.betOption);

        // 下注动画
        let areaIdx = this.getAreaIdxByBetOption(oneBet.betOption);
        let playerHeads = this.getPlayerCoinNodesByUid(oneBet.uid);
        if (playerHeads.length === 0) {
            console.log('this.showBetInAnim, cannot find valid headBg, use btnPlayerList, oneBet.uid: %d', oneBet.uid);
            playerHeads.push(this._btnPlayerList.node);
        }

        // 自己是富豪/神算子时，下注的筹码只显示一个
        let isCircleCoin = this.isCircleCoin(oneBet.betAmount);
        let coinContent = this._betCoinContents[areaIdx];
        let sz = coinContent.getContentSize();

        for (let i = 0; i < playerHeads.length; i++) {
            let fromHead = playerHeads[i];
            let coinFlyWorldBorn = fromHead.getParent().convertToWorldSpaceAR(fromHead.getPosition());

            // 发射摇头动画
            // if (fromHead != this._selfHeadBg && fromHead != this._btnPlayerList.node) {
            //     let headImg = fromHead.getChildByName(this._HEAD_IMG_TAG);
            //     if (headImg) {
            //         let ac: cc.Action = null;
            //         if (coinFlyWorldBorn.x < pf.system.view.width / 2) {
            //             ac = cc.sequence(
            //                 cc.moveBy(0.1, cc.v2(-30, 0)),
            //                 cc.moveBy(0.1, cc.v2(30, 0)).easing(cc.easeInOut(1.0)));
            //         }
            //         else {
            //             ac = cc.sequence(
            //                 cc.moveBy(0.1, cc.v2(30, 0)),
            //                 cc.moveBy(0.1, cc.v2(-30, 0)).easing(cc.easeInOut(1.0)));
            //         }

            //         let circleHeadNode = CircleSprite.getHeadNode(headImg);
            //         if (!circleHeadNode) {
            //             headImg.runAction(ac);
            //         }
            //         else {
            //             circleHeadNode.runAction(ac);
            //         }
            //     }
            // }

            // 富豪和神算子是自己的情况，只下一个金币和播放一次音效
            if (oneBet.uid === this._authService.currentUser.userId && i > 0) continue;

            // 下注音效
            if (!checkedIsIgnoreEffect) {
                if (isCircleCoin) {
                    this.playSoundEffect(this.s_betin);
                } else {
                    this.playSoundEffect(this.s_betin_many);
                }
            }

            let coinFlyBorn = this._coinNode.convertToNodeSpaceAR(coinFlyWorldBorn);
            let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, oneBet.betAmount);

            let coinFlyTargetPos: cc.Vec2 = this._betLineNode[areaIdx].convertToWorldSpaceAR(
                this.getOneAreaPos(areaIdx, isCircleCoin)
            );
            coinFlyTargetPos = flyCoin.node.parent.convertToNodeSpaceAR(coinFlyTargetPos);
            flyCoin.node.setPosition(coinFlyBorn);
            // flyCoin.node.active = false;
            // this.scheduleOnce(function () {
            //     flyCoin.node.active = true;
            if (i === 0) {
                flyCoin.node.runAction(
                    cc.sequence(cc.moveTo(0.3, coinFlyTargetPos), cc.rotateBy(0.15, 180), cc.rotateBy(0.15, 180))
                );
            } else {
                flyCoin.node.runAction(
                    cc.sequence(
                        cc.moveTo(0.3, coinFlyTargetPos),
                        cc.callFunc(() => {
                            flyCoin.node.active = false;
                        })
                    )
                );
            }
            // }.bind(this), 0.13);
        }
    }

    SERangeRandomf(min: number, max: number): number {
        return min + Math.random() * (max - min);
    }

    // 等待下一局动画
    showWaitForNextRoundInAnim(): void {
        this._waitForNextRoundAnim.active = true;
        this.gotoFrameAndPlay(this._waitForNextRoundAction, 0, 30, false);
    }

    showWaitForNextRoundOutAnim(): void {
        if (this._waitForNextRoundAnim && this._waitForNextRoundAnim.active) {
            this._waitForNextRoundAnim.runAction(
                cc.sequence(
                    cc.moveTo(0.3, cc.v2(pf.system.view.width * 1.25, 0)),
                    cc.callFunc(() => {
                        this._waitForNextRoundAnim.active = false;
                        this._waitForNextRoundAnim.setPosition(cc.v2(0, 0));
                    })
                )
            );
        }
    }

    gotoFrameAndPlay(ani: cc.Animation, startIndex: number, endIndex: number, loop: boolean) {
        ani.play();
    }
    // /////////////////animation end//////////////////

    initBetArea(): void {
        // let cv.config.IS_WIDESCREEN
        let bet_content_bg_ipx = this._gameContent.getChildByName('bet_content_bg_ipx');
        let bet_content_bg = this._gameContent.getChildByName('bet_content_bg');
        let bet_content_bg_ipad = this._gameContent.getChildByName('bet_content_bg_ipad');
        console.log('--------------------------.###########' + bet_content_bg_ipx.width);
        let fTotalWidth = 0;
        // fTotalWidth += 2 * pf.system.view.iphoneXOffset;
        fTotalWidth += bet_content_bg_ipx.width;
        // fTotalWidth += (157 - 25);
        // fTotalWidth += (157 - 25);

        const canvas = this.getComponent(cc.Canvas);

        const fitScale = canvas.fitWidth
            ? pf.system.view.width / canvas.designResolution.width
            : pf.system.view.height / canvas.designResolution.height;

        this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;
        if (pf.system.view.isNarrowScreen()) {
            let fTotalWidth = 0;
            // fTotalWidth += 2 * pf.system.view.iphoneXOffset;
            fTotalWidth += bet_content_bg_ipx.width;
            // right and left player panel?
            // fTotalWidth += 157 - 25;
            // fTotalWidth += 157 - 25;
            if (fTotalWidth * fitScale <= pf.system.view.width) {
                this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW;
            }
        } else if (pf.system.view.isWideScreen()) {
            let fTotalHeight = 0;
            fTotalHeight += bet_content_bg_ipad.height;
            fTotalHeight += this._bottomPanel.height;
            fTotalHeight += this._redCardPanel.height;
            fTotalHeight += this._blueCardPanel.height;
            fTotalHeight += this._publicCardPanel.height;
            if (fTotalHeight * fitScale <= pf.system.view.height) {
                this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD;
            }
        }

        // this._bTrueFullScreen = fTotalWidth <= pf.system.view.width;

        // if (cv.native.isWideScreen()) {
        //     if (cc.sys.os == cc.sys.OS_ANDROID) {
        //         let size = cc.winSize;
        //         if (size.width / size.height <= 1920 / 1439) {
        //             this._isIpad = true;
        //         }
        //     } else {
        //         this._isIpad = true;
        //     }
        // }

        // if (this._bTrueFullScreen && cv.config.IS_FULLSCREEN) {
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            // iphoneX
            // this._isViewX = true;
            this._betContentBg = bet_content_bg_ipx.getComponent(cc.Sprite);
            if (cr.UIUtil.isValidNode(bet_content_bg)) {
                bet_content_bg.removeFromParent(true);
                bet_content_bg.destroy();
            }
            if (cr.UIUtil.isValidNode(bet_content_bg_ipad)) {
                bet_content_bg_ipad.removeFromParent(true);
                bet_content_bg_ipad.destroy();
            }
            // } else if (this._isIpad) {
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            // ipad

            this._betContentBg = bet_content_bg_ipad.getComponent(cc.Sprite);
            if (cr.UIUtil.isValidNode(bet_content_bg)) {
                bet_content_bg.removeFromParent(true);
                bet_content_bg.destroy();
            }
            if (cr.UIUtil.isValidNode(bet_content_bg_ipx)) {
                bet_content_bg_ipx.removeFromParent(true);
                bet_content_bg_ipx.destroy();
            }
        } else {
            // 普通分辨率
            // cc.find("logo", this._gameContent).setPosition(cc.v2(-550, 110));
            this._betContentBg = bet_content_bg.getComponent(cc.Sprite);
            if (cr.UIUtil.isValidNode(bet_content_bg_ipx)) {
                bet_content_bg_ipx.removeFromParent(true);
                bet_content_bg_ipx.destroy();
            }
            if (cr.UIUtil.isValidNode(bet_content_bg_ipad)) {
                bet_content_bg_ipad.removeFromParent(true);
                bet_content_bg_ipad.destroy();
            }
        }

        // let _bgName = this._bTrueFullScreen && cv.config.IS_FULLSCREEN ? 'bet_content_ipx' : 'bet_content';
        // if (this._isIpad) {
        //     _bgName = 'bet_content_ipad';
        // }
        // cv.resMgr.setSpriteFrame(this._betContentBg.node, cv.config.getLanguagePath('game/videoCowboy/') + _bgName);
        let _bgName = macros.Dynamic_Assets.VIDEO_COWBOY_TABLE_SPRITE;
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            _bgName = macros.Dynamic_Assets.VIDEO_COWBOY_TABLE_BROAD_SPRITE;
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            _bgName = macros.Dynamic_Assets.VIDEO_COWBOY_TABLE_NARROW_SPRITE;
        }
        pf.addressableAssetManager.loadAsset(_bgName).then((asset: cc.SpriteFrame) => {
            this._betContentBg.node.getComponent(cc.Sprite).spriteFrame = asset;
        });

        // console.log("this._bottomPanel.height=" + this._bottomPanel.height);
        // console.log("this._gameContent.size=[" + this._gameContent.width + "," + this._gameContent.height + "]");
        let betAreaPos: cc.Vec2 = cc.v2(0, cc.winSize.height * 0.44 - this._betContentBg.node.height * 0.5);
        let bottomPanelHeightPos: cc.Vec2 = this._bottomPanel.convertToWorldSpaceAR(cc.v2(0, this._bottomPanel.height));
        let betAreaPosY: number = bottomPanelHeightPos.y + this._betContentBg.node.height * 0.5 - 20;
        if (betAreaPos.y < betAreaPosY) {
            betAreaPos.y = betAreaPosY;
        }
        betAreaPos = this._gameContent.convertToNodeSpaceAR(betAreaPos);
        this._betContentBg.node.setPosition(cc.v2(this._betContentBg.node.x, betAreaPos.y));
        cc.find('logo', this._gameContent).setPosition(
            cc.v2(
                this._betContentBg.node.x - this._betContentBg.node.width * 0.35,
                betAreaPos.y + this._betContentBg.node.height * 0.5
            )
        );

        this._betContentBg.node.active = true;
        this._coinNodeByArea = [];
        // for (let i = 0; i < 10; i++) {
        //     let betArea = (this._betContentBg.node.getChildByName(pf.StringUtil.formatC("bet_area_%d", i)));
        //     betArea.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
        //         let name: string = event.target.name;//bet_area_0
        //         let areaIdx: number = cv.Number(name.replace("bet_area_", ""));

        //         console.log("betAreaClicked, areaIdx: %d", areaIdx);
        //         if (this._videoCowboyRoom.curState == network.RoundState.BET && this._leftTime > 0)	// 可以下注
        //         {
        //             if (this._curBetButtonIdx < 0) {
        //                 //showCowboyToast(pf.languageManager.getString("Cowboy_select_bet_button_first_text"));
        //                 return;
        //             }
        //             else {
        //                 cv.videoCowboyNet.RequestBet(this.getBetOptionByAreaIdx(areaIdx), this.getCurBetLevel());
        //             }
        //         }
        //         else {
        //             console.log("betAreaClicked, cannot bet, curState: %d, left bet time: %d", this._videoCowboyRoom.curState, this._leftTime);
        //             // showCowboyToast(pf.languageManager.getString("Cowboy_cannot_bet_now_text"));
        //         }
        //     }, this);
        //     this._betAreas.push(betArea);
        //     let coin_content = betArea.getChildByName("coin_content");
        //     coin_content.setContentSize(coin_content.width - 40, coin_content.height - 20);
        //     this._betCoinContents.push(coin_content);
        //     this._coinNodeByArea.push([]);

        //     let text_self_bet_num: cc.Label = betArea.getChildByName("text_self_bet_num").getComponent(cc.Label);
        //     let text_total_bet_num: cc.Label = (betArea.getChildByName("text_total_bet_num")).getComponent(cc.Label);
        //     this._textSelfBetNum.push(text_self_bet_num);
        //     this._textTotalBetNum.push(text_total_bet_num);
        //     this._oriTextSelfBetNumPos.push(text_self_bet_num.node.getPosition());
        //     this._oriTextTotalBetNumPos.push(text_total_bet_num.node.getPosition());

        //     let winFlag = (betArea.getChildByName("win_flag")).getComponent(cc.Sprite);
        //     this._sprBetAreaWinFlags.push(winFlag);

        //     let textOddst = (betArea.getChildByName("fnt_odd")).getComponent(cc.Label);
        //     textOddst.string = ("");
        //     this._textBetAreaOdds.push(textOddst);
        // }

        // 下注区域映射
        this._mapBetOptionArea.set(network.BetZoneOption.RED_WIN, 0);
        this._mapBetOptionArea.set(network.BetZoneOption.EQUAL, 1);
        this._mapBetOptionArea.set(network.BetZoneOption.BLUE_WIN, 2);

        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_NONE_1DUI, 3);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_2DUI, 4);

        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_SHUN_TONG_HUA, 5);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_2, 6);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4, 7);

        // 按区域索引升序
        let vAreaIdx: number[] = [];
        do {
            this._mapBetOptionArea.forEach((value: number, key: number) => {
                vAreaIdx.push(value);
            });

            vAreaIdx.sort((a: number, b: number): number => {
                return a > b ? 1 : -1;
            });
        } while (0);

        // 对应区域赔率数组
        let vAreaIdxLen = vAreaIdx.length;
        for (let i = 0; i < vAreaIdxLen; ++i) {
            let iAreaIdx = vAreaIdx[i];
            let betArea = this._betContentBg.node.getChildByName(pf.StringUtil.formatC('bet_area_%d', iAreaIdx));
            let coin_content = betArea.getChildByName('coin_content');
            let Image_touch = cc.find('image_touch/Image_touch_' + iAreaIdx, this._betContentBg.node);
            Image_touch.opacity = 0;
            // Image_touch.setTag(i);
            // Image_touch.setSwallowTouches(false);
            Image_touch.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
                this.betAreaClicked(iAreaIdx);
            });
            // betArea.on(cc.Node.EventType.TOUCH_END, (): void => {
            //     this.betAreaClicked(iAreaIdx);
            // });

            this._betAreas.push(betArea);
            this._betCoinContents.push(coin_content);
            this._coinNodeByArea.push([]);

            let text_self_bet_num = cc.find('text_self/text_self_bet_num_' + iAreaIdx, this._betContentBg.node);
            let text_total_bet_num = cc.find('text_total/text_total_bet_num_' + iAreaIdx, this._betContentBg.node);
            this._textSelfBetNum.push(text_self_bet_num.getComponent(cc.Label));
            this._textTotalBetNum.push(text_total_bet_num.getComponent(cc.Label));
            this._oriTextSelfBetNumPos.push(text_self_bet_num.getPosition());
            this._oriTextTotalBetNumPos.push(text_total_bet_num.getPosition());

            let winFlag = betArea.getChildByName('win_flag');
            this._sprBetAreaWinFlags.push(winFlag.getComponent(cc.Sprite));

            let fnt_odd = cc.find('fnt_odd/fnt_odd_' + iAreaIdx, this._betContentBg.node).getComponent(cc.Label);
            let fnt_odd_en = cc.find('fnt_odd/fnt_odd_en_' + iAreaIdx, this._betContentBg.node).getComponent(cc.Label);
            fnt_odd.string = '';
            fnt_odd_en.string = '';
            if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
                this._textBetAreaOdds.push(fnt_odd);
                fnt_odd.node.active = true;
                fnt_odd_en.node.active = false;
            } else {
                this._textBetAreaOdds.push(fnt_odd_en);
                fnt_odd.node.active = false;
                fnt_odd_en.node.active = true;
            }

            // 初始化路子信息
            this._initWayOutInfoByAreaIdx(iAreaIdx);
        }

        this._initAreaLineData();
    }

    canTouchImgeArea(mysprite: cc.Node, touchLocation: cc.Vec2): boolean {
        return true;
        // 计算touchLocation相对于mysprite的boundingBox左下角的坐标， 因为一般的锚点都在sprite中心，所以加了半个ContentSize
        // let ps = cc.v2(touchLocation.x - mysprite.x, touchLocation.y - mysprite.y);// touchLocation - mysprite.getPosition() + Point(mysprite.getContentSize().width / 2, mysprite.getContentSize().height / 2);
        // //加载和mysprite相同的图片，我是实现的png的，alpha通道很好用
        // let myImage = new Image();
        // myImage.initWithImageFile(StringUtils::format("videoCowboy/betAreaTouch/bet_touch_img_%d.png", mysprite.getTag()));
        // unsigned char *data = myImage.getData();  //这里就是图片数据了
        // //根据刚刚计算的相对坐标值，计算出触摸点代表了哪一个像素点      然后再提取出该像素点的alpha值
        // //注意：因为图片坐标（0，0）是在左上角，所以要和触摸点的Y转换一下，也就是“(myImage.getHeight() - (int)(ps.y) - 1)”
        // int pa = 4 * ((myImage.getHeight() - (int)(ps.y) - 1) * myImage.getWidth() + (int)(ps.x)) + 3;
        // if(pa < 0)	return false;//临时处理
        // unsigned int ap = data[pa];
        // //判断alpha值不为零时，也就是触摸在了不规则sprite的有图像的地方，这时再处理需要的逻辑内容就好了
        // return (ap != 0);
    }

    betAreaClicked(areaIdx: number): void {
        console.log('betAreaClicked, areaIdx: %d', areaIdx);

        if (
            this._videoCowboyRoom.gameState.roundState === network.RoundState.BET &&
            this._leftTime > 0 &&
            this._clock_node.active
        ) {
            // 可以下注
            if (this._curBetButtonIdx < 0) {
                this.showCowboyToast(pf.languageManager.getString('Cowboy_select_bet_button_first_text'));
                return;
            } else {
                // cv.videoCowboyNet.RequestBet(this.getBetOptionByAreaIdx(areaIdx), this.getCurBetLevel());
                this._videoCowboyRoom.bet(this.getBetOptionByAreaIdx(areaIdx), this.getCurBetLevel());
            }
        } else {
            console.log(
                'betAreaClicked, cannot bet, curState: %d, left bet time: %d',
                this._videoCowboyRoom.gameState.roundState,
                this._leftTime
            );
            // showCowboyToast(pf.languageManager.getString("Cowboy_cannot_bet_now_text"));
        }
    }

    updateBetArea(option: number): void {
        let areaIdx = this.getAreaIdxByBetOption(option);

        // // 自己的下注
        // let it = this._videoCowboyRoom.selfZoneBet.get(option);
        // if (it) {
        //     this.updateSelfBetAreaCoin(areaIdx, it);
        // }

        // // 总共的下注
        // let it2 = this._videoCowboyRoom.totalZoneBet.get(option);
        // if (it2) {
        //     this.updateTotalBetAreaCoin(areaIdx, it2);
        // }
        const betZone = this._videoCowboyRoom.betZones.get(option);
        if (betZone) {
            this.updateSelfBetAreaCoin(areaIdx, betZone.optionInfo.selfBet);
            this.updateTotalBetAreaCoin(areaIdx, betZone.optionInfo.totalBet);
        }
    }

    updateSelfBetAreaCoin(areaIdx: number, coin: number): void {
        let text = this._textSelfBetNum[areaIdx];
        if (!text) return;
        text.node.setPosition(this._oriTextSelfBetNumPos[areaIdx]);

        if (coin >= 100) {
            text.string = this.getBetAreaCoinString(coin);
        } else {
            text.string = '';
        }
    }

    updateTotalBetAreaCoin(areaIdx: number, coin: number): void {
        let text = this._textTotalBetNum[areaIdx];
        if (!text) return;
        text.node.setPosition(this._oriTextTotalBetNumPos[areaIdx]);

        if (coin >= 100) {
            text.string = this.getBetAreaCoinString(coin);
        } else {
            text.string = '';
        }
    }

    updateAllBetAreas(): void {
        // let tempMap = this._videoCowboyRoom.selfZoneBet;
        // // 自己下注详情
        // tempMap.forEach(
        //     function (key: number, value: number, i: number) {
        //         this.updateSelfBetAreaCoin(this.getAreaIdxByBetOption(key), value);
        //     }.bind(this)
        // );
        // // 总的下注详情
        // this._videoCowboyRoom.totalZoneBet.forEach(
        //     function (key: number, value: number, i: number) {
        //         let areaIdx = this.getAreaIdxByBetOption(key);
        //         let coin = value;
        //         this.updateTotalBetAreaCoin(areaIdx, coin);
        //         // 随机位置生成金币
        //         if (coin > 0) {
        //             let coinContent = this._betCoinContents[areaIdx];
        //             coinContent.destroyAllChildren();
        //             coinContent.removeAllChildren(true);
        //             let sz = coinContent.getContentSize();
        //             //let betDetails = getBetDetailAmounts(coin);
        //             let betDetails = this.getAllBetDetailAmounts(key);
        //             for (let j = 0; j < betDetails.length; j++) {
        //                 let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, betDetails[j]);
        //                 let coinFlyBorn: cc.Vec2 = this._betLineNode[areaIdx].convertToWorldSpaceAR(
        //                     this.getOneAreaPos(areaIdx, this.isCircleCoin(betDetails[j]))
        //                 );
        //                 coinFlyBorn = flyCoin.node.parent.convertToNodeSpaceAR(coinFlyBorn);
        //                 flyCoin.node.setPosition(coinFlyBorn);
        //             }
        //         }
        //     }.bind(this)
        // );
        this._videoCowboyRoom.betZones.forEach((betZone) => {
            const areaIdx = this.getAreaIdxByBetOption(betZone.option);

            // 自己下注详情
            this.updateSelfBetAreaCoin(areaIdx, betZone.optionInfo.selfBet);

            // 总的下注详情
            this.updateTotalBetAreaCoin(areaIdx, betZone.optionInfo.totalBet);
            if (betZone.optionInfo.totalBet > 0) {
                let coinContent = this._betCoinContents[areaIdx];
                coinContent.destroyAllChildren();
                coinContent.removeAllChildren(true);
                let sz = coinContent.getContentSize();

                // let betDetails = getBetDetailAmounts(coin);
                let betDetails = this.getAllBetDetailAmounts(betZone.option);
                for (const detail of betDetails) {
                    let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, detail);
                    let coinFlyBorn: cc.Vec2 = this.getRandPos(sz, flyCoin.node);
                    coinFlyBorn = this._coinNode.convertToNodeSpaceAR(coinContent.convertToWorldSpaceAR(coinFlyBorn));

                    flyCoin.node.setPosition(coinFlyBorn);
                }
            }
        });
    }

    initBetCountDown(): void {
        this._betCountDownBg = this._gameContent.getChildByName('bet_count_down_bg').getComponent(cc.Sprite);
        const betCountDownBg = this._betCountDownBg.getComponent(cc.Sprite);
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            // VideoCowboyManager.loadSpriteTextureByPlist(this.language_PLIST, this._betCountDownBg, 'bet_count_down');
            betCountDownBg.spriteFrame = this.language_PLIST.getSpriteFrame('bet_count_down');
        }
        this._textCountDown = this._betCountDownBg.node.getChildByName('text_count_down').getComponent(cc.Label);
        this._oriBetCountDownBgPos = this._betCountDownBg.node.getPosition();
    }

    hideBetCountDown(): void {
        this._betCountDownBg.node.active = false;
        this._betCountDownBg.node.setPosition(this._oriBetCountDownBgPos);
        this.unschedule(this.updateBetTimer);
    }

    updateBetCoutDown(): void {
        this.hideBetCountDown();
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            // 可以下注
            // this._betCountDownBg.node.active = (true);
            this._clock_num_txt.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this.schedule(this.updateBetTimer, 1.0);
        }
    }

    updateBetTimer(f32Delta: number): void {
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            // this._betCountDownBg.node.active = (true);
            this._clock_num_txt.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this.playSoundEffect(this.s_time_tick);
        } else {
            this._clock_num_txt.string = '0';
        }
    }

    hideGameTips(): void {
        this._gameTipsBg.node.active = false;
        this.unschedule(this.updateNextRoundTipsTimer);
    }

    showNextRoundTips(): void {
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            this.clearSceneAfterJieSuan();
            this._gameTipsBg.node.active = false;
            // this._gameTipsBg.active = false;
            // this._textGameTips.string = (pf.StringUtil.formatC(pf.languageManager.getString("Cowboy_game_tips_wait_next_round_text"), this._leftTime));
            // this.unschedule(this.updateNextRoundTipsTimer);
            // this.schedule(this.updateNextRoundTipsTimer, 1.0);

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        }
    }

    updateNextRoundTipsTimer(f32Delta: number): void {
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            // this._textGameTips.string = (pf.StringUtil.formatC(pf.languageManager.getString("Cowboy_game_tips_wait_next_round_text"), this._leftTime));

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        } else {
            this.hideGameTips();
        }
    }

    showSendCardTips(): void {
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.NEW_ROUND) {
            // 暂时不要提示
            // this._gameTipsBg..node.active = (true);
            this._gameTipsBg.node.active = false;
            this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_send_card_text');
        }
    }

    showOpenCardTips(): void {
        if (
            this._videoCowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND &&
            this._leftTime > this._betWinFlagsAndFlyCoinsDuration + this._showNextRoundDuration
        ) {
            // 暂时不要提示
            // this._gameTipsBg..node.active = (true);
            this._gameTipsBg.node.active = false;
            this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_open_card_text');
        }
    }

    initPlayersInfo(): void {
        // 自己
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        this._textNickName = this.self_panel.getChildByName('text_nickname').getComponent(cc.Label);
        this._textCoin = this.self_panel.getChildByName('text_coin').getComponent(cc.Label);
        this._selfHeadBg = this.self_panel.getChildByName('img_head_box');
        this._selfCoin = this.self_panel.getChildByName('own_coin').getComponent(cc.Sprite);
        this.selfAvatar = this.self_panel.getChildByName('Avatar').getComponent(AvatarControl);

        // 其他玩家
        this._leftPlayerPanel = this.node.getChildByName('leftPlayerPanel');
        this._rightPlayerPanel = this.node.getChildByName('rightPlayerPanel');

        for (let i = 0; i < 4; i++) {
            {
                let player = new OtherPlayerHead();
                let playerBg = this._leftPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = playerBg.node.getChildByName('text_coin').getComponent(cc.Label);
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                avatar.name = this._HEAD_IMG_TAG;
                playerBg.node.addChild(avatar);
                player.avatarControl = avatar.getComponent(AvatarControl);

                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                    player.nbFlag.getChildByName('nb_flag_desc').getComponent(cc.Label).string = pf.StringUtil.formatC(
                        pf.languageManager.getString('Cowboy_fuhao_no_text'),
                        1
                    );
                }
                this._otherPlayerHeads.push(player);
            }
            {
                let player = new OtherPlayerHead();
                let playerBg = this._rightPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = playerBg.node.getChildByName('text_coin').getComponent(cc.Label);
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                avatar.name = this._HEAD_IMG_TAG;
                playerBg.node.addChild(avatar);
                player.avatarControl = avatar.getComponent(AvatarControl);

                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                    player.nbFlag.getChildByName('nb_flag_desc').getComponent(cc.Label).string =
                        pf.languageManager.getString('Cowboy_shensuanzi_text');
                }
                this._otherPlayerHeads.push(player);
            }
        }
    }

    // iPad/iPhoneX等宽窄屏适配
    adaptiveScreen(): void {
        // if (!this._bTrueFullScreen) {
        if (this.eGameboyScreenType !== MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            let tempNum = this._bottomPanel.height - 128;
            if (tempNum > 0) {
                this._gameContent.setPosition(this._gameContent.x, this._gameContent.y + tempNum);
                this._gameContent.setScale(0.95);
            } else {
                this._bottomPanel.setContentSize(cc.size(this._bottomPanel.width, 128));
            }
        }
        //    if (cv.config.IS_IPHONEX_SCREEN && cv.config.IS_FULLSCREEN) {
        if (
            this._IS_IPHONEX_SCREEN &&
            this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW
        ) {
            let tmp_x = pf.system.view.iphoneXOffset - 50;
            this._leftPlayerPanel.setPosition(this._leftPlayerPanel.x + tmp_x, this._leftPlayerPanel.y);
            this._rightPlayerPanel.setPosition(this._rightPlayerPanel.x - tmp_x, this._rightPlayerPanel.y);
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
            this._btnPlayerList.node.setPosition(this._rightPlayerPanel.x, this._btnPlayerList.node.y);
            this.self_panel.setPosition(this._btnMenu.node.x + 116, this.self_panel.y);
            this._rewardPanel.x = this._btnMenu.node.x - this._btnMenu.node.width * 0.5 + 133;
            // } else if (cv.config.IS_WIDESCREEN) {
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            if (pf.system.view.width / pf.system.view.height >= 1.4) return;
            let heroOffsetY = 56;
            this._heroBoy.node.setPosition(this._heroBoy.node.x, heroOffsetY + this._heroBoy.node.y);
            this._heroCow.node.setPosition(this._heroCow.node.x, heroOffsetY + this._heroCow.node.y);

            let cardOffsetY = 60;
            this._redCardPanel.setPosition(this._redCardPanel.x, cardOffsetY + this._redCardPanel.y);
            this._blueCardPanel.setPosition(this._blueCardPanel.x, cardOffsetY + this._blueCardPanel.y);
            this._publicCardPanel.setPosition(this._publicCardPanel.x, cardOffsetY + this._publicCardPanel.y);
            this._betCountDownBg.node.setPosition(
                this._betCountDownBg.node.x,
                cardOffsetY + this._betCountDownBg.node.y
            );

            this._oriBetCountDownBgPos = this._betCountDownBg.node.getPosition();
        }
    }

    adaptiveBetBtnPanel(): void {
        // 若为空, 则填充按钮数组
        if (this._vBottomBetBtns.length === 0) {
            // 下注按钮
            let betButtons_len = pf.DataUtil.getArrayLength(this._betButtons);
            for (let i = 0; i < betButtons_len; ++i) {
                this._vBottomBetBtns.push(
                    new MiniGameCommonDef.GameNodeScale(this._betButtons[i].node, this._fBetBtnSrcScaleRate)
                );
            }

            // 续投按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetAuto.node, this._btnBetAuto.node.scale)
            );

            // 清屏按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetClean.node, this._btnBetClean.node.scale)
            );

            // 红包节按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_redpacket_festival, this._btn_redpacket_festival.scale)
            );
        }

        let w = this._btnPlayerList.node.x - this._btnPlayerList.node.getContentSize().width / 2;
        w -= this.self_panel.x + this.self_panel.getContentSize().width / 2;
        this._panel_betbtn.setContentSize(cc.size(w, this._panel_betbtn.getContentSize().height));
        this._panel_betbtn.setPosition(
            this.self_panel.x + w / 2 + this.self_panel.getContentSize().width / 2,
            this._panel_betbtn.y
        );

        let iTotal_w = 0; // 所有可见子节点宽度和
        let iSpacing_x = 0; // 子节点之间的间距
        let iChildrenCount = 0; // 可见的子节点个数

        let vBottomBetBtns_len = pf.DataUtil.getArrayLength(this._vBottomBetBtns);
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            let node = this._vBottomBetBtns[i].node;
            let fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                ++iChildrenCount;
                iTotal_w += node.getContentSize().width * fScale;
            }
        }

        iSpacing_x = (this._panel_betbtn.getContentSize().width - iTotal_w) / (iChildrenCount + 1);

        let iLast_w = -this._panel_betbtn.width * 0.5;
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            let node = this._vBottomBetBtns[i].node;
            let fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                let szNode = node.getContentSize();
                let x = iLast_w + iSpacing_x + (szNode.width * fScale) / 2;
                let pos = this._panel_betbtn.convertToWorldSpaceAR(cc.v2(x, 0));
                pos = node.getParent().convertToNodeSpaceAR(pos);
                node.setPosition(pos.x, node.y);
                iLast_w = pos.x + (szNode.width * fScale) / 2;
            }
        }

        // 适配红包节入口节点提示层
        if (this._btn_redpacket_festival_layer) {
            let wpos: cc.Vec2 = this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO);
            this._btn_redpacket_festival_layer.setPosition(
                this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
            );
        }

        // 适配高级续投提示语位置
        // 适配高级续投提示语位置
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
        }
    }

    getPlayerCoinNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._selfCoin.node);
        }

        let len = this._otherPlayerHeads.length;
        for (let i = 0; i < len; i++) {
            if (this._otherPlayerHeads[i].uid === uid) {
                ret.push(this._otherPlayerHeads[i].bg.node);
            }
        }
        return ret;
    }

    getPlayerHeadNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._selfHeadBg);
        }

        for (const head of this._otherPlayerHeads) {
            if (head.uid === uid) {
                ret.push(head.bg.node);
            }
        }
        return ret;
    }

    updatePlayerCoin(uid: number): void {
        if (uid === this._authService.currentUser.userId) {
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(this._videoCowboyRoom.selfPlayer.curCoin);
        }

        for (const head of this._otherPlayerHeads) {
            if (head.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(
                        this._videoCowboyRoom.selfPlayer.curCoin
                    );
                } else {
                    let player = this._videoCowboyRoom.getOtherPlayerByUid(uid);
                    if (player) {
                        head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(player.curCoin);
                        // player.reset();
                    }
                }
            }
        }
    }

    // getShortOwnCoinString(coin: number): string {
    //     let formatCoin = cr.CurrencyUtil.clientGoldByServer(coin);
    //     if (cr.CurrencyUtil.numberToShowNumber(formatCoin) < 10000) {
    //         return cr.CurrencyUtil.numberToString(formatCoin);
    //     } else {
    //         return (
    //             cr.CurrencyUtil.numberToString(cr.CurrencyUtil.clientGoldByServer(coin / 10000)) +
    //             pf.languageManager.getString('Cowboy_coin_short_text')
    //         );
    //     }
    // }

    // 下注区域金币：超过10万显示xxW
    getBetAreaCoinString(coin: number): string {
        let formatCoin = cr.CurrencyUtil.convertToClientAmount(coin);
        if (cr.CurrencyUtil.applyDisplayRatioToNumber(formatCoin) < 100000) {
            return cr.CurrencyUtil.clientAmountToDisplayString(formatCoin);
        } else {
            return (
                pf.StringUtil.formatC('%lld', cr.CurrencyUtil.applyDisplayRatioToNumber(formatCoin) / 10000) +
                pf.languageManager.getString('Cowboy_coin_short_text')
            );
        }
    }

    updateSelfCoin(): void {
        // if (coin < 0) coin = this._videoCowboyRoom.selfPlayer.curCoin;

        // this._videoCowboyRoom.selfPlayer.curCoin = coin;
        // if (this._humanboyMenu) {
        //     this._humanboyMenu.getComponent(HumanboyMenu).refreshMenuBalance();
        // }
        this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(this._videoCowboyRoom.selfPlayer.curCoin);
    }

    updateOtherCoin(): void {
        let len = this._otherPlayerHeads.length;
        let otherPlayersInfo = this._videoCowboyRoom.otherPlayers;
        let infoLen = otherPlayersInfo.length;
        for (let i = 0; i < len; i++) {
            if (i < infoLen) {
                let info = otherPlayersInfo[i];
                this._otherPlayerHeads[i].textCoin.string = cr.CommonUtil.getShortOwnCoinString(info.curCoin);
            }
        }
    }

    updateSelfInfo(): void {
        // 未处理
        const player = this._videoCowboyRoom.selfPlayer;
        cr.UIUtil.setShrinkString(this._textNickName.node, player.name, true);
        let llCurCoin: number = this._videoCowboyRoom.selfPlayer.curCoin;
        this.updateSelfCoin();
        // CircleSprite.setCircleSprite(this._selfHeadBg, player.head, player.plat, false);
        this.selfAvatar.loadHeadImage(this._videoCowboyRoom.selfPlayer.head, this._videoCowboyRoom.selfPlayer.plat);
        this._videoCowboyRoom.setPlayerBeforeSettlementGold(this._authService.currentUser.userId, llCurCoin);
    }

    // 未处理
    updateOtherPlayersInfo(): void {
        // 这里按照服务器发的gamePlayers顺序放
        let len = this._otherPlayerHeads.length;
        for (let i = 0; i < len; i++) {
            let otherPlayersInfo = this._videoCowboyRoom.otherPlayers;
            if (i < otherPlayersInfo.length) {
                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     this._otherPlayerHeads[i].bg,
                //     'player_bg'
                // );
                this._otherPlayerHeads[i].bg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('player_bg');
                if (this._otherPlayerHeads[i].nbFlag) {
                    this._otherPlayerHeads[i].nbFlag.active = true;
                }

                let info = otherPlayersInfo[i];
                this._otherPlayerHeads[i].uid = info.uid;
                this._otherPlayerHeads[i].textCoin.string = cr.CommonUtil.getShortOwnCoinString(info.curCoin);

                // 头像更新
                // let headBg = this._otherPlayerHeads[i].bg;
                // let head = headBg.node.getChildByName(this._HEAD_IMG_TAG);
                // if (!head) {
                //     head = new cc.Node();
                //     head.addComponent(cc.Sprite);
                //     cv.resMgr.setSpriteFrame(head, 'pkw/zh_CN/game/cowboy/head/head_1');

                //     // let headSprite = head.addComponent(cc.Sprite);
                //     // if (info.uid == this._authService.currentUser.userId) {
                //     //     VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, headSprite, "self_head_default_2");
                //     // }
                //     // else {
                //     //     VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, headSprite, "other_head_default");
                //     // }
                //     head.name = this._HEAD_IMG_TAG;
                //     head.setAnchorPoint(cc.v2(0.5, 0.5));
                //     // head.setPosition(headBg.getContentSize() / 2);
                //     head.setPosition(cc.v2(0, 21)); //head.getPosition() +
                //     headBg.node.addChild(head, 9);
                // }
                // head.active = true;
                // let headUrl = '';
                // if (info.head.length > 0) {
                //     headUrl = info.head;

                //     CircleSprite.setCircleSprite(head, headUrl, info.plat, undefined, Head_Mode.IRREGULAR);
                //     head.active = false;
                // }
                // head.UpdateSpriteFromUrl(headUrl);
                this._otherPlayerHeads[i].avatarControl.node.active = true;
                this._otherPlayerHeads[i].avatarControl.loadHeadImage(info.head, info.plat);
            } else {
                let win_player_win_count = this.node.getChildByName(
                    'win_player_win_count_' + this._otherPlayerHeads[i].bg.node.uuid
                );
                if (win_player_win_count) {
                    win_player_win_count.removeFromParent(true);
                    win_player_win_count.destroy();
                }

                // let headBg = this._otherPlayerHeads[i].bg;
                // let head = headBg.node.getChildByName(this._HEAD_IMG_TAG);
                // if (head) {
                //     head.active = false;
                //     let circleHeadNode = CircleSprite.getHeadNode(head);
                //     if (circleHeadNode) {
                //         circleHeadNode.parent.active = false;
                //     }
                // }
                this._otherPlayerHeads[i].avatarControl.node.active = false;
                this._otherPlayerHeads[i].uid = 0;
                // this._otherPlayerHeads[i].bg.removeChildByTag(_HEAD_IMG_TAG);

                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     this._otherPlayerHeads[i].bg,
                //     'player_bg_2'
                // );
                this._otherPlayerHeads[i].bg.spriteFrame = this.game_dznz_PLIST.getSpriteFrame('player_bg_2');
                this._otherPlayerHeads[i].textCoin.string = '';
                if (this._otherPlayerHeads[i].nbFlag) {
                    this._otherPlayerHeads[i].nbFlag.active = false;
                }
            }
        }
    }

    initGuide(): void {
        return;
        // TODO 原邏輯 return 了
        // let storeGuideKey = 'cowboy_has_show_guide_2';
        // if (cr.UIUtil.GetStringByCCFile(storeGuideKey) != 'true') {
        //     let panelRecord = this._topBg.node.getChildByName('panelRecord');
        //     for (let i = 0; i < this._recordNum + 1; i++) {
        //         this._recordDotsTemp.push(
        //             panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i)).getComponent(cc.Sprite)
        //         );
        //     }

        //     if (!this._humanboyGuid) {
        //         this._humanboyGuid = cc.instantiate(this.humanboyGuid_prefab);
        //         this.node.addChild(this._humanboyGuid, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_GUIDE);
        //     }
        //     let guidLayer = this._humanboyGuid.getComponent(HumanboyGuid);
        //     guidLayer.setDescString(pf.languageManager.getString('Cowboy_ludan_guide_text'));

        //     guidLayer.show(
        //         this._topBg.node,
        //         () => {
        //             let hasShowGuide = 'true';
        //             cr.UIUtil.SaveStringByCCFile(storeGuideKey, hasShowGuide);

        //             cv.videoCowboyNet.RequestTrend();
        //             this._cowboyChart.active = true;
        //             cv.MessageCenter.send('on_display_page1');
        //             this.playCowboyEffect(this.s_button);
        //             pf.CollectionUtil.clearArray(this._recordDotsTemp);
        //         },
        //         true
        //     );
        // }
    }

    initChart() {
        this._videoCowboyChart = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.VIDEO_COWBOY_CHART));
        let chartbg = cc.find('Panel', this._videoCowboyChart);

        let size = cc.winSize;
        chartbg.setPosition(size.width, chartbg.getPosition().y);

        // this._cowboyChart.setAnchorPoint(0.5, 0.5);
        this._videoCowboyChart.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
        this.node.addChild(this._videoCowboyChart);
        this._videoCowboyChart.active = false;
        this._videoCowboyChartControl = this._videoCowboyChart.getComponent(VideoCowboyChartControl);
        // cv.videoCowboyNet.RequestTrend();
        this._videoCowboyRoom.queryTrend();
    }

    // onGuideBgClick(event: cc.Event) {
    //     cv.AudioMgr.playButtonSound('button_click');
    //     event.stopPropagation();
    // }

    // onGuideTopBgClick(event: cc.Event) {
    //     cv.AudioMgr.playButtonSound('button_click');
    //     let storeGuideKey = 'cowboy_has_show_guide_2';
    //     let hasShowGuide = 'true';
    //     cr.UIUtil.SaveStringByCCFile(storeGuideKey, hasShowGuide);
    //     //this._topBgTemp.node.parent.active = false;
    //     cv.videoCowboyNet.RequestTrend();
    //     this._cowboyChart.active = true;
    //     this.playCowboyEffect(this.s_button);
    //     //this._topBgTemp.node.removeFromParent(true);
    //     this._recordDotsTemp = [];
    // }

    openShop(sender: any): void {
        // cv.AudioMgr.playButtonSound('button_click');

        // const dialog: HumanboyDialog = cc.instantiate(this.HumanboyDialog_prefab).getComponent(HumanboyDialog);
        // dialog.node.name = 'videocowboy_dialog_recharge';
        // dialog.show(
        //     pf.languageManager.getString('PokerMaster_dialog_recharge'),
        //     pf.languageManager.getString('Cancel'),
        //     pf.languageManager.getString('Confirm'),
        //     (sender: HumanboyDialog) => {},
        //     (sender: HumanboyDialog) => {
        //         //充值标示
        //         // (<any>window).HMFAppSetting.pokerMasterExitWhere = 3;
        //         (<any>window).HMFAppSetting.isPokerMasterExitWithRechargeSuccess = true;
        //         cv.videoCowboyNet.RequestLeaveRoom();
        //     }
        // );

        // this.node.addChild(dialog.node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL);

        if (this._platform === 'pkw') {
            if (pf.system.isBrowser) {
                cr.commonResourceAgent.commonDialog.showMsg(
                    pf.languageManager.getString('UIOpenNewWindow'),
                    [pf.languageManager.getString('TipsPanel_sure_button')],
                    () => cr.commonResourceAgent.shop?.open()
                );
            } else {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                // this.exitGame();
                this.tryLeaveRoom();
            }
        } else if (this._platform === 'wpk') {
            cr.commonResourceAgent.commonDialog.showMsg(
                pf.languageManager.getString('PokerMaster_dialog_recharge'),
                [
                    pf.languageManager.getString('TipsPanel_sure_button'),
                    pf.languageManager.getString('TipsPanel_cancel_button')
                ],
                () => {
                    const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                    context.isSelfRecharge = true;
                    context.exitCallback(pf.client.ExitType.Standard);
                },
                () => {
                    // do nothing here
                }
            );
        }
    }

    getChartPanel(): cc.Node {
        return this._rightChartPanel;
    }

    initButtonEvents(): void {
        // 菜单按钮
        this._btnMenu = this.node.getChildByName('btnMenu').getComponent(cc.Button);
        // this._btnMenu.node.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_5;
        this._btnMenu.node.on('click', (event: cc.Event): void => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            if (!this._humanboyMenu) {
                if (this._platform === 'pkw') {
                    this._humanboyMenu = cc.instantiate(
                        pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU)
                    );
                } else if (this._platform === 'wpk') {
                    this._humanboyMenu = cc.instantiate(
                        pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU_WITHOUT_EXCHANGE)
                    );
                }
                let menuLayer = this._humanboyMenu.getComponent(MiniGameMenuControl);
                this.node.addChild(this._humanboyMenu, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL);

                // TODO 充值邏輯待確認
                // 菜单 - 充值
                // menuLayer.getBtnAdd().node.on('click', (event: cc.Event): void => {
                //     menuLayer.hide(false);
                //     this.openShop(null);
                // });

                // 菜单 - 兑换
                menuLayer.getBtnExchange().node.on('click', (event: cc.Event): void => {
                    pf.audioManager.playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (this._authService.currentUser.usdt <= 0) {
                        cr.commonResourceAgent.toastMessage.showMsg(
                            pf.languageManager.getString('USDTView_ex_coin_error_0_usdt')
                        );
                        return;
                    }
                    if (!this._humanboyExchange) {
                        // this._humanboyExchange = cc
                        //     .instantiate(pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_EXCHANGE))
                        //     .getComponent(MiniGameExchangeControl);
                        // this.node.addChild(
                        //     this._humanboyExchange.node,
                        //     COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                        // );
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXCHANGE)
                            .then((asset: cc.Prefab) => {
                                this._humanboyExchange = cc.instantiate(asset).getComponent(MiniGameExchangeControl);
                                this.node.addChild(
                                    this._humanboyExchange.node,
                                    COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                                );
                            });
                    } else {
                        this._humanboyExchange.openView();
                    }
                });

                // 菜单 - 规则
                menuLayer.getBtnRule().node.on('click', (event: cc.Event): void => {
                    this.playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (this._cowboyRule === null) {
                        // this._cowboyRule = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_RULE)
                        // );
                        // this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboyRule);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_RULE)
                            .then((asset: cc.Prefab) => {
                                this._cowboyRule = cc.instantiate(asset);
                                this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboyRule);
                                this._cowboyRule.getComponent(MiniGameRuleControl).openView('');
                            });
                    } else this._cowboyRule.getComponent(MiniGameRuleControl).openView(macros.RULE_URL);
                });

                // 菜单 - 音效设置
                menuLayer.getBtnSoundSetting().node.on('click', (event: cc.Event): void => {
                    this.playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);
                    if (this._cowboySetting === null) {
                        // this._cowboySetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_AUDIO_SETTING)
                        // );
                        // this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboySetting);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_AUDIO_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._cowboySetting = cc.instantiate(asset);
                                this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboySetting);
                            });
                    } else {
                        this._cowboySetting.getComponent(MiniGameAudioSettingControl).initSwitch();
                        this._cowboySetting.active = true;
                    }
                });

                // 菜单 - 高级设置
                menuLayer.getBtnAdvancedSetting().node.on('click', (event: cc.Event): void => {
                    this.playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (!this._humanboyAdvancedSetting) {
                        // this._humanboyAdvancedSetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_ADVANCED_SETTING)
                        // );
                        // this.node.addChild(
                        //     this._humanboyAdvancedSetting,
                        //     COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                        // );
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_ADVANCED_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._humanboyAdvancedSetting = cc.instantiate(asset);
                                this.node.addChild(
                                    this._humanboyAdvancedSetting,
                                    COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                                );
                                this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                            });
                    } else this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                });

                // 菜单 - 退出
                menuLayer.getBtnExit().node.on('click', (event: cc.Event): void => {
                    this.playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    let iUsedAutoBetCount = this._videoCowboyRoom.betSettings.usedAutoBetCount;
                    let iSelectAutoBetCount = this._videoCowboyRoom.betSettings.selectAutoBetCount;
                    if (iSelectAutoBetCount > 0) {
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                            iUsedAutoBetCount,
                            iSelectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_exit_game');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_game');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.videoCowboyNet.RequestLeaveRoom();
                            this.tryLeaveRoom();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };

                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = cr.UIUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                        iUsedAutoBetCount,
                                        iSelectAutoBetCount
                                    )
                                );
                            }
                            if (this._videoCowboyRoom.betSettings.reachLimitBet) {
                                miniGameDialog?.blockCenterButton();
                            }
                        };

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._videoCowboyRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    } else {
                        if (this._cowboyExit === null) {
                            // this._cowboyExit = cc.instantiate(
                            //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_EXIT)
                            // );
                            // this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                            // this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                            // this.node.addChild(this._cowboyExit);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXIT)
                                .then((asset: cc.Prefab) => {
                                    this._cowboyExit = cc.instantiate(asset);
                                    this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                                    this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                    this.node.addChild(this._cowboyExit);
                                });
                        } else {
                            this._cowboyExit.active = true;
                        }
                        this.playSoundEffect(macros.Audio.PRESS);
                    }
                });
            }

            this._humanboyMenu.getComponent(MiniGameMenuControl).show(true);
            this._humanboyMenu
                .getComponent(MiniGameMenuControl)
                .setMenuPosition(
                    cc.v2(this._btnMenu.node.x + 25, this._btnMenu.node.y - this._btnMenu.node.height / 2)
                );
        });

        // 玩家列表
        this._btnPlayerList = this._bottomPanel.getChildByName('btnPlayerList').getComponent(cc.Button);
        this._btnPlayerList.node.on('click', (event: cc.Event): void => {
            // cv.videoCowboyNet.RequestPlayerList();
            this.playSoundEffect(this.s_button);
            this._videoCowboyRoom.getPlayerList().then((resp) => {
                this.OnPlayerListUpdate(resp.players, resp.playerNum);
            });
        });

        // 商店
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        let btn_shop_valid = this.self_panel.getChildByName('btn_shop_valid');
        btn_shop_valid.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
            this.openShop(null);
        });
    }
    onGoldViewShop() {
        this.openShop(null);
    }

    initBetButtons(): void {
        this._betButtons = [];
        this._betButtonTexts = [];
        this._betButtonMasks = [];

        for (let betBtnIdx = 0; betBtnIdx < this._betButtonNum; betBtnIdx++) {
            let btnBet = this._panel_betbtn.getChildByName('btn_bet_' + betBtnIdx).getComponent(cc.Button);
            // std.string betBtnPng = betBtnIdx <= 2 ? "bet_coin_normal" : "bet_block_normal";
            // btnBet.loadTextures(betBtnPng, betBtnPng, "", cocos2d.ui.Widget.TextureResType.PLIST);
            this._betButtons.push(btnBet);
            this._betButtonTexts.push(btnBet.node.getChildByName('textBet'));
            this._betButtonMasks.push(btnBet.node.getChildByName('imgMask').getComponent(cc.Sprite));

            let spMask: any = btnBet.node.getChildByName('imgMask').getComponent(cc.Sprite);
            // TODO 适配私语平台?
            // if (!cv.config.isSiyuType()) {
            //     // 非私语平台，设置通透。
            //     spMask.srcBlendFactor = cc.macro.BlendFactor.SRC_ALPHA;
            // }

            btnBet.node.on(
                'click',
                (event: cc.Event): void => {
                    console.log('GameCowboyScene btnBet %d clicked', betBtnIdx);
                    this.betButtonSelected(betBtnIdx);
                    this.playSoundEffect(this.s_button);
                },
                this
            );
        }

        // 初始化高级续投面板
        if (!this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ADVANCED_AUTO)
            );
            this.node.addChild(this._humanboyAdvancedAuto, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT);
        }

        // 续投按钮
        this._btnBetAuto = this._panel_betbtn.getChildByName('btn_bet_auto').getComponent(cc.Button);
        this._btnBetAuto.node.on('click', (event: cc.Event) => {
            this.playSoundEffect(this.s_button);

            switch (this._eAutoBtnStyle) {
                // 常规续投点击
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                    // {
                    //     cv.videoCowboyNet.RequestAutoBet();
                    // }
                    this._videoCowboyRoom.autoBet().then(() => {
                        this.OnAutoBetSucc();
                    });
                    break;

                // 高级续投已激活(再次点击 弹出高级续投选项面板)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                    {
                        // if (cb.getCowboyRoom().curState == RoundState.BET) {
                        // this._humanboyAdvancedAuto?.adaptSelectPanelPos(this._btnBetAuto.node);
                        // this._humanboyAdvancedAuto?.showSelectPanel(true);
                        const miniGameAdvanceAuto =
                            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl);
                        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
                        advanceAuto.adaptSelectPanelPos(this._btnBetAuto.node);
                        advanceAuto.showSelectPanel(true);
                        // }
                    }
                    break;

                // 高级续投中(再次点击取消)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                    {
                        let iUsedAutoBetCount = this._videoCowboyRoom.betSettings.usedAutoBetCount;
                        let iSelectAutoBetCount = this._videoCowboyRoom.betSettings.selectAutoBetCount;
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                            iUsedAutoBetCount,
                            iSelectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_stop_auto_bet');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.videoCowboyNet.ReqCancelAdvanceAutoBet();
                            this._videoCowboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };
                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = cr.UIUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                                        iUsedAutoBetCount,
                                        iSelectAutoBetCount
                                    )
                                );
                            }
                            if (this._videoCowboyRoom.betSettings.reachLimitBet) {
                                miniGameDialog?.blockCenterButton();
                            }
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._videoCowboyRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                    break;

                default:
                    break;
            }
        });

        // 清屏按钮
        this._btnBetClean = this._panel_betbtn.getChildByName('btn_bet_clean').getComponent(cc.Button);
        // cv.resMgr.loadButtonTextureByPlist(
        //     this.language_PLIST,
        //     this._btnBetClean.node,
        //     'clean_screen_normal',
        //     'clean_screen_normal',
        //     'clean_screen_normal',
        //     'clean_screen_gray'
        // );
        this._btnBetClean.normalSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.pressedSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.hoverSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.disabledSprite = this.language_PLIST.getSpriteFrame('clean_screen_gray');
        this._btnBetClean.node.on('click', (event: cc.Event) => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            this.clearAllBetAreaCoins();
        });
    }

    /**
     * 初始化红包等相关按钮入口
     */
    private initRedPackage(): void {
        // 红包节按钮
        this._btn_redpacket_festival = this._panel_betbtn.getChildByName('btn_redpacket_festival');
        this._btn_redpacket_festival.getComponent(cc.Sprite).spriteFrame = null;
        this._btn_redpacket_festival.active = false;

        // 红包节按钮提示层
        this._btn_redpacket_festival_layer = cc.instantiate(this._btn_redpacket_festival);
        this.node.addChild(this._btn_redpacket_festival_layer, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_RED_PACKAGE);

        let wpos: cc.Vec2 = cc.Vec2.ZERO;
        this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
        this._btn_redpacket_festival_layer.setPosition(
            this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
        );

        // 初始执行一次
        this.showLuckButton();
    }

    clearAllBetAreaCoins(): void {
        let betAreas_len = pf.DataUtil.getArrayLength(this._betAreas);
        for (let i = 0; i < betAreas_len; i++) {
            this.hideAreaCoin(i, false);
        }
    }
    resetAllBetButtons(): void {
        let len = this._betButtons.length;
        for (let i = 0; i < len; i++) {
            this._betButtons[i].node.setScale(this._fBetBtnSrcScaleRate, this._fBetBtnSrcScaleRate);
            this._betButtonTexts[i].active = true;
            this._betButtonMasks[i].node.active = false;
            this._betButtons[i].enabled = true;
        }
        this._curBetButtonIdx = -1;
    }

    betButtonSelected(betBtnIdx: number, ignoreCheckCoin?: boolean): void {
        // 未完
        const checkedIsIgnoreCheckCoin = ignoreCheckCoin === true ? true : false;
        this.resetAllBetButtons();
        if (!checkedIsIgnoreCheckCoin) {
            this._updateBetButtonState();
        }

        if (betBtnIdx < 0 || betBtnIdx > this._betButtonNum - 1) {
            return;
        }

        this._curBetButtonIdx = betBtnIdx;
        this._betButtons[betBtnIdx].node.setScale(this._fBetBtnTarScaleRate, this._fBetBtnTarScaleRate);
        // let clickedPng = this._curBetButtonIdx <= 2 ? "bet_coin_clicked" : "bet_block_clicked";
        // VideoCowboyManager.loadButtonTextureByPlist(this.game_dznz_PLIST, this._betButtons[betBtnIdx].node, clickedPng, clickedPng, clickedPng, clickedPng);
        // this._betButtonTexts[betBtnIdx].node.scale = (1.0);
        // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._betButtonMasks[betBtnIdx], this._curBetButtonIdx <= 2 ? "bet_coin_disable_mask_big" : "bet_block_disable_mask_big");
    }

    updateBetButtonText(): void {
        return;
        // let amountlevel = this._videoCowboyRoom.pkRoomParam.amountLevel;
        // for (let i = 0; i < amountlevel.length; i++) {
        //     if (i < this._betButtonNum) {
        //         //this._betButtonTexts[i].string = (this.getShortOwnCoinString(amountlevel[i]));
        //         this._betButtonTexts[i].string = (cr.CurrencyUtil.numberToString(cr.CurrencyUtil.clientGoldByServer(amountlevel[i])));
        //     }
        //     else {
        //         console.log("error!! updateBetButtonText amountlevel must be %d, size: %d", this._betButtonNum, amountlevel.length);
        //     }
        // }
    }

    _updateBetOddsDetail(): void {
        let details = this._videoCowboyRoom.roomParams.oddsDetails;
        for (const detail of details) {
            let areaIdx = this.getAreaIdxByBetOption(detail.option);
            if (this._textBetAreaOdds[areaIdx]) {
                this._textBetAreaOdds[areaIdx].string =
                    cr.CurrencyUtil.convertToClientAmount(detail.odds) +
                    pf.languageManager.getString('Cowboy_odds_text');
            }
        }
    }

    _updateBetButtonState(): void {
        // 检测下注按钮禁用与否
        let vBetCoinOption = this._videoCowboyRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin = this._videoCowboyRoom.selfPlayer.curCoin; // 当前自身携带金币
        let vBetCoinOption_len = pf.DataUtil.getArrayLength(vBetCoinOption);
        for (let i = 0; i < vBetCoinOption_len; ++i) {
            // 钱是否够按钮上的金额
            let llAmountLevel = cr.CurrencyUtil.convertToClientAmount(vBetCoinOption[i]);
            if (curCoin >= vBetCoinOption[i]) {
                this._betButtons[i].interactable = true;
                this._betButtons[i].enabled = true;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, true);
                // cv.resMgr.getAsyncFontByName("pkw/zh_CN/game/cowboy/fnt/bet_btn_num_yellow", (font: cc.Font): void => {
                //     this._betButtonTexts[i].font = font;
                // });
            } else {
                this._betButtons[i].interactable = false;
                this._betButtons[i].enabled = false;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, false);
                // cv.resMgr.getAsyncFontByName("pkw/zh_CN/game/cowboy/fnt/bet_btn_num_gray", (font: cc.Font): void => {
                //     this._betButtonTexts[i].font = font;
                // });                //_resetBetButton(i, false);
            }
        }

        // 检测下注按钮可触摸与否
        let bEffective: boolean =
            this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0;
        let betButtons_len: number = pf.DataUtil.getArrayLength(this._betButtons);
        for (let i = 0; i < betButtons_len; ++i) {
            this._betButtonMasks[i].node.active = !bEffective;
            this._betButtonMasks[i].enabled = true;
            this._betButtons[i].enabled = bEffective;
        }

        // 更新续投按钮状态
        this._updateAutoBetBtnStatus();

        // 更新清屏按钮状态
        this._updateCleanBtnStatus();
    }

    updatBetButtonByCurCoin(): void {
        return;
        // 可以下注的时候才判断
        // if (this._videoCowboyRoom.curState == network.RoundState.BET && this._leftTime > 0) {
        //     let amountlevel = this._videoCowboyRoom.pkRoomParam.amountLevel;
        //     let curCoin = this._videoCowboyRoom.selfPlayer.curCoin;
        //     let len = amountlevel.length;

        //     let disablePng: string = '';
        //     let curStatePng: string = '';

        //     for (let i = 0; i < len; i++) {
        //         if (i < this._betButtonNum) {
        //             // 钱不够按钮上的金额
        //             if (curCoin < amountlevel[i]) {
        //                 if (i == this._curBetButtonIdx) {
        //                     disablePng = i <= 2 ? 'bet_coin_disabled_big' : 'bet_block_disabled_big';
        //                 } else {
        //                     disablePng = i <= 2 ? 'bet_coin_disabled' : 'bet_block_disabled';
        //                 }

        //                 VideoCowboyManager.loadButtonTextureByPlist(
        //                     this.game_dznz_PLIST,
        //                     this._betButtons[i].node,
        //                     disablePng,
        //                     disablePng,
        //                     disablePng,
        //                     disablePng
        //                 );
        //                 this._betButtons[i].enabled = false;
        //                 this._betButtons[i].interactable = false;
        //                 // this._betButtonTexts[i].node.color = (cc.Color.BLACK);
        //                 // this._betButtonTexts[i].node.opacity = (102);
        //             } else {
        //                 if (i == this._curBetButtonIdx) {
        //                     curStatePng = i <= 2 ? 'bet_coin_clicked' : 'bet_block_clicked';
        //                 } else {
        //                     curStatePng = i <= 2 ? 'bet_coin_normal' : 'bet_block_normal';
        //                 }

        //                 VideoCowboyManager.loadButtonTextureByPlist(
        //                     this.game_dznz_PLIST,
        //                     this._betButtons[i].node,
        //                     curStatePng,
        //                     curStatePng,
        //                     curStatePng,
        //                     curStatePng
        //                 );
        //                 this._betButtons[i].enabled = true;
        //                 this._betButtons[i].interactable = true;
        //                 // this._betButtonTexts[i].node.color = (cc.Color.WHITE);
        //                 // this._betButtonTexts[i].node.opacity = (255);
        //             }
        //         } else {
        //             console.log(
        //                 'error!! this.updatBetButtonByCurCoin amountlevel must be: %d, size: %d',
        //                 this._betButtonNum,
        //                 amountlevel.length
        //             );
        //         }
        //     }
        // }
    }

    enableAutoBetButton(enabled: boolean): void {
        this._btnBetAuto.enabled = enabled;
        this._btnBetAuto.interactable = enabled;
    }

    _updateAutoBetBtnStatus(): void {
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL: {
                if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
                    // 当前一局下过注
                    if (this._videoCowboyRoom.roundInfo.hasBetInCurRound) {
                        this.enableAutoBetButton(false);
                    } else {
                        this.enableAutoBetButton(this._videoCowboyRoom.betSettings.canAutoBet);
                    }
                } else {
                    this.enableAutoBetButton(false);
                }
                break;
            }
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE: {
                // 当前一局下过注
                if (this._videoCowboyRoom.roundInfo.hasBetInCurRound) {
                    this.enableAutoBetButton(true);
                } else {
                    this.enableAutoBetButton(this._videoCowboyRoom.betSettings.canAutoBet);
                }
                break;
            }
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING: {
                this.enableAutoBetButton(true);
                break;
            }
            default:
                break;
        }
    }

    _updateCleanBtnStatus(): void {
        let bEnable = false;
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            bEnable = true;
        }

        this._btnBetClean.interactable = bEnable;
    }

    _updateBetAmountLevel(): void {
        let vBetCoinOption = this._videoCowboyRoom.betSettings.betCoinOptions;
        let vBetCoinOption_len = pf.DataUtil.getArrayLength(vBetCoinOption);
        for (let i = 0; i < vBetCoinOption_len; ++i) {
            if (i < this._betButtonNum) {
                let llAmountLevel = cr.CurrencyUtil.convertToClientAmount(vBetCoinOption[i]);
                if (llAmountLevel < this._videoCowboyRoom.llCoinUICritical) {
                    // VideoCowboyManager.loadButtonTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._betButtons[i].node,
                    //     'bet_coin_clicked',
                    //     'bet_coin_clicked',
                    //     'bet_coin_clicked',
                    //     'bet_coin_disabled_big'
                    // );
                    this._betButtons[i].normalSprite = this.game_dznz_PLIST.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].pressedSprite = this.game_dznz_PLIST.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].hoverSprite = this.game_dznz_PLIST.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].disabledSprite = this.game_dznz_PLIST.getSpriteFrame('bet_coin_disabled_big');
                    // VideoCowboyManager.loadSpriteTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._betButtonMasks[i],
                    //     'bet_coin_disable_mask_big'
                    // );
                    this._betButtonMasks[i].spriteFrame =
                        this.game_dznz_PLIST.getSpriteFrame('bet_coin_disable_mask_big');
                    this._betButtonMasks[i].node.scale = 1.05;
                    // this._betButtonMasks[i].loadTexture("bet_coin_disable_mask_big.png", TextureResType.PLIST);
                } else {
                    // VideoCowboyManager.loadButtonTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._betButtons[i].node,
                    //     'bet_block_clicked',
                    //     'bet_block_clicked',
                    //     'bet_block_clicked',
                    //     'bet_block_disabled_big'
                    // );
                    this._betButtons[i].normalSprite = this.game_dznz_PLIST.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].pressedSprite = this.game_dznz_PLIST.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].hoverSprite = this.game_dznz_PLIST.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].disabledSprite = this.game_dznz_PLIST.getSpriteFrame('bet_block_disabled_big');
                    // VideoCowboyManager.loadSpriteTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._betButtonMasks[i],
                    //     'bet_block_disable_mask_big'
                    // );
                    this._betButtonMasks[i].spriteFrame =
                        this.game_dznz_PLIST.getSpriteFrame('bet_block_disable_mask_big');
                    this._betButtonMasks[i].node.scale = 1.0;
                    // _betButtonMasks[i].loadTexture("bet_block_disable_mask_big.png", TextureResType.PLIST);
                }

                // _betButtons[i].ignoreContentAdaptWithSize(true);
                // _betButtonMasks[i].ignoreContentAdaptWithSize(true);
                // this._betButtonMasks[i].node.setPosition(this._betButtonMasks[i].node.getParent().width / 2, this._betButtonMasks[i].node.getParent().height / 2);
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, true);
                // this._betButtonTexts[i].string = (cr.CurrencyUtil.numberToString(llAmountLevel));
                // this._betButtonTexts[i].node.setPosition(this._betButtonTexts[i].node.getParent().width / 2, this._betButtonTexts[i].node.getParent().height / 2);
            } else {
                console.log(
                    'error!! HumanboyMainView._updateBetAmountLevel vBetCoinOption must be %d, size: %d',
                    this._betButtonNum,
                    vBetCoinOption_len
                );
            }
        }

        switch (this._videoCowboyRoom.betSettings.autoBetLevel) {
            case pf.client.session.AutoBetLevel.Level_Normal:
                this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL);
                break;

            case pf.client.session.AutoBetLevel.Level_Advance:
                if (this._videoCowboyRoom.betSettings.selectAutoBetCount > 0) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);
                } else {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
                break;

            default:
                break;
        }

        this.adaptiveBetBtnPanel();
    }

    private _setAutoBetBtnStytle(eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle): void {
        // 隐藏高级续投子面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoTips();
            this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoCount();
            this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
        }

        this._eAutoBtnStyle = eAutoBtnStyle;
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE:
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                // VideoCowboyManager.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_normal',
                //     'autobet_normal',
                //     'autobet_normal',
                //     'autobet_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                // VideoCowboyManager.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_block_normal',
                //     'autobet_block_normal',
                //     'autobet_block_normal',
                //     'autobet_block_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_block_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                // VideoCowboyManager.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_block_using',
                //     'autobet_block_using',
                //     'autobet_block_using',
                //     'autobet_block_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_block_gray');
                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto
                        ?.getComponent(HumanboyAdvancedAutoControl)
                        .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
                }
                break;

            default:
                break;
        }

        let imgBetAuto: cc.Sprite = this._btnBetAuto.getComponent(cc.Sprite);
        imgBetAuto.type = cc.Sprite.Type.SIMPLE;
        imgBetAuto.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    }

    _getAutoBetBtnStytle(): MiniGameCommonDef.eGameboyAutoBtnStyle {
        return this._eAutoBtnStyle;
    }

    _checkAdvanceAutoReq(): void {
        if (
            this._videoCowboyRoom.gameState.roundState === network.RoundState.BET &&
            this._getAutoBetBtnStytle() === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING
        ) {
            if (this._humanboyAdvancedAuto) {
                this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoTips();
            }

            if (
                this._videoCowboyRoom.betSettings.usedAutoBetCount <
                this._videoCowboyRoom.betSettings.selectAutoBetCount
            ) {
                // cv.videoCowboyNet.reqAdvanceAutoBet();
                this._videoCowboyRoom
                    .advanceAutoBet()
                    .then(() => {
                        this._onMsgAdvanceAutobet(network.ErrorCode.OK);
                    })
                    .catch((err: pf.ServerError) => {
                        this._onMsgAdvanceAutobet(err.errorCode);
                    });
            }
        }
    }

    clearBetArea(areaIdx: number): void {
        if (pf.DataUtil.getArrayLength(this._sprBetAreaWinFlags) > areaIdx) {
            this._sprBetAreaWinFlags[areaIdx].node.active = false;
        }

        // this.hideWinFlagAnim(areaIdx);
        this._betCoinContents[areaIdx].destroyAllChildren();
        this._betCoinContents[areaIdx].removeAllChildren(true);
        console.log('clearBetArea-.清理areaIndex = ' + areaIdx);
        this.hideAreaCoin(areaIdx, false);
        this.updateSelfBetAreaCoin(areaIdx, 0);
        this.updateTotalBetAreaCoin(areaIdx, 0);
    }

    clearAllBetArea(): void {
        let len = this._betAreas.length;
        for (let i = 0; i < len; i++) {
            this.clearBetArea(i);
        }
    }

    getAreaIdxByBetOption(betOption: number): number {
        return this._mapBetOptionArea.get(betOption);
    }

    getBetOptionByAreaIdx(areaIdx: number): number {
        let betOption = -1;
        this._mapBetOptionArea.forEach((value: number, key: number) => {
            if (value === areaIdx) {
                betOption = key;
            }
        });

        if (betOption < 0) {
            console.log('error!! getOptionByAreaIdx -1');
        }
        return betOption;
    }

    getCurBetLevel(): number {
        if (this._curBetButtonIdx < 0) {
            return 0;
        }

        let amountlevel = this._videoCowboyRoom.betSettings.betCoinOptions;
        return amountlevel[this._curBetButtonIdx];
    }

    initHistoryDots(): void {
        this._btnZouShi = this._rightChartPanel.getChildByName('btnZouShi').getComponent(cc.Button);
        this._btnZouShi.node.active = false;
        // this._btnZouShi.interactable = false;

        let panel1 = this._rightChartPanel.getChildByName('panel1');
        let panel2 = this._rightChartPanel.getChildByName('panel2');

        this._chartBg = this._rightChartPanel.getChildByName('image_bg');
        let panelRecord = this._rightChartPanel.getChildByName('panelRecord');
        cr.UIUtil.adaptWidget(this._rightChartPanel, true);
        if (this._IS_IPHONEX_SCREEN) {
            this._chartBg.setAnchorPoint(cc.v2(1, 0));
            let chartSize = this._rightChartPanel.getContentSize();
            this._rightChartPanel.setContentSize(
                cc.size(chartSize.width + pf.system.view.iphoneXOffset, chartSize.height)
            );
            this._chartBg.setContentSize(cc.size(chartSize.width + pf.system.view.iphoneXOffset, chartSize.height));
            panelRecord.setPosition(panelRecord.x - pf.system.view.iphoneXOffset, panelRecord.y);
            panel1.setPosition(panel1.x - pf.system.view.iphoneXOffset, panel1.y);
            panel2.setContentSize(cc.size(panel2.width + pf.system.view.iphoneXOffset, panel2.height));
        }
        panel1.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
            // cv.videoCowboyNet.RequestTrend();
            this._videoCowboyRoom.queryTrend();
            this._videoCowboyChart.active = true;
            // this.playCowboyEffect(this.s_button);
            this.playSoundEffect(this.s_button);
        });

        panel2.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
            // cv.videoCowboyNet.RequestTrend();
            this._videoCowboyRoom.queryTrend();
            this._videoCowboyChart.active = true;
            this.playSoundEffect(this.s_button);
        });
        // this._btnZouShi.enabled = false;
        // this._topBg.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
        //     cv.videoCowboyNet.RequestTrend();
        //     this._cowboyChart.active = true;
        //     cv.MessageCenter.send("on_display_page1");
        //     this.playCowboyEffect(this.s_button);
        // }, this);

        for (let i = 0; i < this._recordNum + 1; i++) {
            this._recordDots.push(
                panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i)).getComponent(cc.Sprite)
            );
            let pos: cc.Vec2 = cc.v2(this._recordDots[i].node.getPosition());
            this._oriRecordDotsPos.push(pos);
        }
        this._lastRecordDotWorldPos = panelRecord.convertToWorldSpaceAR(
            this._recordDots[this._recordNum - 1].node.getPosition()
        );

        // 走势
        this._btnZouShi.node.on(
            'click',
            (event: cc.Event): void => {
                // cv.videoCowboyNet.RequestTrend();
                this._videoCowboyRoom.queryTrend();
                this._videoCowboyChart.active = true;
                this.playSoundEffect(this.s_button);
            },
            this
        );

        this._topBg = this.node.getChildByName('top_bg').getComponent(cc.Sprite);
    }

    updateDotState(): void {
        this.hideHistoryMoveAnim();

        let historySize = this._videoCowboyRoom.historyResults.length;

        if (historySize === 1) {
            let recordDotIdx = this._recordNum - 1;
            this._recordDots[recordDotIdx].node.active = true;
        }

        if (historySize > 2) {
            for (let i = 0; i < this._recordNum; i++) {
                // 逆序取
                let historyIdx = historySize - i - 1;
                let recordDotIdx = this._recordNum - i - 1;
                this._recordDots[recordDotIdx].node.active = true;

                if (historyIdx <= 0) {
                    this._recordDots[recordDotIdx].node.active = false;
                } else {
                    this._recordDots[recordDotIdx].node.active = true;
                }
            }
        }
    }

    updateHistoryResults(): void {
        this.hideHistoryMoveAnim();

        let historySize = this._videoCowboyRoom.historyResults.length;
        for (let i = 0; i < this._recordNum; i++) {
            // 逆序取
            let historyIdx = historySize - i - 1;
            let recordDotIdx = this._recordNum - i - 1;
            this._recordDots[recordDotIdx].node.active = true;
            if (this._recordDotsTemp.length > 0) {
                this._recordDotsTemp[recordDotIdx].node.active = true;
            }
            if (historyIdx < 0) {
                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     this._recordDots[recordDotIdx],
                //     'record_draw'
                // );
                this._recordDots[recordDotIdx].spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_draw');
                this._recordDots[recordDotIdx].node.active = false;

                if (this._recordDotsTemp.length > 0) {
                    // VideoCowboyManager.loadSpriteTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._recordDotsTemp[recordDotIdx],
                    //     'record_draw'
                    // );
                    this._recordDotsTemp[recordDotIdx].spriteFrame = this.game_dznz_PLIST.getSpriteFrame('record_draw');
                    this._recordDotsTemp[recordDotIdx].node.active = false;
                }
            } else {
                let betOption = this._videoCowboyRoom.historyResults[historyIdx];
                let frameName = 'record_draw';
                if (betOption === network.BetZoneOption.RED_WIN) {
                    frameName = 'record_red';
                } else if (betOption === network.BetZoneOption.BLUE_WIN) {
                    frameName = 'record_blue';
                }
                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     this._recordDots[recordDotIdx],
                //     frameName
                // );
                this._recordDots[recordDotIdx].spriteFrame = this.game_dznz_PLIST.getSpriteFrame(frameName);
                this._recordDots[recordDotIdx].node.active = true;

                if (this._recordDotsTemp.length > 0) {
                    // VideoCowboyManager.loadSpriteTextureByPlist(
                    //     this.game_dznz_PLIST,
                    //     this._recordDotsTemp[recordDotIdx],
                    //     frameName
                    // );
                    this._recordDotsTemp[recordDotIdx].spriteFrame = this.game_dznz_PLIST.getSpriteFrame(frameName);
                    this._recordDotsTemp[recordDotIdx].node.active = true;
                }
            }
        }
    }

    updateHistoryResultsPrevious(): void {
        let last = this._videoCowboyRoom.removeCurrentHistoryResult();
        if (last !== -1) {
            this.updateHistoryResults();
            this._videoCowboyRoom.addCurrentHistoryResult(last);
        }
    }

    showHistoryMoveAnim(): void {
        if (this._videoCowboyRoom.historyResults.length > 0) {
            // 设置最新胜负标记
            let betOption = this._videoCowboyRoom.historyResults[this._videoCowboyRoom.historyResults.length - 1];
            let frameName = 'record_draw';
            if (betOption === network.BetZoneOption.RED_WIN) {
                frameName = 'record_red';
            } else if (betOption === network.BetZoneOption.BLUE_WIN) {
                frameName = 'record_blue';
            }
            let len = pf.DataUtil.getArrayLength(this._recordDots);
            console.log('----. len = ' + len);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, this._recordDots[len - 1], frameName);

            // 位移动画
            let moveOffset = cc.v2(
                this._oriRecordDotsPos[0].x - this._oriRecordDotsPos[1].x,
                this._oriRecordDotsPos[0].y - this._oriRecordDotsPos[1].y
            );
            this.updateDotState();
            let historySize = this._videoCowboyRoom.historyResults.length;

            for (let i = 0; i < len; i++) {
                console.log('----2222222----. len = ' + len + ', i = ' + i);
                if (this._recordDots[i].node.active === true) {
                    let historySize = this._videoCowboyRoom.historyResults.length;

                    if (historySize === 1) {
                        this.updateHistoryResults();
                    } else {
                        this._recordDots[i].node.runAction(
                            cc.sequence(
                                cc.moveBy(0.3, moveOffset),
                                cc.callFunc(() => {
                                    let lenX = pf.DataUtil.getArrayLength(this._recordDots);
                                    // if (historySize < len)
                                    // {
                                    //     this._recordDots[i].node.active = false;
                                    // }
                                    console.log('--------. len = ' + len + ', i = ' + i);
                                    if (i === len - 2) {
                                        this.updateHistoryResults();
                                    }
                                })
                            )
                        );
                    }
                }
            }

            // cv.MessageCenter.send('cowboy_start_history_move_anim');
            if (this._videoCowboyChartControl) {
                this._videoCowboyChartControl.updateResult();
            }
            // let isOpen = this._videoCowboyRoom.isOpen;
            // if (isOpen) {
            //     cv.videoCowboyNet.RequestTrend();
            // }
        }
    }

    hideHistoryMoveAnim(): void {
        let len = pf.DataUtil.getArrayLength(this._recordDots);
        for (let i = 0; i < len; i++) {
            this._recordDots[i].node.stopAllActions();
            this._recordDots[i].node.setPosition(this._oriRecordDotsPos[i]);
        }
    }

    // /////////////////event handlers//////////////////

    // addEvent(): void {
    //     if (this._isAddEvent) {
    //         return;
    //     } else {
    //         this._isAddEvent = true;
    //     }
    //     cv.MessageCenter.register('on_cowboy_game_round_end_notify', this.OnGameRoundEndNotify.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_deal_notify', this.OnDealNotify.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_start_bet_notify', this.OnStartBetNotify.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_bet_notify', this.OnBetNotify.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_auto_bet_notify', this.OnAutoBetNotify.bind(this), this.node);
    //     cv.MessageCenter.register(
    //         'on_cowboy_auto_bet_notify_handle_over',
    //         this.OnAutoBetNotifyHandleOver.bind(this),
    //         this.node
    //     );
    //     cv.MessageCenter.register('on_cowboy_leave_room_succ', this.OnLeaveRoomSucc.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_auto_bet_succ', this.OnAutoBetSucc.bind(this), this.node);
    //     cv.MessageCenter.register(
    //         'on_cowboy_room_param_change_notify',
    //         this.OnRoomParamChangeNotify.bind(this),
    //         this.node
    //     );
    //     cv.MessageCenter.register('on_cowboy_server_error', this.OnServerError.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_kick_notify', this.OnKickNotify.bind(this), this.node);
    //     cv.MessageCenter.register('on_cowboy_join_room_failed', this.OnJoinRoomFailed.bind(this), this.node);
    //     cv.MessageCenter.register('showShopPanel', this.openShop.bind(this), this.node);

    //     cv.MessageCenter.register('showLuckButton', this.showLuckButton.bind(this), this.node); // 红包节
    //     cv.MessageCenter.register('turntableResultNotice', this.onTurntableResultNotice.bind(this), this.node);

    //     cv.MessageCenter.register('update_gold', this._onMsgUpdateWorldServerGold.bind(this), this.node); // world服金币有变动通知
    //     cv.MessageCenter.register(
    //         'on_cowboy_bet_amount_level_change',
    //         this._onMsgBetAmountLevelChange.bind(this),
    //         this.node
    //     ); // 下注级别变更
    //     cv.MessageCenter.register('on_cowboy_advance_autobet_set', this._onMsgAdvanceAutobetSet.bind(this), this.node); // 设置高级续投次数成功
    //     cv.MessageCenter.register('on_advance_autobet', this._onMsgAdvanceAutobet.bind(this), this.node); // 高级续投
    //     cv.MessageCenter.register(
    //         'on_cowboy_advance_autobet_cancel',
    //         this._onMsgAdvanceAutobetCancel.bind(this),
    //         this.node
    //     ); // 取消高级续投成功
    //     cv.MessageCenter.register(
    //         'on_cowboy_advance_autobet_limit_reached',
    //         this._onMsgAdvanceAutobetLimitReached.bind(this),
    //         this.node
    //     ); // 高级续投接近或者已达上限
    //     cv.MessageCenter.register('on_advance_autobet_add', this.onMsgAdvanceAutobetAdd.bind(this), this.node);
    //     cv.MessageCenter.register('on_gamecoin_consuming_notify', this._onMsgConsumingNotify.bind(this), this.node);
    // }

    OnTrendUpdate(trend: domain.RoomTrend): void {
        // for test
        // this._videoCowboyRoom.trendData = [];
        // this._videoCowboyRoom.dailyStat = [];

        // if (this._cowboyChart != null && this._videoCowboyRoom.showTheNewestTrend == false) {
        //     this._videoCowboyRoom.trendData = this._videoCowboyRoom.lasttrendData;
        //     this._videoCowboyRoom.dailyStat = this._videoCowboyRoom.dailyStat;
        // }
        if (this._videoCowboyChartControl !== null && this._videoCowboyRoom.showTheNewestTrend === false) {
            // this._videoCowboyChartControl.resetblink();
            return;
        }

        this._videoCowboyChartControl.setData(trend);
        this._videoCowboyChartControl.moveChart();

        // this._videoCowboyChart.getComponent(VideoCowboyChart).setData();
        // this._videoCowboyChart.getComponent(VideoCowboyChart).moveChart();
    }

    OnPlayerListUpdate(gamePlayers: pf.services.GamePlayer[], playerNum: number): void {
        if (this._videoCowboyList === null) {
            // this._videoCowboyList = cc.instantiate(
            //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.MINI_GAME_PLAYER_LIST)
            // );
            // this._videoCowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
            // this.node.addChild(this._videoCowboyList);

            // this._videoCowboyList.getComponent(MiniGamePlayerListControl).setVideoCowboyData(gamePlayers, playerNum);
            // this._videoCowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.MINI_GAME_PLAYER_LIST)
                .then((asset: cc.Prefab) => {
                    this._videoCowboyList = cc.instantiate(asset);
                    this._videoCowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
                    this.node.addChild(this._videoCowboyList);

                    this._videoCowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
                    this._videoCowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
                });
        } else {
            this._videoCowboyList.active = true;
            this._videoCowboyList.getComponent(MiniGamePlayerListControl).setVideoCowboyData(gamePlayers, playerNum);
            this._videoCowboyList.getComponent(MiniGamePlayerListControl).displayCell(-1);
        }
    }

    OnGameDataSynNotify(): void {
        console.error('------------------------------------OnGameDataSynNotify--------------------------.');
        this._bSwitchTable = false;
        this._videoCowboyRoom.showTheNewestTrend = true;
        this._vCoinOptimizationDeque.clear();
        this.clearRound(); // 清除场景动态信息
        // this.addEvent();

        // 更新场景静态信息
        this.resetLeftTimer();
        this._updateBetAmountLevel();
        this.updatBetButtonByCurCoin();
        this._updateBetButtonState();
        this._updateBetOddsDetail();
        this.updateSelfInfo();
        this.updateOtherPlayersInfo();

        this.updateHistoryResults();
        this.updateAllPlayerWinCount();

        // 根据不同的游戏状态恢复游戏场景
        if (this._videoCowboyRoom.gameState.roundState === network.RoundState.GAME_PENDING) {
            // 房间新建的，准备开局
            // do nothing
        } else if (this._videoCowboyRoom.gameState.roundState === network.RoundState.NEW_ROUND) {
            // 新的一局
            this.updateCards();
            this._updateAllWayOut();
        } else if (this._videoCowboyRoom.gameState.roundState === network.RoundState.BET) {
            // 下注
            this.updateCards();
            this._updateAllWayOut();
            this.updateAllBetAreas();
            this.updateBetCoutDown();

            // 下注剩余时间大于4s，显示出战动画
            if (this._leftTime > 0) {
                this.showFightBeginAnim();
                // 检测是否正在使用高级续投
                if (this._videoCowboyRoom.betSettings.canAdvanceAutoBet) {
                    this._checkAdvanceAutoReq();
                }
            } else {
                this.playKaiPai(null);
            }
        } else if (this._videoCowboyRoom.gameState.roundState === network.RoundState.SKIP_ROUND) {
            this.updateCards();
            this._updateAllWayOut();
        } else if (this._videoCowboyRoom.gameState.roundState === network.RoundState.SHOW_CARD) {
            this.updateCards();
            this._updateAllWayOut();
            this.updateAllBetAreas();
            this.updateBetCoutDown();
            // cv.MessageCenter.send('videoCowboy_ShowCardNotify', '0');
        } else if (this._videoCowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND) {
            // 处于结束期间并即将开启新的一局
            let isSpecial = this.isResultSpecialCardType();
            let _specialDuration = isSpecial ? this._specialCardTypeDuration : 0; // 特殊牌型动画时间

            this.showWaitForNextRoundInAnim();
            // cv.MessageCenter.send('videoCowboy_ShowCardNotify', '0');
            this._openCardLayer.updateCardType();
            this._openCardLayer.updateWinCards();

            if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration +
                    this._fightEndDuration +
                    this._betCountDownEndDuration
            ) {
                this._videoCowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                // this.showBetCoutDownEndAnim();
                this.showHandCardsAnim();
                this.playJieSuan();
                console.log('OnGameDataSynNotify, enter this.showBetCoutDownEndAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration +
                    this._fightEndDuration
            ) {
                this._videoCowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.playJieSuan();
                // this.showFightEndAnim();
                console.log('OnGameDataSynNotify, enter showFightEndAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration
            ) {
                this._videoCowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.showHandCardsAnim();
                this.playJieSuan();
                console.log('OnGameDataSynNotify, enter showHandCardsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration
            ) {
                this._videoCowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.updateAllCardsExceptPublicBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                // this.showPublicCardsAnim();
                this.showHandCardsAnim();
                this.playJieSuan();
                console.log('OnGameDataSynNotify, enter showPublicCardsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration
            ) {
                this._videoCowboyRoom.showTheNewestTrend = true;
                this.updateHistoryResultsPrevious();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.updateCards();
                this.showHideLoseBetCoinsAnim();
                this.showCowboyLoseAnim();
                this.playJieSuan();
                console.log('OnGameDataSynNotify, enter showHideLoseBetCoinsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration + _specialDuration
            ) {
                // added
                this._videoCowboyRoom.showTheNewestTrend = true;
                this.updateHistoryResultsPrevious();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.updateCards();
                this.clearLoseBetCoins();
                this.showCowboyLoseAnim();
                this.playJieSuan();
                if (isSpecial) {
                    this.showSpecialCardTypeAnim();
                } else {
                    this.showBetWinFlagsAndFlyCoinsAnim();
                }
                console.log('OnGameDataSynNotify, enter showSpecialCardTypeAnim/showBetWinFlagsAndFlyCoinsAnim');
            } else if (this._leftTime > this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration) {
                this._videoCowboyRoom.showTheNewestTrend = true;
                this.updateAllBetAreas();
                this.updateCards();
                this.clearLoseBetCoins();
                this.showCowboyLoseAnim();
                this.playJieSuan();
                if (isSpecial) {
                    this.showSpecialCardTypeAnim(
                        true,
                        this._leftTime - this._showNextRoundDuration - this._betWinFlagsAndFlyCoinsDuration
                    );
                } else {
                    this.showBetWinFlagsAndFlyCoinsAnim();
                }
                console.log(
                    'OnGameDataSynNotify, enter showSpecialCardTypeAnim/showBetWinFlagsAndFlyCoinsAnim, left time'
                );
            } else if (this._leftTime > this._showNextRoundDuration) {
                this._videoCowboyRoom.showTheNewestTrend = true;
                this.updateWinFlags();
                // this.updateAllBetAreas();
                // this.updateCards();
                // this.updateCardType();
                // this.updateWinCards();
                this.showNextRoundTips();
                console.log('OnGameDataSynNotify, enter showSpecialCardTypeAnim left time');
            } else {
                this._videoCowboyRoom.showTheNewestTrend = true;

                // this.updateAllBetAreas();
                // this.updateCards();
                // this.updateCardType();
                // this.updateWinCards();
                this.updateWinFlags(); // 显示win标记
                this.showNextRoundTips();
                console.log('OnGameDataSynNotify, enter showNextRoundTips');
            }

            // 显示路子
            if (this._leftTime > this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration) {
                this._updateAllWayOut(1);
            } else if (this._leftTime > this._showNextRoundDuration) {
                this._updateAllWayOut(1);
                this._showAllWayOutAnim();
            } else {
                this._updateAllWayOut();
            }
            this.hideKaiPaiSprite();
        }
    }

    // 一局结束
    OnGameRoundEndNotify(): void {
        this.hideKaiPaiSprite();
        this.playJieSuan();

        this.playPointAni();
        this._videoCowboyRoom.showTheNewestTrend = false;
        this.resetLeftTimer();

        // 下注倒计时结束动画	 . 开战动画	.	翻牌动画 . 显示win标记，金币收回动画	. 等待下一局动画
        // this.showBetCoutDownEndAnim();

        // // 隐藏高级续投选择面板
        // if (this._humanboyAdvancedAuto) {
        //     this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAuto).hideSelectPanel(false);
        // }

        this.showHandCardsAnim();

        this._openCardLayer.updateCardType();
        this._openCardLayer.updateWinCards();
    }

    // 新的一局
    OnDealNotify(): void {
        this._videoCowboyRoom.showTheNewestTrend = true;
        if (this._openCardLayer) {
            this._openCardLayer.setMode(true);
        }
        this.clearRound();
        this.resetLeftTimer();
        this.updatBetButtonByCurCoin();
        this._updateBetButtonState();
        this.updateSelfCoin();
        this.updateOtherPlayersInfo();
        this.showCowboyNormalAnim();
        this.showSendCardTips();
        this.updateAllPlayerWinCount();
        this.handleCoin();
        this._updateAllWayOut();

        // 开局动画	.	发牌动画/翻牌动画		. 出战动画
        this.showRoundStartAnim();
    }

    // 开始下注
    OnStartBetNotify(): void {
        this._videoCowboyRoom.showTheNewestTrend = true;
        this.resetLeftTimer();
        this.updatBetButtonByCurCoin();
        // this._updateBetButtonState();
        this.hideGameTips();
        // 下注倒计时开始动画
        // this.showBetCoutDownBeginAnim();
        // 检测是否正在使用高级续投
        // this._checkAdvanceAutoReq();
        this.showFightBeginAnim();
    }

    OnBetNotify(bet: PlayerOneBet): void {
        this._updateAutoBetBtnStatus();
        // this.showBetInAnim();

        // 自己筹码变化后判断一下下注筹码状态
        if (bet.uid === this._authService.currentUser.userId) {
            this._updateBetButtonState();
        }

        let tempData = new PlayerOneBet();
        tempData.betAmount = bet.betAmount;
        tempData.betOption = bet.betOption;
        tempData.uid = bet.uid;
        this._vCoinOptimizationDeque.push_back(tempData);
    }

    OnAutoBetNotify(bets: PlayerOneBet[]): void {
        // this.showBetInAnim(true);
        bets.forEach((bet) => {
            let tempData = new PlayerOneBet();
            tempData.betAmount = bet.betAmount;
            tempData.betOption = bet.betOption;
            tempData.uid = bet.uid;
            this._vCoinOptimizationDeque.push_back(tempData);
        });
        let betSize = pf.DataUtil.getArrayLength(bets);
        if (betSize > 1) {
            this.playSoundEffect(macros.Audio.BET_MANY);
        } else {
            this.playSoundEffect(macros.Audio.BET);
        }
        this._updateBetButtonState();
    }

    // OnAutoBetNotifyHandleOver(pSender: number): void {
    //     let betSize = pSender;
    //     if (betSize > 1) {
    //         this.playCowboyEffect(this.s_betin_many);
    //     } else {
    //         this.playCowboyEffect(this.s_betin);
    //     }
    //     this._updateBetButtonState();
    // }

    // OnLeaveRoomSucc(pSender: any): void {
    //     this.cleanData();
    //     this.playCowboyBGM();
    //     this.stopCowboyBGM();
    //     this.backToCowboyListScene();
    // }

    OnAutoBetSucc(): void {
        this._updateBetButtonState();
    }

    _onMsgBetAmountLevelChange(sender: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    _onMsgAdvanceAutobetSet(sender: any): void {
        this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);

        // 如果本局没有下注,且已勾选续投局数,则本局就生效一次
        if (!this._videoCowboyRoom.roundInfo.hasBetInCurRound && this._videoCowboyRoom.betSettings.canAutoBet) {
            this._checkAdvanceAutoReq();
        }
    }

    _onMsgAdvanceAutobet(sender: number): void {
        let code = sender;
        switch (code) {
            case network.ErrorCode.OK:
                break;

            // 高级续投超出限红
            case network.ErrorCode.AUTO_BET_EXCEED_LIMIT:
                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto
                        .getComponent(HumanboyAdvancedAutoControl)
                        .adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto
                        .getComponent(HumanboyAdvancedAutoControl)
                        .showAdvanceAutoTips(
                            pf.languageManager.getString(pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code))
                        );
                }
                break;

            // 高级续投金额不足
            case network.ErrorCode.AUTO_BET_NO_MONEY:
                if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
                    cr.commonResourceAgent.commonDialog.showMsg(
                        pf.languageManager.getString(pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code)),
                        [
                            pf.languageManager.getString('TipsPanel_sure_button'),
                            pf.languageManager.getString('TipsPanel_cancel_button')
                        ],
                        null
                    );
                } else {
                    let strNodeName = 'cowboy_dialog_recharge';
                    let dialogNode = this.node.getChildByName(strNodeName);
                    if (!dialogNode) {
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const stringContent = pf.languageManager.getString(
                            pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code)
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_cancel');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_recharge');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.videoCowboyNet.ReqCancelAdvanceAutoBet();
                            this._videoCowboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            this.openShop(null);
                        };

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: false,
                            legacyDialog: dialogNode.getComponent(HumanboyDialogControl),
                            isShowBtnCenter: false
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                        dialogNode.name = strNodeName;
                    }
                }
                break;

            default:
                // cv.MessageCenter.send('on_cowboy_server_error', code);
                this.OnServerError(code);
                break;
        }

        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }

    _onMsgAdvanceAutobetCancel(sender: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    OnRoomParamChangeNotify(): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    _onMsgAdvanceAutobetLimitReached(addedCount: number): void {
        if (addedCount) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.StringUtil.formatC(pf.languageManager.getString('MiniGame_btn_desc_auto_bet_reached'), addedCount)
            );
        }
    }

    OnServerError(pSender: number): void {
        let i32Error = pSender;

        let acBuffer: string = pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', i32Error);
        console.log('%s', acBuffer);

        if (i32Error === network.ErrorCode.BET_TOO_SMALL) {
            let errText = pf.StringUtil.formatC('%s', pf.languageManager.getString(acBuffer));
            let formatCoin = cr.CurrencyUtil.convertToClientAmount(this._videoCowboyRoom.roomParams.smallBet);
            this.showCowboyToast(
                pf.StringUtil.formatC(errText, cr.CurrencyUtil.clientAmountToDisplayString(formatCoin))
            );
        } else if (i32Error === network.ErrorCode.NO_BET) {
            // 忽略提示:已停止下注
        } else if (i32Error === network.ErrorCode.CAN_NOT_LEAVE_IN_BETTING) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString(acBuffer));

            // possible scenario: push recharge button while there are still unsettled bets
            // isSelfRecharge needs to be reset or exiting game will lead to recharge flow
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            context.isSelfRecharge = false;
        } else {
            const errorDesc = pf.languageManager.getString(acBuffer);
            if (errorDesc) {
                this.showCowboyToast(pf.StringUtil.formatC('%s', errorDesc));
            } else {
                this.showCowboyToast(acBuffer);
            }
        }
    }

    OnKickNotify(pSender: network.IKickNotify): void {
        let kickType = pSender.kickType;
        if (pSender.idle_roomid > 0) {
            if (!this._bSwitchTable) {
                this._videoCowboyRoom.roundInfo.idleRoomId = pSender.idle_roomid;
                this.showSwitchTable();
            }
            return;
        }
        if (kickType === network.Kick.IDLE_LONG_TIME) {
            this.backToMainScene(
                pf.StringUtil.formatC('%s', pf.languageManager.getString('Cowboy_server_kick_long_time_text'))
            );
        } else if (kickType === network.Kick.Stop_World) {
            this.backToMainScene(
                pf.StringUtil.formatC('%s', pf.languageManager.getString('Cowboy_server_kick_stop_world_text'))
            );
        }
    }

    OnSoundSwitchNotify(): void {
        // if (cr.UIUtil.isPlayMusic()) {
        //     this.playCowboyBGM();
        // } else {
        //     this.stopCowboyBGM();
        // }
        pf.audioManager.enableMusic = pf.localStorage.getItem(macros.AudioSettingKeys.MUSIC) !== 'false';
        pf.audioManager.enalbeSoundEffect = pf.localStorage.getItem(macros.AudioSettingKeys.SOUND_EFFECT) !== 'false';

        pf.audioManager.playMusic(macros.Audio.BGM);
    }

    // OnSelfInfo(): void {
    //     if (_minitary == null) {
    //         Size winSize = Director. getInstance().getWinSize();
    //         _minitary = static_cast < CowboyMilitary *> (CowboyMilitary. createLayer());
    //         //this._chart.setPosition(Vec2(winSize.width / 2, winSize.height / 2));
    //         addChild(_minitary);
    //     }
    //     else {
    //         _minitary..node.active = (true);
    //         //_minitary.setData();
    // }
    // }

    // OnJoinRoomFailed(pSender: number): void {
    //     let i32Error = pSender;
    //     let acBuffer = pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', i32Error);

    //     this.backToMainScene(pf.StringUtil.formatC('%s', pf.languageManager.getString(acBuffer)));
    // }

    OnCowboyRewardTips(value: string): void {
        if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
            this._rewardTips.fontSize = 26;
            this._rewardTips.lineHeight = 26;
        } else {
            this._rewardTips.fontSize = 22;
            this._rewardTips.lineHeight = 22;
        }
        this._rewardTips.node.width = this._rewardSize.width;
        this._rewardTips.string = cr.UIUtil.calculateAutoWrapString(this._rewardTips.node, value);
        this._rewardPanel.active = true;
        this._rewardPanel.stopAllActions();
        this._rewardPanel.runAction(
            cc.sequence(
                cc.delayTime(4.0),
                cc.callFunc(() => {
                    this._rewardPanel.active = false;
                })
            )
        );
    }

    // onBtnTopBgClick(event: cc.Event) {
    //     cv.videoCowboyNet.RequestTrend();
    //     this._videoCowboyChart.active = true;
    //     this.playCowboyEffect(this.s_button);
    // }

    _onMsgUpdateWorldServerGold(isForce: boolean = false): void {
        // world服接收接口已过滤只发自己, 因此这里无需再次判断(同时没有别的需求, 所以也不用缓存下发的结构)
        const checkedIsForce = isForce === true ? true : false;
        let llCurGold = this._authService.currentUser.totalAmount;

        // 结算阶段跳过(否则会提前知道输赢结果)
        if (this._videoCowboyRoom.canUpdateWorldServerGold || isForce) {
            // 更新自己金币信息
            this._videoCowboyRoom.selfPlayer.curCoin = llCurGold;
            this.updateSelfCoin();

            // 更新其他人信息(因为自己有可能会在8人列表中)
            let bOnMainPlayerList = false;
            let otherPlayersInfo = this._videoCowboyRoom.otherPlayers;
            let otherInfoLen = otherPlayersInfo.length;
            for (let i = 0; i < otherInfoLen; ++i) {
                if (this._authService.currentUser.userId === otherPlayersInfo[i].uid) {
                    bOnMainPlayerList = true;
                    otherPlayersInfo[i].curCoin = llCurGold;
                }
            }
            if (bOnMainPlayerList) {
                this.updateOtherCoin();
            }
        }
    }

    _initWayOutInfoByAreaIdx(iAreaIdx: number): void {
        if (iAreaIdx < 0 || iAreaIdx >= pf.DataUtil.getArrayLength(this._betAreas)) return;

        let panelWayOut = this._betAreas[iAreaIdx].getChildByName('panel_way_out');
        if (!panelWayOut) return;

        if (panelWayOut.getComponent(cc.Mask)) {
            panelWayOut.removeComponent(cc.Mask);
        }

        let tWayOutInfo = new CowboyWayOutInfo();
        this._mapWayOutInfo.set(iAreaIdx, tWayOutInfo);

        tWayOutInfo.iAreaIdx = iAreaIdx;
        tWayOutInfo.panelWayOut = panelWayOut;
        tWayOutInfo.panelWayOut.on(cc.Node.EventType.TOUCH_END, (): void => {
            return;
            // 点击路子入口事件
            // TODO 原邏輯註解掉了
            // cv.videoCowboyNet.RequestTrend();
            // this._videoCowboyChart.active = true;
            // cv.MessageCenter.send('on_display_page2');
            // this.playCowboyEffect(this.s_button);
        });

        // 路子球状图片
        do {
            let children = tWayOutInfo.panelWayOut.children;
            let count = children.length;
            for (let i_wayout_index = 0; i_wayout_index < count; ++i_wayout_index) {
                let strImgName = pf.StringUtil.formatC('img_%d', i_wayout_index);
                let img = tWayOutInfo.panelWayOut.getChildByName(strImgName);
                if (img) {
                    img.active = false;
                    tWayOutInfo.vWayOutImg.push(img);
                    tWayOutInfo.vWayOutImgSrcPos.push(img.getPosition());
                }
            }
        } while (0);

        // 文本
        do {
            let txt = tWayOutInfo.panelWayOut.getChildByName('txt_way_out');
            if (txt) {
                if (!tWayOutInfo.rtxtWayOut) tWayOutInfo.rtxtWayOut = new cc.Node().addComponent(cc.RichText);
                tWayOutInfo.rtxtWayOut.fontSize = txt.getComponent(cc.Label).fontSize;
                tWayOutInfo.rtxtWayOut.node.setAnchorPoint(txt.getAnchorPoint());
                tWayOutInfo.rtxtWayOut.node.setContentSize(txt.getContentSize());
                tWayOutInfo.rtxtWayOut.node.angle = txt.angle;
                tWayOutInfo.rtxtWayOut.node.setPosition(txt.getPosition());
                tWayOutInfo.rtxtWayOut.node.active = false;
                tWayOutInfo.rtxtWayOut.handleTouchEvent = false;

                txt.getParent().addChild(tWayOutInfo.rtxtWayOut.node);
                txt.removeFromParent(true);
                txt.destroy();
            }
        } while (0);

        // 路子显示风格
        do {
            let option = this.getBetOptionByAreaIdx(iAreaIdx);
            switch (option) {
                // case cowboy_proto.RED_WIN:					tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;	break; // 牛仔胜利
                case network.BetZoneOption.EQUAL:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break; // 平
                // case cowboy_proto.BLUE_WIN:					tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;	break; // 小牛胜利
                case network.BetZoneOption.HOLE_3_TONG_SAME_SHUN:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 顺子/同花/同花顺
                case network.BetZoneOption.FIVE_NONE_1DUI:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 高牌/一对
                case network.BetZoneOption.FIVE_2DUI:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 两对
                case network.BetZoneOption.HOLE_SAME:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break; // 对子
                case network.BetZoneOption.HOLE_A:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break; // 对A
                case network.BetZoneOption.FIVE_3_SHUN_TONG_HUA:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 三条/顺子/同花
                case network.BetZoneOption.FIVE_3_2:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break; // 葫芦
                case network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 300;
                    break; // 金刚/同花顺/皇家
                default:
                    break;
            }
        } while (0);
    }

    _clearWayOutInfo(): void {
        this._mapWayOutInfo.clear(); // 只是清除元素,内存并没有变化
    }

    /**
     * 路单滚动动画
     * @param iAreaIdx
     */
    _showWayOutMoveAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut: cc.Node = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        let vWayOutImg: cc.Node[] = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgSrcPos: cc.Vec2[] = this._mapWayOutInfo.get(iAreaIdx).vWayOutImgSrcPos;
        if (!panelWayOut || pf.DataUtil.getArrayLength(vWayOutImg) <= 0) return;

        // 裁剪右移 模式
        // let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
        // for (let i = 0; i < vWayOutImg.length; ++i) {
        //     if (i === 0) {
        //         tarPos.x = vWayOutImgSrcPos[i].x - vWayOutImg[iAreaIdx].width * vWayOutImg[iAreaIdx].scaleX;
        //         tarPos.y = vWayOutImgSrcPos[i].y;
        //     }
        //     else {
        //         tarPos.x = vWayOutImgSrcPos[i - 1].x;
        //         tarPos.y = vWayOutImgSrcPos[i - 1].y;
        //     }

        //     vWayOutImg[i].runAction(cc.sequence(cc.moveTo(0.3, tarPos), cc.callFunc((): void => {
        //         if (i === vWayOutImg.length - 1) {
        //             this._updateWayOut(iAreaIdx, 0);
        //         }
        //     }, this)));
        // }

        // 缩小渐隐右移 模式
        let st: cc.ActionInterval = cc.scaleTo(0.2, 0);
        let fo: cc.ActionInterval = cc.fadeOut(0.3);
        let spawn: cc.FiniteTimeAction = cc.spawn(st, fo);
        vWayOutImg[0].runAction(
            cc.sequence(
                spawn,
                cc.callFunc((): void => {
                    vWayOutImg[0].active = false;
                    let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
                    for (let i = 0; i < vWayOutImg.length; ++i) {
                        if (i === 0) continue;

                        tarPos.x = vWayOutImgSrcPos[i - 1].x;
                        tarPos.y = vWayOutImgSrcPos[i - 1].y;
                        vWayOutImg[i].runAction(
                            cc.sequence(
                                cc.moveTo(0.5, tarPos),
                                cc.callFunc((): void => {
                                    if (i === vWayOutImg.length - 1) {
                                        this._updateWayOut(iAreaIdx, 0);
                                        vWayOutImg[0].setScale(1.0);
                                        vWayOutImg[0].opacity = 0xff;
                                        vWayOutImg[0].active = true;
                                    }
                                }, this)
                            )
                        );
                    }
                })
            )
        );
    }

    _showWayOutImgAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgLen = pf.DataUtil.getArrayLength(vWayOutImg);
        if (vWayOutImgLen <= 0) return;

        // let mapZoneData = this._videoCowboyRoom.mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._videoCowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 该区域输赢(0 - 未击中, 1 - 击中)
        let result = zoneData.optionResult.result;

        let fileName = '';

        // 输
        if (result === 0) {
            fileName = 'cowboy_icon_circle_small_gray';
        }
        // 赢
        else if (result === 1) {
            let check = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN;
            if (check) {
                fileName = 'cowboy_icon_circle_small_red';
            } else {
                fileName = 'cowboy_icon_circle_small_red_en';
            }
        }

        // 计算空闲路子索引
        let freeIndex = vWayOutImg.length;
        for (let i = 0; i < freeIndex; ++i) {
            if (!vWayOutImg[i].active) {
                freeIndex = i;
                break;
            }
        }

        // 路子满了挤动动画(老模式)
        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.6 * this._fActExecute_WayOut), cc.callFunc(function () {
        //     if (freeIndex > vWayOutImgLen - 1) {
        //         this._showWayOutMoveAnim(iAreaIdx);
        //     }
        //     else {
        //         vWayOutImg[freeIndex].active = true;
        //         VideoCowboyManager.loadSpriteTextureByPlist(this.game_dznz_PLIST, vWayOutImg[freeIndex].getComponent(cc.Sprite), fileName);
        //         // vWayOutImg[freeIndex].ignoreContentAdaptWithSize(true);
        //     }
        // }.bind(this))));

        // 路子满了挤动动画
        if (freeIndex > vWayOutImgLen - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        this._showWayOutMoveAnim(iAreaIdx);
                    })
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        vWayOutImg[freeIndex].active = true;
                        // VideoCowboyManager.loadSpriteTextureByPlist(
                        //     this.game_dznz_PLIST,
                        //     vWayOutImg[freeIndex].getComponent(cc.Sprite),
                        //     fileName
                        // );
                        vWayOutImg[freeIndex].getComponent(cc.Sprite).spriteFrame =
                            this.game_dznz_PLIST.getSpriteFrame(fileName);
                    })
                )
            );
        }
    }

    _showWayOutAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgLen = pf.DataUtil.getArrayLength(vWayOutImg);
        if (!panelWayOut || vWayOutImgLen <= 0) return;

        // let mapZoneData = this._videoCowboyRoom.mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._videoCowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 路子显示风格
        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, 1);
                this._showWayOutImgAnim(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > vWayOutImg.length) {
                        let bDefeat = true;
                        let vWayOutImgLen = vWayOutImg.length;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, 1);
                        this._showWayOutImgAnim(iAreaIdx);
                    }
                }
                break;

            default:
                break;
        }
    }

    _showAllWayOutAnim(): void {
        this._mapWayOutInfo.forEach((value: CowboyWayOutInfo, key: number) => {
            this._showWayOutAnim(key);
        });
    }

    _updateWayOutImg(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        panelWayOut.active = true;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgSrcPos = this._mapWayOutInfo.get(iAreaIdx).vWayOutImgSrcPos;

        // let mapZoneData = this._videoCowboyRoom.mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._videoCowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 逆序取历史记录
        let fileName = '';
        // let vHistoryResults = it_zoneData.vHistoryResults;
        let vHistoryResults = zoneData.optionResult.historyResults;

        let vWayOutImgLen = pf.DataUtil.getArrayLength(vWayOutImg);
        let vHistoryResultsLen = pf.DataUtil.getArrayLength(vHistoryResults);
        let min_count = vWayOutImgLen < vHistoryResultsLen ? vWayOutImgLen : vHistoryResultsLen;
        let end_index = 0;
        let end_count = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (vWayOutImgLen >= vHistoryResultsLen) {
            end_index = min_count - 1;
            end_count = min_count - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            end_index = min_count - 1 + reduce;
            end_count = min_count;
        }

        for (let i = 0; i < vWayOutImgLen; ++i) {
            // 复原位置
            vWayOutImg[i].setPosition(vWayOutImgSrcPos[i]);

            let index = end_index - i;
            if (i < end_count && index >= 0 && index < vHistoryResultsLen) {
                vWayOutImg[i].active = true;

                // 该区域输赢(0 - 未击中, 1 - 击中)
                let result = vHistoryResults[index];
                if (result === 0) {
                    fileName = 'cowboy_icon_circle_small_gray';
                } else if (result === 1) {
                    let check = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN;
                    if (check) {
                        fileName = 'cowboy_icon_circle_small_red';
                    } else {
                        fileName = 'cowboy_icon_circle_small_red_en';
                    }
                }
                // VideoCowboyManager.loadSpriteTextureByPlist(
                //     this.game_dznz_PLIST,
                //     vWayOutImg[i].getComponent(cc.Sprite),
                //     fileName
                // );
                vWayOutImg[i].getComponent(cc.Sprite).spriteFrame = this.game_dznz_PLIST.getSpriteFrame(fileName);
                // vWayOutImg[i].ignoreContentAdaptWithSize(true);
            } else {
                vWayOutImg[i].active = false;
            }
        }
    }

    _updateWayOutTxt(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (!rtxtWayOut) return;

        // 隐藏路单球图片面板
        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let iWayOutLoseLimitCount = this._mapWayOutInfo.get(iAreaIdx).iWayOutLoseLimitCount;
        let vWayOutImgLen = pf.DataUtil.getArrayLength(vWayOutImg);
        for (let i = 0; i < vWayOutImgLen; ++i) {
            vWayOutImg[i].active = false;
        }

        let eCurState = this._videoCowboyRoom.gameState.roundState;
        // let mapZoneData = this._videoCowboyRoom.mapZoneData;
        let mapZoneData = this._videoCowboyRoom.betZones;

        let it = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (it) {
            // 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
            let luckLoseHand = it.optionResult.luckLoseHand;

            if (luckLoseHand < 0) {
                rtxtWayOut.string = '';
            } else if (luckLoseHand === 0) {
                if (eCurState === network.RoundState.WAIT_NEXT_ROUND) {
                    cr.UIUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_txt')
                    );
                } else {
                    cr.UIUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_last_txt')
                    );
                }
            } else {
                let strCountDest = '';
                if (iWayOutLoseLimitCount !== 0 && luckLoseHand > iWayOutLoseLimitCount) {
                    strCountDest = pf.StringUtil.formatC('%d+', iWayOutLoseLimitCount);
                } else {
                    strCountDest = pf.StringUtil.formatC('%d', luckLoseHand);
                }
                cr.UIUtil.setRichTextString(
                    rtxtWayOut.node,
                    pf.StringUtil.formatC(pf.languageManager.getString('Cowboy_game_wayout_lose_txt'), strCountDest)
                );
            }

            let szParent = rtxtWayOut.node.getParent().getContentSize();
            let szTextNode = rtxtWayOut.node.getContentSize();

            rtxtWayOut.node.active = true;
            // rtxtWayOut.node.setPosition((szParent.width - szTextNode.width) / 2, (szParent.height - szTextNode.height) / 2);
        }
    }

    _updateWayOut(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        // let mapZoneData = this._videoCowboyRoom.mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._videoCowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, reduce);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults = zoneData.optionResult.historyResults;
                    let vWayOutImgLen = pf.DataUtil.getArrayLength(vWayOutImg);
                    let vHistoryResultsLen = pf.DataUtil.getArrayLength(vHistoryResults);
                    if (vHistoryResultsLen > 0 && vHistoryResultsLen > vWayOutImgLen) {
                        let bDefeat = true;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, reduce);
                    }
                }
                break;

            default:
                break;
        }
    }

    _updateAllWayOut(reduce?: number /* = 0 */): void {
        const checkedReduce = reduce === undefined ? 0 : reduce;
        this._mapWayOutInfo.forEach((value: CowboyWayOutInfo, key: number) => {
            this._updateWayOut(key, checkedReduce);
        });
    }

    initCowboyAnims(): void {
        this._cowWinAnim = this.initAni(this._heroCow.node.getParent(), this.cow_win_prefab);
        this._cowWinAction = this._cowWinAnim.getComponent(cc.Animation);

        this._cowLoseAnim = this.initAni(this._heroCow.node.getParent(), this.cow_lose_prefab);
        this._cowLoseAction = this._cowLoseAnim.getComponent(cc.Animation);

        this._boyWinAnim = this.initAni(this._heroBoy.node.getParent(), this.boy_win_prefab);
        this._boyWinAction = this._boyWinAnim.getComponent(cc.Animation);

        this._boyLoseAnim = this.initAni(this._heroBoy.node.getParent(), this.boy_lose_prefab);
        this._boyLoseAction = this._boyLoseAnim.getComponent(cc.Animation);

        this._cowWinAnim.zIndex = -1;
        this._cowLoseAnim.zIndex = -1;
        this._boyWinAnim.zIndex = -1;
        this._boyLoseAnim.zIndex = -1;
    }

    getTimelineAnimSpeed(atl: cc.Animation, fExecuteTime: number): number {
        let fRet = 0;
        if (atl && fExecuteTime > 0) {
            let frameInternal = 1 / 60.0;
            fRet = (atl.defaultClip.duration * frameInternal) / fExecuteTime;
        }
        return fRet;
    }
    setTimeSpeed(ani: cc.Animation, speed: number) {
        ani.defaultClip.speed = speed;
    }
    // 牛仔输时哭的动画
    showCowboyLoseAnim(): void {
        this.showCowboyNormalAnim();
        this.playSoundEffect(this.s_win_lose);

        // 0 平, 1 牛仔胜, -1 小牛胜
        if (this._videoCowboyRoom.roundInfo.roundResult.result === 1) {
            // 牛仔赢
            do {
                this._heroBoy.node.active = false;
                let pos = this._heroBoy.node.getPosition();

                this._boyWinAnim.active = true;
                this._boyWinAnim.setPosition(pos);
                let endIndex = this._boyWinAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._boyWinAction, this._fActExecute_BoyWin);
                // this.setTimeSpeed(this._boyWinAction,speed);
                this.gotoFrameAndPlay(this._boyWinAction, 0, endIndex, false);
                this._boyWinAction.on('finished', (event: cc.Event): void => {
                    this._boyWinAction.node.off('finished');
                    this._boyWinAnim.active = false;
                    this._heroBoy.node.active = true;
                });
            } while (0);

            // 牛输
            do {
                this._heroCow.node.active = false;
                let pos = this._heroCow.node.getPosition();

                this._cowLoseAnim.active = true;
                this._cowLoseAnim.setPosition(pos);
                let endIndex = this._cowLoseAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._cowLoseAction, this._fActExecute_CowLose);
                // this.setTimeSpeed(this._cowLoseAction,speed);
                this.gotoFrameAndPlay(this._cowLoseAction, 0, endIndex, false);
                this._cowLoseAction.on('finished', (event: cc.Event): void => {
                    this._cowLoseAction.node.off('finished');
                    this._cowLoseAnim.active = false;
                    this._heroCow.node.active = true;
                });
            } while (0);
        } else if (this._videoCowboyRoom.roundInfo.roundResult.result === -1) {
            // 牛赢
            do {
                this._heroCow.node.active = false;
                let pos = this._heroCow.node.getPosition();

                this._cowWinAnim.active = true;
                this._cowWinAnim.setPosition(pos);
                let endIndex = this._cowWinAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._cowWinAction, this._fActExecute_CowWin);
                // this.setTimeSpeed(this._cowWinAction,speed);
                this.gotoFrameAndPlay(this._cowWinAction, 0, endIndex, false);
                this._cowWinAction.on('finished', (event: cc.Event): void => {
                    this._cowWinAction.node.off('finished');
                    this._cowWinAnim.active = false;
                    this._heroCow.node.active = true;
                });
            } while (0);

            // 牛仔输
            do {
                this._heroBoy.node.active = false;
                let pos = this._heroBoy.node.getPosition();

                this._boyLoseAnim.active = true;
                this._boyLoseAnim.setPosition(pos);
                let endIndex = this._boyLoseAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._boyLoseAction, this._fActExecute_BoyLose);
                // this.setTimeSpeed(this._boyLoseAction,speed);
                this.gotoFrameAndPlay(this._boyLoseAction, 0, endIndex, false);
                this._boyLoseAction.on('finished', (event: cc.Event): void => {
                    this._boyLoseAction.node.off('finished');
                    this._boyLoseAnim.active = false;
                    this._heroBoy.node.active = true;
                });
            } while (0);
        }
    }

    // 牛仔恢复正常动画
    showCowboyNormalAnim(): void {
        this._heroBoy.node.active = true;
        this._heroCow.node.active = true;

        // 重置牛仔输赢动画
        do {
            this._cowWinAction.stop();
            this._cowWinAnim.active = false;

            this._cowLoseAction.stop();
            this._cowLoseAnim.active = false;

            this._boyWinAction.stop();
            this._boyWinAnim.active = false;

            this._boyLoseAction.stop();
            this._boyLoseAnim.active = false;
        } while (0);
    }

    private showLuckButton() {
        if (!this._luckButton) {
            // this._luckButton = cc.instantiate(this.luckButton_prefab).getComponent(LuckTurntableButtonControl);
            const luckButtonPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(
                macros.Assets.LUCK_TURNTABLE_BUTTON
            );
            this._luckButton = cc.instantiate(luckButtonPrefab).getComponent(LuckTurntableButtonControl);
            this._btn_redpacket_festival.addChild(this._luckButton.node);
            this._luckButton.node.setPosition(0, 0);
            let pos: cc.Vec2 = this._selfCoin.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
            this._luckButton.setViewData(pos);
        }
        // isShowLuckTurntables = cv.dataHandler.getUserData().isShowLuckTurntables
        if (this._luckTurntableService.isShowLuckTurntable) {
            this._btn_redpacket_festival.active = true;
            this._luckButton.updateView(true, this._btn_redpacket_festival_layer);
        } else {
            this._btn_redpacket_festival.active = false;
        }

        // "红包节"提示层是否显隐
        this._btn_redpacket_festival_layer.active = this._btn_redpacket_festival.active;

        // "红包节"状态有变化, 适配底栏按钮位置
        this.adaptiveBetBtnPanel();
    }

    private onTurntableResultNotice(userId: number) {
        let list: cc.Node[] = this.getPlayerCoinNodesByUid(userId);
        // 桌面没有该玩家
        if (list.length === 0) {
            list.push(this._btnPlayerList.node);
        }
        for (const node of list) {
            // let node = list[i];
            // let pos = node.getParent().convertToWorldSpaceAR(node.getPosition());
            // this._luckButton.showGoldMoveAction(pos, puf.currency_type);

            this._luckButton.runGoldMoveAction(this._btn_redpacket_festival, node);
        }
    }

    _HandleStopBetNotify(): void {
        this.hideKaiJuSprite();
        this.showBetCoutDownEndAnim();
        this._leftTime = 0;
        this._updateBetButtonState();
        // 隐藏高级续投选择面板
        this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
    }

    _HandleSkipRoundNotify() {
        this.hideKaiJuSprite();
        this.showBetClock(false);
        this.hideKaiPaiSprite();
        this.hideJieSuanSprite();
        let len = this._betCoinContents.length;
        for (let i = 0; i < len; i++) {
            this.hideAreaCoin(i, true);
        }
        let textNumLen = this._textTotalBetNum.length;
        for (let i = 0; i < textNumLen; i++) {
            let totalBet = pf.TypeUtil.toSafeNumber(this._textTotalBetNum[i].string);
            let myBet = pf.TypeUtil.toSafeNumber(this._textSelfBetNum[i].string);
            this.HandleReturnCoinToPlayers(i, myBet, true);
            this.HandleReturnCoinToPlayers(i, totalBet - myBet, false);
            this._textTotalBetNum[i].string = '';
            this._textSelfBetNum[i].string = '';
        }

        // 隐藏高级续投选择面板
        this._humanboyAdvancedAuto?.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
    }

    _HandleCancelRoundNotify() {
        this.hideKaiPaiSprite();
        if (this._openCardLayer) {
            this._openCardLayer.setMode(true);
            this._openCardLayer.reset();
        }
    }

    HandleReturnCoinToPlayers(areaIdx: number, coin: number, isMe: boolean): void {
        if (coin <= 0) return;
        let coinContent = this._betLineNode[areaIdx];
        let betDetails = this.getBetDetailAmounts(coin * 100);
        let headMidWorldPos: cc.Vec2;
        if (isMe) {
            headMidWorldPos = this._selfCoin.node.getParent().convertToWorldSpaceAR(this._selfCoin.node.getPosition());
        } else {
            headMidWorldPos = this._btnPlayerList.node
                .getParent()
                .convertToWorldSpaceAR(this._btnPlayerList.node.getPosition());
        }
        headMidWorldPos = this._nodeAnim.convertToNodeSpaceAR(headMidWorldPos);
        let len = pf.DataUtil.getArrayLength(betDetails);
        for (let k = 0; k < len; k++) {
            let flyCoin = this.createFlyCoin(areaIdx, betDetails[k], true);
            let coinFlyBorn: cc.Vec2 = coinContent.convertToWorldSpaceAR(
                this.getOneAreaPos(areaIdx, this.isCircleCoin(betDetails[k]))
            );
            coinFlyBorn = this._nodeAnim.convertToNodeSpaceAR(coinFlyBorn);
            this._nodeAnim.addChild(flyCoin.node);
            flyCoin.node.setPosition(coinFlyBorn);
            flyCoin.node.active = false;

            // 延迟一会(win动画结束)开始飞金币
            this.scheduleOnce(() => {
                flyCoin.node.active = true;
                flyCoin.node.runAction(
                    cc.sequence(
                        cc.delayTime(0.2 + k * 0.025),
                        cc.moveTo(0.6, headMidWorldPos).easing(cc.easeOut(0.8)),
                        cc.destroySelf()
                    )
                );
            }, 0.7);
        }
    }

    initCheckXianLu() {
        let xianLu_index = Number(pf.localStorage.getItem('xianLu_index'));
        xianLu_index = isFinite(xianLu_index) ? xianLu_index : 0;
        xianLu_index = xianLu_index === 0 ? 2 : xianLu_index; // 默认播放高清-中

        if (this._videoCowboyRoom.xianluList.length > xianLu_index - 1) {
            this._liveVideoComponent?.setUrl(this._videoCowboyRoom.xianluList[xianLu_index - 1]);
        }

        let btn_xianlu = this.node.getChildByName('btn_xianlu');
        this._btn_xianlu = btn_xianlu;
        btn_xianlu.zIndex = 2;
        // if (cv.config.IS_IPHONEX_SCREEN) {
        //     btn_xianlu.setPosition(btn_xianlu.x - pf.system.view.iphoneXOffset, btn_xianlu.y);
        // }
        if (this._IS_IPHONEX_SCREEN) {
            btn_xianlu.setPosition(btn_xianlu.x - pf.system.view.iphoneXOffset, btn_xianlu.y);
        }
        this._statusLayerPosNode = btn_xianlu.getChildByName('statusLayer_pos');

        this._videoStatusTips = btn_xianlu.getChildByName('Image_videoTips');
        let content_0 = this._videoStatusTips.getChildByName('content_0');
        this._videoStatusTips.zIndex = -3;
        this._videoStatusTips.active = false;
        let btn_xianlu_real = btn_xianlu.getChildByName('btn_xianlu');

        let Panel_1 = btn_xianlu.getChildByName('Panel_1');
        Panel_1.setScale(1.0, 72.0 / 300);
        Panel_1.zIndex = -2;
        let Button_current = btn_xianlu.getChildByName('Button_current');
        Button_current.zIndex = -1;
        Button_current.active = true;
        let btnCurrentLabelString = pf.languageManager.getString(
            pf.StringUtil.formatC('VideoCowboy_videoResource_Text_%d', xianLu_index - 1)
        );
        Button_current.getChildByName('Label').getComponent(cc.Label).string = btnCurrentLabelString;
        let content_0String = pf.languageManager.getString(
            pf.StringUtil.formatC('VideoCowboy_videoResource_Text_%d', xianLu_index - 1)
        );
        content_0.getComponent(cc.Label).string = content_0String;
        let Image_current = btn_xianlu.getChildByName('Image_current');
        Image_current.zIndex = -2;
        for (let i = 0; i < 3; i++) {
            let childName = pf.StringUtil.formatC('Button_%d', i);
            let tempBtn = Panel_1.getChildByName(childName);
            let btnLabelString = pf.languageManager.getString(
                pf.StringUtil.formatC('VideoCowboy_videoResource_Text_%d', i)
            );
            tempBtn.getChildByName('Label').getComponent(cc.Label).string = btnLabelString;
            tempBtn.on('click', (event: cc.Event) => {
                // cv.AudioMgr.playButtonSound('button_click');
                pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                if (this._videoCowboyRoom.xianluList.length > i) {
                    this._liveVideoComponent?.setUrl(this._videoCowboyRoom.xianluList[i]);
                }
                pf.localStorage.setItem('xianLu_index', pf.TypeUtil.toSafeString(i + 1));
                let btn = tempBtn;
                Button_current.getChildByName('Label').getComponent(cc.Label).string = btn
                    .getChildByName('Label')
                    .getComponent(cc.Label).string;
                content_0.getComponent(cc.Label).string = btn.getChildByName('Label').getComponent(cc.Label).string;

                Panel_1.runAction(
                    cc.sequence(
                        cc.scaleTo(0.3, 1.0, 72.0 / 300),
                        cc.callFunc(() => {
                            Panel_1.active = false;
                            // btn_xianlu_real.getComponent(cc.Button).interactable = (true);
                            if (!this._videoStatusTips.active) {
                                Button_current.active = true;
                                Image_current.active = true;
                            }
                        })
                    )
                );
            });
        }

        btn_xianlu_real.on('click', (event: cc.Event) => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            // btn_xianlu_real.getComponent(cc.Button).interactable = (false);
            if (!Panel_1.active) {
                Button_current.active = false;
                Image_current.active = false;
                Panel_1.active = true;
                Panel_1.runAction(cc.scaleTo(0.3, 1.0));
            } else {
                Panel_1.stopAllActions();
                Panel_1.runAction(
                    cc.sequence(
                        cc.scaleTo(0.3, 1.0, 72.0 / 300),
                        cc.callFunc(() => {
                            Panel_1.active = false;
                            // btn_xianlu_real.getComponent(cc.Button).interactable = (true);
                            if (!this._videoStatusTips.active) {
                                Button_current.active = true;
                                Image_current.active = true;
                            }
                        })
                    )
                );
            }
        });

        Button_current.on('click', (event: cc.Event) => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            // btn_xianlu_real.getComponent(cc.Button).interactable = (false);
            if (!Panel_1.active) {
                Button_current.active = false;
                Image_current.active = false;
                Panel_1.active = true;
                Panel_1.runAction(cc.scaleTo(0.3, 1.0));
            } else {
                Panel_1.stopAllActions();
                Panel_1.runAction(
                    cc.sequence(
                        cc.scaleTo(0.3, 1.0, 72.0 / 300),
                        cc.callFunc(() => {
                            Panel_1.active = false;
                            // btn_xianlu_real.getComponent(cc.Button).interactable = (true);
                            if (!this._videoStatusTips.active) {
                                Button_current.active = true;
                                Image_current.active = true;
                            }
                        })
                    )
                );
            }
        });

        this.node.on(cc.Node.EventType.TOUCH_START, (event: cc.Event) => {
            if (!Panel_1.active) return true;
            Panel_1.runAction(
                cc.sequence(
                    cc.scaleTo(0.3, 1.0, 72.0 / 300),
                    cc.callFunc(() => {
                        Panel_1.active = false;
                        //  btn_xianlu_real.getComponent(cc.Button).interactable = (true);
                        if (!this._videoStatusTips.active) {
                            Button_current.active = true;
                            Image_current.active = true;
                        }
                    })
                )
            );
        });
        // let touchListener = cc.EventListenerTouchOneByOne:: create();
        // touchListener.setSwallowTouches(false);
        // touchListener.onTouchBegan = [=](Touch * touch, Event * event).bool {
        //     if (!Panel_1.isVisible()) return true;
        //     Panel_1.runAction(cc.sequence(cc.scaleTo(0.3, 1.0f, 72.0 / 300), cc.callFunc(() => {
        //         Panel_1.active = (false);
        //         btn_xianlu.setTouchEnabled(true);
        //         if (!this._videoStatusTips.isVisible()) {
        //             Button_current.active = (true);
        //             Image_current.active = (true);
        //         }
        //     }), nullptr));
        //     return true;
        // };
        // Director:: getInstance().getEventDispatcher().addEventListenerWithSceneGraphPriority(touchListener, Panel_1);
    }

    onExitCowboyLiveVideo() {
        if (this._cowboyExit) {
            this._cowboyExit.active = false;
        }
    }

    showVideoStatusTips() {
        if (!this._videoStatusTips) return;

        let content = this._videoStatusTips.getChildByName('content').getComponent(cc.Label);
        if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
            content.fontSize = 26;
        } else {
            content.fontSize = 24;
        }
        content.string = pf.languageManager.getString('VideoCowboy_videoTips');

        this._videoStatusTips.stopAllActions();
        this._videoStatusTips.scaleX = 0.3; // 待验证

        let Panel_1 = this._btn_xianlu.getChildByName('Panel_1');
        let Button_current = this._btn_xianlu.getChildByName('Button_current');
        let Image_current = this._btn_xianlu.getChildByName('Image_current');

        Button_current.active = false;
        Image_current.active = false;

        this._videoStatusTips.runAction(
            cc.sequence(
                cc.scaleTo(0.2, 1.0, 1.0),
                cc.delayTime(5.0),
                cc.scaleTo(0.3, 0.3, 1.0),
                cc.callFunc(() => {
                    this._videoStatusTips.active = false;
                    if (!Panel_1.active) {
                        Button_current.active = true;
                        Image_current.active = true;
                    }
                })
            )
        );
    }

    _initAreaLineData() {
        this._betLineNode = [];
        this.lineInfoArr = [];
        for (let i = 0; i < 2; i++) {
            this.lineInfoArr.push([]);
            for (let j = 0; j < 8; j++) {
                this.lineInfoArr[i].push(new BetAreaLineInfo());
            }
        }

        let areaNum = this._betAreas.length;
        for (let k = 0; k < 2; k++) {
            for (let i = 0; i < areaNum; i++) {
                let node = this._betAreas[i].getChildByName('Node_corner');
                node.active = false;
                let count = node.childrenCount;
                let vecPos: cc.Vec2[] = []; // 顺时针的点数组
                if (k === 0) {
                    this._betLineNode.push(node);
                    for (let n = 0; n < count / 2; n++) {
                        let childName = pf.StringUtil.formatC('Image_%d', n);
                        let img = node.getChildByName(childName);
                        if (img) {
                            vecPos.push(img.getPosition());
                        }
                    }
                } else {
                    for (let n = count / 2; n < count; n++) {
                        let childName = pf.StringUtil.formatC('Image_%d', n);
                        let img = node.getChildByName(childName);
                        if (img) {
                            vecPos.push(img.getPosition());
                        }
                    }
                }

                let minX = vecPos[0].x;
                let minY = vecPos[0].y;
                let maxX = vecPos[0].x;
                let maxY = vecPos[0].y;
                let vecPosLen = vecPos.length;
                for (let j = 0; j < vecPosLen; j++) {
                    if (vecPos[j].x < minX) {
                        minX = vecPos[j].x;
                    }
                    if (vecPos[j].x > maxX) {
                        maxX = vecPos[j].x;
                    }
                    if (vecPos[j].y < minY) {
                        minY = vecPos[j].y;
                    }
                    if (vecPos[j].y > maxY) {
                        maxY = vecPos[j].y;
                    }
                }

                this.lineInfoArr[k][i].minX = minX;
                this.lineInfoArr[k][i].minY = minY;
                this.lineInfoArr[k][i].maxX = maxX;
                this.lineInfoArr[k][i].maxY = maxY;

                this.lineInfoArr[k][i].aArr = [];
                this.lineInfoArr[k][i].bArr = [];

                for (let j = 0; j < vecPosLen; j++) {
                    // y = a* x + b
                    let m = j + 1 < vecPosLen ? j + 1 : 0;
                    let a = (vecPos[m].y - vecPos[j].y) / (vecPos[m].x - vecPos[j].x);
                    let b = vecPos[j].y - a * vecPos[j].x;
                    this.lineInfoArr[k][i].aArr.push(a);
                    this.lineInfoArr[k][i].bArr.push(b);
                    if (vecPos[m].x > vecPos[j].x) {
                        this.lineInfoArr[k][i].x1.push(vecPos[j].x);
                        this.lineInfoArr[k][i].x2.push(vecPos[m].x);
                    } else {
                        this.lineInfoArr[k][i].x1.push(vecPos[m].x);
                        this.lineInfoArr[k][i].x2.push(vecPos[j].x);
                    }
                }
            }
        }
    }

    getOneAreaPos(iAreaIdx: number, isCircle: boolean): cc.Vec2 {
        // SESrand(time(0));
        let shapeNum = isCircle ? 0 : 1;
        let info: BetAreaLineInfo = this.lineInfoArr[shapeNum][iAreaIdx];
        let randX = this.SERangeRandomf(info.minX, info.maxX);
        let randY;
        let yRange: number[] = [];

        let aArrLen = info.aArr.length;
        for (let i = 0; i < aArrLen; i++) {
            if (randX < info.x1[i] || randX > info.x2[i]) continue;
            let tempF = info.aArr[i] * randX + info.bArr[i];
            if (tempF >= info.minY && tempF <= info.maxY) {
                yRange.push(tempF);
            }
        }

        if (yRange.length !== 2) {
            console.error('-------.存在数据错误');
        }

        if (yRange[0] > yRange[1]) {
            randY = this.SERangeRandomf(yRange[1], yRange[0]);
        } else {
            randY = this.SERangeRandomf(yRange[0], yRange[1]);
        }

        return cc.v2(randX, randY);
    }

    _initBetClock() {
        if (!this._clock_node) {
            let winSize = cc.winSize;
            let _clock_node_pos = this.node.getChildByName('Image_clock').getPosition();

            this._clock_node = cc.instantiate(this.clock_prefab);
            this.node.addChild(this._clock_node);
            this._clock_node.setPosition(_clock_node_pos);
            this._clock_node.active = false;

            this._clock_num_txt = this._clock_node.getChildByName('BitmapFontLabel_index').getComponent(cc.Label);
            this._clock_num_txt.string = '';
            let Sprite_circle = this._clock_node.getChildByName('Sprite_circle');
            this._clock_green = this._clock_node.getChildByName('Sprite_circle_0').getComponent(cc.Sprite);
            this._clock_circle = Sprite_circle.getComponent(cc.ProgressBar);
            this._clock_circle.progress = 0;

            let Image_txt: cc.Sprite = this._clock_node.getChildByName('Image_txt').getComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.videoLanguage_PLIST, Image_txt, 'txt_qxz');
            Image_txt.getComponent(cc.Sprite).spriteFrame = this.videoLanguage_PLIST.getSpriteFrame('txt_qxz');
        }
    }

    showBetClock(isView: boolean) {
        if (!this._clock_node) return;

        this._clock_node.active = isView;

        if (isView) {
            this._clock_node.setScale(0.0);
            this._clock_node.runAction(
                cc.sequence(
                    cc.scaleTo(0.3, 1.0),
                    cc.scaleTo(1.0 / 40, 1.1),
                    cc.scaleTo(1.0 / 40, 1.0),
                    cc.scaleTo(1.0 / 40, 1.05),
                    cc.scaleTo(1.0 / 40, 1.0),
                    cc.callFunc(() => {
                        this._clock_total_time = this._leftTime;
                        if (this._clock_total_time <= 5) {
                            this._clock_canChange = true;
                            this.handleClockChangeColor();
                        }
                        this.schedule(this.updateClockCircle, 0.1); // / 40.0
                    })
                )
            );
        } else {
            this._clock_total_time = 0;
            this._clock_canChange = false;
            this._clock_node.stopAllActions();
        }

        this._clock_circle.progress = 0;
        this.unschedule(this.updateClockCircle);
    }

    updateClockCircle(f32Delta: number) {
        let percent = this._clock_circle.progress + 1.0 / ((this._clock_total_time - 1) * 10); // * 40
        percent = percent >= 1.0 ? 1.0 : percent;
        this._clock_circle.progress = percent;

        if (this._clock_circle.progress >= 1.0) {
            this.unschedule(this.updateClockCircle);
        }
    }

    handleClockChangeColor() {
        if (this._clock_canChange) {
            this._clock_num_txt.font = pf.addressableAssetManager.getAsset(macros.Assets.TIME_XIAZHU); // ("videoCowboy/fnt/time_xiazhu.fnt");
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_videonz_PLIST, this._clock_green, 'red_bg');
            this._clock_green.getComponent(cc.Sprite).spriteFrame = this.game_videonz_PLIST.getSpriteFrame('red_bg');
        } else {
            this._clock_num_txt.font = pf.addressableAssetManager.getAsset(macros.Assets.TIME_XIAZHU_1); // ("videoCowboy/fnt/time_xiazhu_1.fnt");
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_videonz_PLIST, this._clock_green, 'green_bg');
            this._clock_green.getComponent(cc.Sprite).spriteFrame = this.game_videonz_PLIST.getSpriteFrame('green_bg');
        }
    }

    playKaiJu() {
        if (!this._kaiju_Sprite) {
            this._kaiju_Sprite = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.videoLanguage_PLIST, this._kaiju_Sprite, 'xiazhu_ani');
            this._kaiju_Sprite.getComponent(cc.Sprite).spriteFrame =
                this.videoLanguage_PLIST.getSpriteFrame('xiazhu_ani');
            this.node.addChild(this._kaiju_Sprite.node);
            this._kaiju_Sprite.node.setPosition(this._clock_node.getPosition());
            this._kaiju_Sprite.node.active = false;
        }
        this._kaiju_Sprite.node.setScale(0.8);
        this._kaiju_Sprite.node.active = true;
        this._kaiju_Sprite.node.runAction(
            cc.sequence(
                cc.scaleTo(0.3, 1.0),
                cc.scaleTo(1.0 / 40, 1.1),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.scaleTo(1.0 / 40, 1.05),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.delayTime(0.2),
                cc.scaleTo(0.2, 0),
                cc.callFunc(() => {
                    this._kaiju_Sprite.node.active = false;
                    this.showBetClock(true);
                })
            )
        );
    }

    // 停止下注
    playStopXiazhu() {
        if (!this._stopXz_Sprite) {
            this._stopXz_Sprite = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.videoLanguage_PLIST, this._stopXz_Sprite, 'stopXz_ani');
            this._stopXz_Sprite.getComponent(cc.Sprite).spriteFrame =
                this.videoLanguage_PLIST.getSpriteFrame('stopXz_ani');
            this.node.addChild(this._stopXz_Sprite.node);
            this._stopXz_Sprite.node.setPosition(this._clock_node.getPosition());
            this._stopXz_Sprite.node.active = false;
        }
        this.playSoundEffect(this.s_end_bet);
        this._stopXz_Sprite.node.setScale(0.8);
        this._stopXz_Sprite.node.active = true;
        this._stopXz_Sprite.node.runAction(
            cc.sequence(
                cc.scaleTo(0.3, 1.0),
                cc.scaleTo(1.0 / 40, 1.1),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.scaleTo(1.0 / 40, 1.05),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.delayTime(0.2),
                cc.scaleTo(0.2, 0),
                cc.callFunc(() => {
                    this._stopXz_Sprite.node.active = false;
                    this.playKaiPai(null);
                })
            )
        );
    }

    // 开牌
    playKaiPai(sender: string) {
        let result = false;
        if (sender) {
            result = sender === '1';
            this._openCardLayer.showCardNotify(result);
        }

        if (!this._openCard_Sprite) {
            this._openCard_Sprite = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.videoLanguage_PLIST, this._openCard_Sprite, 'kaibai');
            this._openCard_Sprite.getComponent(cc.Sprite).spriteFrame =
                this.videoLanguage_PLIST.getSpriteFrame('kaibai');
            this.node.addChild(this._openCard_Sprite.node);
            this._openCard_Sprite.node.setPosition(this._clock_node.getPosition());
            this._openCard_Sprite.node.active = false;

            this._openCard_blink = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(
            //     this.game_videonz_PLIST,
            //     this._openCard_blink,
            //     'opencard_blink'
            // );
            this._openCard_blink.getComponent(cc.Sprite).spriteFrame =
                this.game_videonz_PLIST.getSpriteFrame('opencard_blink');
            this._openCard_Sprite.node.addChild(this._openCard_blink.node);
            let size = this._openCard_Sprite.node.getContentSize();
            // this._openCard_blink.node.setPosition(cc.v2(size.width * 0.5, size.height * 0.5));
        }

        if (this._openCard_Sprite.node.active) return;
        this._openCard_Sprite.node.active = true;
        this._openCard_Sprite.node.setScale(0.0);
        this._openCard_Sprite.node.runAction(
            cc.sequence(
                cc.scaleTo(0.3, 1.0),
                cc.scaleTo(1.0 / 40, 1.1),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.scaleTo(1.0 / 40, 1.05),
                cc.scaleTo(1.0 / 40, 1.0)
            )
        );
        this._openCard_blink.node.runAction(cc.repeatForever(cc.sequence(cc.fadeOut(0.75), cc.fadeIn(0.75))));
    }

    onRealBackMainScene() {
        // 回到大厅
        // cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);
    }

    playJieSuan() {
        if (!this._jieSuan_Sprite) {
            this._jieSuan_Sprite = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.videoLanguage_PLIST, this._jieSuan_Sprite, 'jiesuan');
            this._jieSuan_Sprite.getComponent(cc.Sprite).spriteFrame =
                this.videoLanguage_PLIST.getSpriteFrame('jiesuan');
            this.node.addChild(this._jieSuan_Sprite.node);
            this._jieSuan_Sprite.node.setPosition(this._clock_node.getPosition());

            this._jieSuan_blink = new cc.Node().addComponent(cc.Sprite);
            // VideoCowboyManager.loadSpriteTextureByPlist(this.game_videonz_PLIST, this._jieSuan_blink, 'jiesuan_blink');
            this._jieSuan_blink.getComponent(cc.Sprite).spriteFrame =
                this.game_videonz_PLIST.getSpriteFrame('jiesuan_blink');
            this._jieSuan_Sprite.node.addChild(this._jieSuan_blink.node);
            let size = this._jieSuan_Sprite.node.getContentSize();
            // this._jieSuan_blink.node.setPosition(cc.v2(size.width * 0.5, size.height * 0.5));
        }
        this._jieSuan_Sprite.node.active = true;
        this._jieSuan_Sprite.node.setScale(0.0);
        this._jieSuan_Sprite.node.runAction(
            cc.sequence(
                cc.scaleTo(0.3, 1.0),
                cc.scaleTo(1.0 / 40, 1.1),
                cc.scaleTo(1.0 / 40, 1.0),
                cc.scaleTo(1.0 / 40, 1.05),
                cc.scaleTo(1.0 / 40, 1.0)
            )
        );
        this._jieSuan_blink.node.runAction(cc.repeatForever(cc.sequence(cc.fadeOut(0.75), cc.fadeIn(0.75))));
    }

    hideKaiJuSprite() {
        if (this._kaiju_Sprite) {
            this._kaiju_Sprite.node.stopAllActions();
            this._kaiju_Sprite.node.active = false;
        }
    }

    hideStopXiazhuSprite() {
        if (this._stopXz_Sprite) {
            this._stopXz_Sprite.node.stopAllActions();
            this._stopXz_Sprite.node.active = false;
        }
    }

    hideKaiPaiSprite() {
        if (this._openCard_Sprite) {
            this._openCard_Sprite.node.stopAllActions();
            this._openCard_Sprite.node.active = false;
        }
    }

    hideJieSuanSprite() {
        if (this._jieSuan_Sprite) {
            this._jieSuan_Sprite.node.stopAllActions();
            this._jieSuan_Sprite.node.active = false;
        }
    }

    playPointAni() {
        let points_num = this._videoCowboyRoom.roundInfo.changePoints;
        if (points_num < 0) return;

        if (!this.points_node) {
            // this.points_node = cc.instantiate(
            //     pf.addressableAssetManager.getAsset(macros.Dynamic_Assets.HEAD_POINTS_ANI)
            // );
            // this.node.addChild(this.points_node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
            // this.points_node.setPosition(
            //     this.node.convertToNodeSpaceAR(this._selfHeadBg.parent.convertToWorldSpaceAR(this._selfHeadBg.position))
            // );
            // this.points_node.getComponent(cc.Animation).on(
            //     'finished',
            //     (event: cc.Event): void => {
            //         this.resetPointAni();
            //     },
            //     this
            // );
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.HEAD_POINTS_ANI).then((asset: cc.Prefab) => {
                this.points_node = cc.instantiate(asset);
                this.node.addChild(this.points_node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
                this.points_node.setPosition(
                    this.node.convertToNodeSpaceAR(
                        this._selfHeadBg.parent.convertToWorldSpaceAR(this._selfHeadBg.position)
                    )
                );
                this.points_node.getComponent(cc.Animation).on(
                    'finished',
                    (event: cc.Event): void => {
                        this.resetPointAni();
                    },
                    this
                );
                this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
            });
        } else this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
    }

    resetPointAni() {
        this._videoCowboyRoom.roundInfo.changePoints = 0;
        if (this.points_node) {
            this.points_node.getComponent(HeadPointsAniControl).resetPointAni();
        }
    }

    showSwitchTable() {
        if (this._bSwitchTable) return;
        this._bSwitchTable = true;
        // cv.TP.showMsg(
        //     pf.languageManager.getString('MiniGames_Switch_content'),
        //     cv.Enum.ButtonStyle.TWO_BUTTON,
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         cv.roomManager.setCurrentRoomID(this._videoCowboyRoom.idle_roomid);
        //         cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
        //         cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        //         cv.roomManager.RequestJoinRoom();
        //     },
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         this.backToCowboyListScene();
        //     }
        // );
        // cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_SWITCH_TABLE);
        cr.commonResourceAgent.commonDialog.showMsg(
            pf.languageManager.getString('MiniGame_Switch_Content'),
            [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
            async () => {
                pf.app.events<AppEvents>().emit('hideWebview');
                const roomId = this._videoCowboyRoom.roundInfo.idleRoomId;
                const cowboyService = pf.serviceManager.get(domain.VideoCowboyService);
                await cowboyService.login();
                await this._videoCowboyRoom.joinRoom(roomId);
            },
            () => {
                pf.app.events<AppEvents>().emit('hideWebview');
                this.backToCowboyListScene();
            }
        );
    }

    private showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this._advanceAutoAddBet) {
            this._advanceAutoAddBet = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_AUTO)
            );
            this.node.addChild(
                this._advanceAutoAddBet,
                COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT
            );
        }
        const miniGameAdvanceAuto = this._advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    onMsgAdvanceAutobetAdd(usedBetCount: number, autoBetCount: number) {
        // console.log('onMsgAdvanceAutobetAdd', msg);
        console.log(`onMsgAdvanceAutobetAdd ${usedBetCount}/${autoBetCount}`);
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }
    private _onMsgConsumingNotify(msg: network.ILeftGameCoinNotify) {
        if (!this._consumingNotify) {
            const notifyObj = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.CONSUMING_PROMPT));
            this.consumingNotifyHolder.addChild(notifyObj);
            this.consumingNotifyHolder.parent.x = this.self_panel.x - this.self_panel.width / 2;
            this._consumingNotify = notifyObj.getComponent(ConsumingPromptControl);
        }
        this._consumingNotify.show(msg.lost_game_coin, msg.cur_game_coin, 2);
    }

    updateGlobalCarousel() {
        const gNode = cc.find('globalCarouselBox');
        if (gNode) {
            if (gNode.getComponent(cc.Widget)) {
                gNode.getComponent(cc.Widget).top = 0;
                gNode.getComponent(cc.Widget).left = 0;
                gNode.getComponent(cc.Widget).right = 0;
                gNode.getComponent(cc.Widget).updateAlignment();
                // 刷新widget
                // cv.resMgr.adaptWidget(gNode, true);
            }
            gNode.active = true;
        }
    }

    tryLeaveRoom() {
        try {
            this._liveVideoComponent?.onLogOut();
            this._videoCowboyRoom.leaveRoom();
        } catch (err) {
            cc.warn(err);
        }
    }

    exitGame() {
        cc.log('[VideoCowboy] exit game');
        this.cleanData();
        this._liveVideoComponent?.onLeaveRoom();
        pf.bundleManager.exitBundle(macros.BUNDLE_NAME);
    }

    private _onPushNotification(notification: PushNoticeData) {
        const curLanguageContent = cr.CommonUtil.selectServerString(notification.content);
        if (curLanguageContent.length <= 0) {
            return;
        }
        if (
            notification.msgType.includes(PushNoticeType.PUSH_WORLD) ||
            notification.msgType.includes(PushNoticeType.PUSH_VIDEOCOWBOY)
        ) {
            this.OnCowboyRewardTips(curLanguageContent);
        }
    }

    /**
     * 按指定动画的持续时间计算该动画播放速度(应用于所有动画剪辑)
     * @param anim
     * @param executeTime
     */
    private _getAnimClipSpeedByDuring(animClip: cc.AnimationClip, executeTime: number): number {
        let speed: number = animClip.speed;
        if (executeTime > 0) {
            let totalFrames: number = animClip.sample * animClip.duration;
            speed = totalFrames / cc.game.getFrameRate() / executeTime;
        }
        return speed;
    }
}
