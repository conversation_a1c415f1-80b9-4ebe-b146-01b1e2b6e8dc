/* eslint-disable no-param-reassign */
/* eslint-disable no-fallthrough */
/* eslint-disable @typescript-eslint/prefer-for-of */
/* eslint-disable complexity */
/* eslint-disable no-empty */
/* eslint-disable no-lone-blocks */
/* eslint-disable camelcase */
/* eslint-disable max-classes-per-file */
import * as pf from 'pf';
import * as common from 'common';
import * as gs_protocol from '../../network/pkw/pb/gs_protocol';
import game_pb = gs_protocol.protocol;
import { CardControl } from '../CardControl';
import HashMapControl = common.components.HashMapControl;
import { TexasCommonDef } from '../../common/texas-define';
import { PokerData } from 'common';
// TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
// import { AwardPlayerJackpotType } from '../../../../jackpot/scripts/domain/jackpot-data';
import type {
    HandCardType,
    ReplayData,
    ReplayFlopData
} from '../../../../../poker-framework/scripts/services/data/data-service-index';
import { AvatarControl } from '../../../../common/scripts/components/AvatarControl';
import { ResourceUtil } from '../../../../common/scripts/utils/resource-util';
import { CashGameFeatureCreator } from '../feature/CashGameFeature';
import type { SquidGameData } from '../squid/SquidGameData';

export enum GameReviewBettingRoundType {
    /**
     * 默认
     */
    Enum_BettingRound_None = 0,

    /**
     * 翻牌前
     */
    Enum_BettingRound_Preflop,

    /**
     * 翻牌
     */
    Enum_BettingRound_Flop,

    /**
     * turn 牌
     */
    Enum_BettingRound_Turn,

    /**
     * river 牌
     */
    Enum_BettingRound_River,

    /**
     * 河底
     */
    Enum_BettingRound_ShowDown
}

export class GameReviewItemData {
    // 从"PokerHandData"结构中抽取
    nGameID: number = 0; // 游戏id
    sGameUUID: string = ''; // 牌局uuid
    nGameMode: number = 0; // 牌局模式
    objReplay: Object = null; // 牌局回放数据串对象
    nShortFull: number = 0; // 0: 花 > 葫芦, 1:葫芦 > 花
    vPubsCards: HandCardType[] = []; // 公共牌组
    vUnsendPublicCards: HandCardType[] = []; // 未发出的公共牌堆

    // 从"PlayerRecord"结构中抽取
    nPlayerID: number = 0; // id
    sPlayerName: string = ''; // 昵称
    sPlayerHead: string = ''; // 头像
    nWinBet: number = 0; // 输赢的筹码数
    nInsuranceBet: number = 0; // 投保额
    nInsuranceAmount: number = 0; // 赔付额
    nJackWinbet: number = 0; // 一手牌赢的jackpot筹码数
    nPlayerBettingRoundBet: number = 0; // 本局下注的所有筹码总数
    bMuck: boolean = false; // 是否自动埋牌
    bActiveShow: boolean = false; // 主动show
    bForceShowDown: boolean = false; // 是否强制show
    nLastRoundType: number = 0; // 玩家坚持到哪一阶段(cv.Enum.BettingRoundType)
    vHandCards: HandCardType[] = []; // 手牌数组
    plat: number = 0; //
    seatNo: number = -1; //
    seatInfo: number = 0; // 000=default, 001=D, 010=SB, 100=BB
    bFold: boolean = false; // 是否弃牌
    nReviewSendOutLen: number = 0; // 发发看的长度(即该玩家额外能看到的长度)
    nReviewSendOutActLen: number = 0; // 牌局回顾"发发看"动画长度
    nForceShowedActLen: number = 0; // 被强制亮牌的长度(需要显示翻牌动画)
    jackpotType: number = 0;
    squidCount: number = 0;
    squidWinLoseAmount: number = 0;
    superSquidCount: number = 0;
    seatStatus: number = 0; // 0 playing, 1 waiting, 2 Away
    currentHandWinnerNoOfNormalSquids: number = 0;
    squidValue: number = 0;
    squidMultiplier: number = 0;
}

/**
 * 下注信息
 */
class GameReviewItemBetInfo {
    action: string = ''; // 动作名
    amount: number = 0; // 金额
}

/**
 * 牌局回顾面板子项
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class GameReviewItemComp extends cc.Component {
    private _authService: pf.services.AuthService = null;
    @property(cc.Node) panel_card: cc.Node = null; // 牌面板
    @property(cc.Node) panel_bottom: cc.Node = null; // 底栏面板

    @property(cc.Sprite) img_bg: cc.Sprite = null; // background image
    @property(cc.Sprite) img_head: cc.Sprite = null; // 头像
    @property(cc.Sprite) img_head_sheild: cc.Sprite = null; // 头像遮罩
    @property(cc.Sprite) img_d: cc.Sprite = null; // D
    @property(cc.Sprite) img_pei: cc.Sprite = null; // 赔
    @property(cc.Sprite) img_bao: cc.Sprite = null; // 保
    @property(cc.Sprite) img_jp: cc.Sprite = null; // JP
    @property(cc.Sprite) img_cardtype: cc.Sprite = null; // 牌局型
    @property(cc.Sprite) img_pub_card_box_1: cc.Sprite = null; // 1张公牌牌框
    @property(cc.Sprite) img_pub_card_box_3: cc.Sprite = null; // 3张公牌牌框
    @property(cc.Sprite) img_pub_card_box_4: cc.Sprite = null; // 4张公牌牌框

    @property(cc.Label) txt_username: cc.Label = null; // 玩家昵称
    @property(cc.Label) txt_number: cc.Label = null; // 输赢数量
    @property(cc.Label) txt_bet_preflop: cc.Label = null; // 翻前下注
    @property(cc.Label) txt_bet_flop: cc.Label = null; // 翻牌下注
    @property(cc.Label) txt_bet_turn: cc.Label = null; // 转牌下注
    @property(cc.Label) txt_bet_river: cc.Label = null; // 河牌下注
    @property(cc.Label) txt_bao: cc.Label = null; // 保
    @property(cc.Label) txt_pei: cc.Label = null; // 赔
    @property(cc.Label) txt_jp: cc.Label = null; // JP

    // Falling mars
    @property(cc.Label) txt_player_type: cc.Label = null;
    @property(cc.Label) txt_falling_mars_username: cc.Label = null; // 玩家昵称
    @property(cc.Sprite) img_jackpot_mars: cc.Sprite = null;
    @property([cc.SpriteFrame]) jackpot_mars_sf: cc.SpriteFrame[] = [];
    @property(cc.Node) falling_mars_holder: cc.Node = null;
    @property(cc.Label) squid_counter: cc.Label = null; // Squid counter
    @property(cc.Label) squid_loss_or_gain: cc.Label = null;
    @property(cc.Node) super_squid_icon: cc.Node = null;
    @property(cc.Node) Squid_Temp_Away: cc.Node = null;
    @property(cc.SpriteFrame) squid_counter_frame: cc.SpriteFrame[] = [];
    @property(AvatarControl) selfAvatar: AvatarControl = null;

    private _vHandCard: CardControl[] = []; // 手牌
    private _vPubsCard: CardControl[] = []; // 公牌
    private _handCardScaleRatio: number = 1; // 手牌初始缩放比例
    private _pubsCardScaleRatio: number = 1; // 公牌初始缩放比例

    private _jackpotTxtFontSize: number = 0; // 底栏面板"JP"字体初始大小
    private _jackpotImgScaleRatio: number = 0; // 底栏面板"JP"图标初始缩放比例

    private _insuranceTxtFontSize: number = 0; // 底栏面板"投保/赔付"字体初始大小
    private _insuranceImgScaleRatio: number = 0; // 底栏面板"投保/赔付"图标初始缩放比例

    protected onLoad(): void {
        this._authService = pf.serviceManager.get(pf.services.AuthService);
        common.UIUtil.adaptWidget(this.node, true);

        // 初始化相关控件
        for (let i = 0; i < this.panel_card.children.length; ++i) {
            let hand_card: cc.Node = this.panel_card.getChildByName(`hand_card_${i}`);
            if (hand_card) {
                this._handCardScaleRatio = hand_card.scale;
                this._vHandCard.push(hand_card.getComponent(CardControl));
            }

            let public_card: cc.Node = this.panel_card.getChildByName(`public_card_${i}`);
            if (public_card) {
                this._pubsCardScaleRatio = public_card.scale;
                this._vPubsCard.push(public_card.getComponent(CardControl));
            }
        }

        this._insuranceTxtFontSize = this.txt_bao.fontSize;
        this._insuranceImgScaleRatio = this.img_bao.node.scale;

        this._jackpotTxtFontSize = this.txt_jp.fontSize;
        this._jackpotImgScaleRatio = this.img_jp.node.scale;

        // 重置相关控件
        this._resetCtl();
    }

    protected start(): void {}

    /**
     * 重置相关控件
     */
    private _resetCtl(): void {
        this.img_d.node.active = false;
        this.img_cardtype.node.active = false;
        this.img_head_sheild.node.active = false;
        this.img_pub_card_box_1.node.active = false;
        this.img_pub_card_box_3.node.active = false;
        this.img_pub_card_box_4.node.active = false;

        this.txt_bet_preflop.node.active = false;
        this.txt_bet_flop.node.active = false;
        this.txt_bet_turn.node.active = false;
        this.txt_bet_river.node.active = false;
        if (this.Squid_Temp_Away) this.Squid_Temp_Away.active = false;

        this.txt_username.string = '';
    }

    setSquidInfoActive({
        squidWinLoseAmount,
        squidCount,
        superSquidCount,
        seatStatus,
        currentHandWinnerNoOfNormalSquids,
        vPubsCards,
        squidMultiplier
    }: GameReviewItemData) {
        if (
            !this.squid_counter ||
            !this.squid_loss_or_gain ||
            (squidCount === undefined && squidWinLoseAmount === undefined)
        )
            return;
        if (squidCount) {
            this.squid_counter.node.parent.parent.active = true;
            this.squid_counter.node.parent.active = true;
            if (this.super_squid_icon && (currentHandWinnerNoOfNormalSquids === 2 || superSquidCount > 0)) {
                this.super_squid_icon.active = true;
            }
            if (squidCount > 0) this.squid_counter.string = squidCount.toString();
            else this.squid_counter.node.parent.active = false;
        }
        if (squidWinLoseAmount) {
            this.squid_loss_or_gain.node.active = true;
            this.squid_loss_or_gain.node.color = common.CurrencyUtil.getSignColor(squidWinLoseAmount);
            this.squid_loss_or_gain.string =
                pf.languageManager.getString('Filter_Mode_Squid_Text') +
                common.CurrencyUtil.getSignedString(common.CurrencyUtil.convertToClientAmount(squidWinLoseAmount));
        }

        const featureData = CashGameFeatureCreator.getFeatureData(pf.client.GameId.Squid) as SquidGameData;
        squidMultiplier = squidMultiplier || featureData?.squidHuntGameParams?.squidMultiplier[squidCount]?.multiplier;
        this.squid_counter.node.parent.getComponent(cc.Sprite).spriteFrame =
            this.squid_counter_frame[squidMultiplier > 1 ? 1 : 0];
        if (seatStatus !== game_pb.HandRecord.SeatStatus.Playing && this.Squid_Temp_Away)
            this.Squid_Temp_Away.active = true;
        if (!this.Squid_Temp_Away.active || !seatStatus) return;

        this.txt_number.node.active = false; // AT- 7808 :Don't show anything in amount (even the amount is 0) for "leave" and "waiting" user in hand review.

        const lbl = this.Squid_Temp_Away.getChildByName('txt')?.getComponent(cc.Label);
        if (
            seatStatus === game_pb.HandRecord.SeatStatus.IsAwayHasCard ||
            seatStatus === game_pb.HandRecord.SeatStatus.IsAwayHasNoCard
        ) {
            if (lbl) lbl.string = pf.languageManager.getString('Away');
            this.panel_card.active = seatStatus === game_pb.HandRecord.SeatStatus.IsAwayHasCard;
        } else if (
            seatStatus === game_pb.HandRecord.SeatStatus.IsWaitingHasCard ||
            seatStatus === game_pb.HandRecord.SeatStatus.IsWaitingNoCard
        ) {
            if (lbl) lbl.string = pf.languageManager.getString('GameUiWaiting');
            this.panel_card.active = seatStatus === game_pb.HandRecord.SeatStatus.IsWaitingHasCard;
        }
    }

    /**
     * 设置"输赢"金额
     * @param nAmount
     */
    private _setWinAmount(nAmount: number): void {
        let nWinBet: number = common.CurrencyUtil.convertToClientAmount(nAmount);
        nWinBet = common.CommonUtil.numberToShowNumber(
            pf.MathUtil.bigNumberToFixed(nWinBet, 2, pf.MathUtil.RoundingMode.ROUND_HALF_UP)
        );

        this.txt_number.string = common.CurrencyUtil.getSignedString(nWinBet);
        this.txt_number.node.color = common.CurrencyUtil.getSignColor(nWinBet);
        this.txt_number.node.active = true;
    }

    /**
     * 设置保险"赔付/投保"金额
     * @param nPeiFuAmount    - 赔付额
     * @param nTouBaoAmount   - 投保额
     */
    private _setInsuredAmount(nPeiFuAmount: number, nTouBaoAmount: number): void {
        // 赔付
        do {
            let peiAmount: number = common.CurrencyUtil.convertToClientAmount(nPeiFuAmount);
            peiAmount = common.CommonUtil.numberToShowNumber(
                pf.MathUtil.bigNumberToFixed(peiAmount, 2, pf.MathUtil.RoundingMode.ROUND_DOWN)
            );
            let peiStr = '0';
            let peiColor: cc.Color = cc.Color.WHITE;

            if (peiAmount > 0) {
                peiStr = '+' + pf.TypeUtil.toSafeString(peiAmount);
                peiColor = common.CommonUtil.getWinColor();
            } else if (peiAmount < 0) {
                peiStr = pf.TypeUtil.toSafeString(peiAmount);
                peiColor = common.CommonUtil.getLoseColor();
            }

            this.txt_pei.string = pf.StringUtil.formatC(pf.languageManager.getString('UIPeiFuE'), peiStr);
            this.txt_pei.node.color = peiColor;
            this.txt_pei.node.active = peiAmount !== 0;
            this.img_pei.node.active = this.txt_pei.node.active;
            if (this.img_pei.node.active) {
                let fileName = 'ui/gameMain/game_icon_pei';
                ResourceUtil.setSpriteFrame(
                    this.img_pei.node,
                    ResourceUtil.getLanguagePath(fileName, pf.languageManager.currentLanguage)
                );
            }
        } while (false);

        // 投保
        do {
            let baoAmount: number = common.CurrencyUtil.convertToClientAmount(nTouBaoAmount);
            baoAmount = common.CommonUtil.numberToShowNumber(
                pf.MathUtil.bigNumberToFixed(baoAmount, 2, pf.MathUtil.RoundingMode.ROUND_DOWN)
            );
            let baoStr = '0';
            let baoColor: cc.Color = cc.Color.WHITE;
            if (baoAmount > 0) {
                baoStr = '-' + pf.TypeUtil.toSafeString(baoAmount);
                baoColor = common.CommonUtil.getLoseColor();
            } else if (baoAmount < 0) {
                baoAmount = Math.abs(baoAmount);
                baoStr = '+' + pf.TypeUtil.toSafeString(baoAmount);
                baoColor = common.CommonUtil.getWinColor();
            }

            this.txt_bao.string = pf.StringUtil.formatC(pf.languageManager.getString('UITouBaoE'), baoStr);
            this.txt_bao.node.color = baoColor;
            this.txt_bao.node.active = baoAmount !== 0;
            this.img_bao.node.active = this.txt_bao.node.active;
            if (this.img_bao.node.active) {
                let fileName = 'ui/gameMain/game_icon_bao';
                ResourceUtil.setSpriteFrame(
                    this.img_bao.node,
                    ResourceUtil.getLanguagePath(fileName, pf.languageManager.currentLanguage)
                );
            }
        } while (false);
    }

    /**
     * 设置"JP"输赢
     * @param nAmount
     */
    private _setJackPotAmout(nAmount: number): void {
        if (nAmount && nAmount > 0) {
            this.img_jp.node.active = true;
            this.txt_jp.node.active = true;
            this.txt_jp.string = ': ' + common.CurrencyUtil.clientAmountToDisplayString(nAmount);
        } else {
            this.img_jp.node.active = false;
            this.txt_jp.node.active = false;
            this.txt_jp.string = '';
        }
    }

    /**
     * 排版底栏面板
     */
    private _layoutPanelBottom(): void {
        this.panel_bottom.active = this.txt_bao.node.active || this.txt_pei.node.active || this.txt_jp.node.active;

        // 复原控件属性
        this.txt_jp.fontSize = this._jackpotTxtFontSize;
        this.img_jp.node.setScale(this._jackpotImgScaleRatio);

        this.txt_bao.fontSize = this._insuranceTxtFontSize;
        this.img_bao.node.setScale(this._insuranceImgScaleRatio);

        this.txt_pei.fontSize = this._insuranceTxtFontSize;
        this.img_pei.node.setScale(this._insuranceImgScaleRatio);

        // "投保/赔付"等左对齐, "JP"右对齐
        let lx = -this.panel_bottom.width * this.panel_bottom.anchorX; // 左对齐坐标计数
        let rx = this.panel_bottom.width * (1 - this.panel_bottom.anchorX); // 右对齐坐标计数
        let y = 0; // 公用纵坐标计数
        let attenuate_font = 1; // 适配时衰减的字体大小
        let attenuate_scale = 0.05; // 适配时衰减的图标缩放比例

        let tmp_lx = 0; // 临时左对齐坐标计数
        let tmp_rx = 0; // 临时右对齐坐标计数
        let tmp_total_w = 0; // 临时排版控件总占宽
        let tmp_jackpit_fontSize: number = this._jackpotTxtFontSize + attenuate_font; // 临时"JP"字体大小
        let tmp_jackpot_imgScale: number = this._jackpotImgScaleRatio + attenuate_scale; // 临时"JP"图标缩放
        let tmp_insurance_fontSize: number = this._insuranceTxtFontSize + attenuate_font; // 临时"投保/赔付"字体大小
        let tmp_insurance_imgScale: number = this._insuranceImgScaleRatio + attenuate_scale; // 临时"投保/赔付"图标缩放

        let offset_left_nextto = 10; // 左对齐绑定间距
        let offset_right_nextto = -15; // 右对齐绑定间距
        let offset_middle = 50; // 公用毗邻间距

        do {
            tmp_lx = lx;
            tmp_rx = rx;
            tmp_total_w = 0;

            tmp_jackpit_fontSize -= attenuate_font;
            tmp_jackpot_imgScale -= attenuate_scale;

            tmp_insurance_fontSize -= attenuate_font;
            tmp_insurance_imgScale -= attenuate_scale;

            // 投保
            if (this.txt_bao.node.active) {
                this.txt_bao.fontSize = tmp_insurance_fontSize;
                this.img_bao.node.setScale(tmp_insurance_imgScale);
                let txt_bao_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_bao).width;

                tmp_lx += this.img_bao.node.width * this.img_bao.node.scaleX * this.img_bao.node.anchorX;
                this.img_bao.node.setPosition(tmp_lx, y);
                tmp_lx += this.img_bao.node.width * this.img_bao.node.scaleX * (1 - this.img_bao.node.anchorX);
                tmp_lx += offset_left_nextto;

                tmp_lx += txt_bao_w * this.txt_bao.node.anchorX;
                this.txt_bao.node.setPosition(tmp_lx, y);
                tmp_lx += txt_bao_w * (1 - this.txt_bao.node.anchorX);
            }

            // 赔付
            if (this.txt_pei.node.active) {
                if (this.txt_bao.node.active) tmp_lx += offset_middle;

                this.txt_pei.fontSize = tmp_insurance_fontSize;
                this.img_pei.node.setScale(tmp_insurance_imgScale);
                let txt_pei_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_pei).width;

                tmp_lx += this.img_pei.node.width * this.img_pei.node.scaleX * this.img_pei.node.anchorX;
                this.img_pei.node.setPosition(tmp_lx, y);
                tmp_lx += this.img_pei.node.width * this.img_pei.node.scaleX * (1 - this.img_pei.node.anchorX);
                tmp_lx += offset_left_nextto;

                tmp_lx += txt_pei_w * this.txt_pei.node.anchorX;
                this.txt_pei.node.setPosition(tmp_lx, y);
                tmp_lx += txt_pei_w * (1 - this.txt_pei.node.anchorX);
            }

            // JP
            if (this.txt_jp.node.active) {
                this.txt_jp.fontSize = tmp_jackpit_fontSize;
                this.img_jp.node.setScale(tmp_jackpot_imgScale);
                let txt_jp_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_jp).width;

                tmp_rx -= txt_jp_w * (1 - this.txt_jp.node.anchorX);
                this.txt_jp.node.setPosition(tmp_rx, y);
                tmp_rx -= txt_jp_w * this.txt_jp.node.anchorX;
                tmp_rx -= offset_right_nextto;
                tmp_rx -= this.img_jp.node.width * this.img_jp.node.scaleX * (1 - this.img_jp.node.anchorX);
                this.img_jp.node.setPosition(tmp_rx, y);
                tmp_rx -= this.img_jp.node.width * this.img_jp.node.scaleX * this.img_jp.node.anchorX;

                if (this.txt_bao.node.active || this.txt_pei.node.active) tmp_rx -= offset_middle;
            }

            tmp_total_w += Math.abs(Math.abs(lx) - Math.abs(tmp_lx));
            tmp_total_w += Math.abs(Math.abs(rx) - Math.abs(tmp_rx));
        } while (tmp_total_w > this.panel_bottom.width);
    }

    /**
     * 设置下注信息
     * @param data
     */
    private _setBetRoundsInfo(data: GameReviewItemData): void {
        if (!data.objReplay) return;

        let seatNo: number = data.seatNo; // 座位ID
        let initialStakeMap: HashMapControl<number, number> = new HashMapControl(); // 每个座位的筹码初始值
        const objReplay = data.objReplay as ReplayData;
        let roomInfo = objReplay.roomInfo;
        let tableInfo = objReplay.tableInfo;
        let seatsInfo = objReplay.seatsInfo;
        let roundsInfo = objReplay.roundsInfo;

        // 填充"每个座位的筹码初始值"
        initialStakeMap.clear();
        for (let i = 0; i < pf.DataUtil.getArrayLength(seatsInfo.seatsInfo); ++i) {
            let info = seatsInfo.seatsInfo[i];
            let seatid: number = pf.TypeUtil.toSafeNumber(info.seatNo);
            let stake: number = pf.TypeUtil.toSafeNumber(info.stake);
            initialStakeMap.add(seatid, stake);
        }

        // 解析前注
        let analysisAnte: (betInfo: GameReviewItemBetInfo) => void = (betInfo: GameReviewItemBetInfo) => {
            let dealerSeatID: number = pf.TypeUtil.toSafeNumber(tableInfo.dealerSeat);
            let mode: number = pf.TypeUtil.toSafeNumber(roomInfo.mode);
            let ante: number = pf.TypeUtil.toSafeNumber(roomInfo.ante);
            let isAnteRound: boolean = roundsInfo.anteRound;
            let isDoubleAnte = Boolean(roomInfo.doubleAnte);
            if (isAnteRound) {
                // 短牌检测庄家是否双倍"ante"(此处再处理一个前注)
                if (mode === pf.client.CreateGameMode.CreateGame_Mode_Short) {
                    if (isDoubleAnte) {
                        if (seatNo === dealerSeatID) {
                            betInfo.action = 'Ante';
                            betInfo.amount += ante;
                        }
                    }

                    // 短牌判断下补"Ante"
                    for (let i = 0; i < pf.DataUtil.getArrayLength(tableInfo.postSeats); ++i) {
                        if (seatNo === tableInfo.postSeats[i]) {
                            betInfo.action = pf.languageManager.getString('ActionTips9');
                            betInfo.amount += ante;
                            break;
                        }
                    }
                }
            }
        };

        // 解析盲注
        let analysisBlind: (betInfo: GameReviewItemBetInfo) => void = (betInfo: GameReviewItemBetInfo) => {
            let sbLevel = 0;
            let bbLevel: number = pf.TypeUtil.toSafeNumber(roomInfo['blind']);
            let straddleLevel: number = 2 * bbLevel;

            let sbSeatID: number = pf.TypeUtil.toSafeNumber(tableInfo.sbSeat);
            let bbSeatID: number = pf.TypeUtil.toSafeNumber(tableInfo.bbSeat);
            let straddleSeatID = -1;

            // 解析小盲配置
            // for (let i = 0; i < cv.config.getblindArrLen(); ++i) {
            for (let i = 0; i < common.TexasMathUtil.getBlindArr(false).length; ++i) {
                let strBlind: string = common.TexasMathUtil.getblindString(i);
                if (strBlind) {
                    let vBlind = strBlind.split('/');
                    let nSB = Math.min(pf.TypeUtil.toSafeNumber(vBlind[0]), pf.TypeUtil.toSafeNumber(vBlind[1]));
                    let nBB = Math.max(pf.TypeUtil.toSafeNumber(vBlind[0]), pf.TypeUtil.toSafeNumber(vBlind[1]));

                    if (common.CurrencyUtil.convertToServerAmount(nBB) === bbLevel) {
                        sbLevel = common.CurrencyUtil.convertToServerAmount(nSB);
                    }
                }
            }

            // 解析"straddle"
            if (tableInfo.straddleSeat !== null && typeof tableInfo.straddleSeat !== 'undefined') {
                straddleSeatID = pf.TypeUtil.toSafeNumber(tableInfo.straddleSeat);
            }

            let isBlindRound: boolean = roundsInfo.blindRound;
            if (isBlindRound) {
                if (seatNo === sbSeatID) {
                    betInfo.amount += sbLevel;
                    betInfo.action = 'SB';
                } else if (seatNo === bbSeatID) {
                    betInfo.amount += bbLevel;
                    betInfo.action = 'BB';
                } else if (straddleSeatID >= 0 && seatNo === straddleSeatID) {
                    betInfo.amount += straddleLevel;
                    betInfo.action = pf.languageManager.getString('ActionTips5');
                }

                // 补盲注
                for (let i = 0; i < pf.DataUtil.getArrayLength(tableInfo.postSeats); ++i) {
                    if (seatNo === tableInfo.postSeats[i]) {
                        if (initialStakeMap.length > 3 && straddleSeatID >= 0) {
                            betInfo.amount += straddleLevel;
                            betInfo.action = pf.languageManager.getString('ActionTips8');
                        } else {
                            betInfo.amount += bbLevel;
                            betInfo.action = pf.languageManager.getString('ActionTips8');
                        }
                    }
                }
            }
        };

        // 解析下注圈
        let analysisRoundBet: (info: ReplayFlopData[], betInfo: GameReviewItemBetInfo) => void = (
            info: ReplayFlopData[],
            betInfo: GameReviewItemBetInfo
        ): void => {
            let finalActionType: number = TexasCommonDef.ActionType.Enum_Action_Null;
            for (let i = 0; i < pf.DataUtil.getArrayLength(info); ++i) {
                let seatID: number = pf.TypeUtil.toSafeNumber(info[i].seatNo);
                let amount: number = pf.TypeUtil.toSafeNumber(info[i].amount);
                let actionType: number = pf.TypeUtil.toSafeNumber(info[i].actionType);

                // 找到自己座位
                if (seatID === seatNo) {
                    // 每轮筹码计数
                    switch (actionType) {
                        case TexasCommonDef.ActionType.Enum_Action_Call:
                            {
                                betInfo.amount += amount;
                            }
                            break;

                        case TexasCommonDef.ActionType.Enum_Action_Bet:
                            {
                                betInfo.amount = amount;
                            }
                            break;

                        case TexasCommonDef.ActionType.Enum_Action_Raise:
                            {
                                betInfo.amount = amount;
                            }
                            break;

                        case TexasCommonDef.ActionType.Enum_Action_Allin:
                            {
                                betInfo.amount = amount;
                            }
                            break;
                    }

                    // 保存该轮最后一个动作
                    finalActionType = actionType;
                }
            }

            switch (finalActionType) {
                case TexasCommonDef.ActionType.Enum_Action_Null:
                    break; // 默认
                case TexasCommonDef.ActionType.Enum_Action_Check:
                    betInfo.action = pf.languageManager.getString('ActionTips6');
                    break; // 看牌
                case TexasCommonDef.ActionType.Enum_Action_Fold:
                    betInfo.action = pf.languageManager.getString('ActionTips1');
                    break; // 弃牌                    case TexasCommonDef.ActionType.Enum_Action_Straddle: betInfo.action = pf.languageManager.getString("ActionTips5"); break;   // straddle
                case TexasCommonDef.ActionType.Enum_Action_Straddle:
                    betInfo.action = pf.languageManager.getString('ActionTips5');
                    break; // straddle
                case TexasCommonDef.ActionType.Enum_Action_Post:
                    betInfo.action = pf.languageManager.getString('ActionTips8');
                    break; // 补盲
                case TexasCommonDef.ActionType.Enum_Action_Call:
                    betInfo.action = pf.languageManager.getString('ActionTips0');
                    break; // 跟注
                case TexasCommonDef.ActionType.Enum_Action_Bet:
                    betInfo.action = pf.languageManager.getString('ActionTips3');
                    break; // 下注
                case TexasCommonDef.ActionType.Enum_Action_Raise:
                    betInfo.action = pf.languageManager.getString('ActionTips2');
                    break; // 加注
                case TexasCommonDef.ActionType.Enum_Action_Allin:
                    betInfo.action = pf.languageManager.getString('ActionTips4');
                    break; // Allin
                default:
                    break;
            }
        };

        // 剩余筹码
        let stakes: number = pf.TypeUtil.toSafeNumber(initialStakeMap.get(data.seatNo));

        // 前注
        let bet_profix: GameReviewItemBetInfo = new GameReviewItemBetInfo();
        analysisAnte(bet_profix);

        // 翻牌前(包含盲注)
        let bet_preflop: GameReviewItemBetInfo = new GameReviewItemBetInfo();
        bet_preflop.action = bet_profix.action;
        analysisBlind(bet_preflop);
        analysisRoundBet(roundsInfo.preflop, bet_preflop);
        bet_preflop.amount += bet_profix.amount;
        bet_preflop.amount = Math.min(stakes, bet_preflop.amount);
        stakes -= bet_preflop.amount;
        stakes = Math.max(stakes, 0);

        // 翻牌圈
        let bet_flop: GameReviewItemBetInfo = new GameReviewItemBetInfo();
        analysisRoundBet(roundsInfo.flop, bet_flop);
        bet_flop.amount = Math.min(stakes, bet_flop.amount);
        stakes -= bet_flop.amount;
        stakes = Math.max(stakes, 0);

        // 转牌
        let bet_turn: GameReviewItemBetInfo = new GameReviewItemBetInfo();
        analysisRoundBet(roundsInfo.turn, bet_turn);
        bet_turn.amount = Math.min(stakes, bet_turn.amount);
        stakes -= bet_turn.amount;
        stakes = Math.max(stakes, 0);

        // 河牌
        let bet_river: GameReviewItemBetInfo = new GameReviewItemBetInfo();
        analysisRoundBet(roundsInfo.river, bet_river);
        bet_river.amount = Math.min(stakes, bet_river.amount);
        stakes -= bet_river.amount;
        stakes = Math.max(stakes, 0);
        let haveAllinBefore = this._haveDoActionBefore('Allin', data.nLastRoundType, [
            bet_preflop.action,
            bet_flop.action,
            bet_turn.action
        ]);

        // 当前打到那个阶段(引擎自带的"cc.Label.Overflow.SHRINK"在某些情况右侧数字0边被裁剪一部分, 采用手动计算)
        switch (data.nLastRoundType) {
            case GameReviewBettingRoundType.Enum_BettingRound_ShowDown:
            case GameReviewBettingRoundType.Enum_BettingRound_River: {
                this.txt_bet_river.node.active = true;
                if (bet_river.amount === 0) {
                    if (!(bet_river.action === pf.languageManager.getString('ActionTips6')) && haveAllinBefore) {
                        this.txt_bet_river.string = '';
                    } else {
                        this.txt_bet_river.string = bet_river.action;
                    }
                } else {
                    let strAmount: string = common.CurrencyUtil.clientAmountToDisplayString(bet_river.amount);
                    let strText = `${bet_river.action}  ${strAmount}`;
                    this.txt_bet_river.string = strText;
                }
            }
            case GameReviewBettingRoundType.Enum_BettingRound_Turn: {
                this.txt_bet_turn.node.active = true;
                if (bet_turn.amount === 0) {
                    if (!(bet_turn.action === pf.languageManager.getString('ActionTips6')) && haveAllinBefore) {
                        this.txt_bet_turn.string = '';
                    } else {
                        this.txt_bet_turn.string = bet_turn.action;
                    }
                } else {
                    let strAmount: string = common.CurrencyUtil.clientAmountToDisplayString(bet_turn.amount);
                    let strText = `${bet_turn.action}  ${strAmount}`;
                    this.txt_bet_turn.string = strText;
                }
            }
            case GameReviewBettingRoundType.Enum_BettingRound_Flop: {
                this.txt_bet_flop.node.active = true;
                if (bet_flop.amount === 0) {
                    if (!(bet_flop.action === pf.languageManager.getString('ActionTips6')) && haveAllinBefore) {
                        this.txt_bet_flop.string = '';
                    } else {
                        this.txt_bet_flop.string = bet_flop.action;
                    }
                } else {
                    let strAmount: string = common.CurrencyUtil.clientAmountToDisplayString(bet_flop.amount);
                    let strText = `${bet_flop.action}  ${strAmount}`;
                    this.txt_bet_flop.string = strText;
                }
            }
            case GameReviewBettingRoundType.Enum_BettingRound_Preflop:
                {
                    this.txt_bet_preflop.node.active = true;
                    if (bet_preflop.amount === 0) {
                        this.txt_bet_preflop.string = `${bet_preflop.action}`;
                    } else {
                        let strAmount: string = common.CurrencyUtil.clientAmountToDisplayString(bet_preflop.amount);
                        let strText = `${bet_preflop.action}  ${strAmount}`;
                        this.txt_bet_preflop.string = strText;
                    }
                }
                break;

            case GameReviewBettingRoundType.Enum_BettingRound_None:
                break;
            default:
                break;
        }
    }

    private _haveDoActionBefore(
        action: string,
        currentRound: GameReviewBettingRoundType,
        betActions: string[]
    ): boolean {
        for (let i = 0; i < betActions.length && i < currentRound - 1; ++i) {
            if (betActions[i] === action) return true;
        }
        return false;
    }

    /**
     * 计算牌型
     * @param data
     */
    private _calculateCardType(data: GameReviewItemData): void {
        let cardType = '';
        let hasCardType = false;

        let hpokers: number[] = [];
        let ppokers: number[] = [];

        let out_hps: number[] = [];
        let out_pps: number[] = [];

        // 手牌
        for (let i = 0; i < this._vHandCard.length; ++i) {
            let card: CardControl = this._vHandCard[i];
            if (card.node.active && card.isFace() && card.hasContent()) {
                let nValue = PokerData.getLocalValue(card.eCardNum, data.nGameMode);
                let poker: PokerData = new PokerData(data.nGameMode);
                poker.initWhitValue(nValue, card.eCardSuit, data.nGameMode);
                hpokers.push(poker.getNumber(data.nGameMode));
            }
        }

        // 公牌
        for (let i = 0; i < this._vPubsCard.length; ++i) {
            let card: CardControl = this._vPubsCard[i];
            if (card.node.active && card.isFace() && card.hasContent()) {
                let nValue = PokerData.getLocalValue(card.eCardNum, data.nGameMode);
                let poker: PokerData = new PokerData(data.nGameMode);
                poker.initWhitValue(nValue, card.eCardSuit, data.nGameMode);
                ppokers.push(poker.getNumber(data.nGameMode));
            }
        }

        // "奥马哈"
        let tmpCardType = '';
        if (data.nGameID === pf.client.GameId.PLO) {
            tmpCardType = PokerData.getPokerTypeString(hpokers, ppokers, data.nGameMode, 2, 3, out_hps, out_pps);
        }
        // 其他
        else {
            tmpCardType = PokerData.getPokerTypeString(hpokers, ppokers, data.nGameMode);
        }

        // 弃牌
        if (data.bFold) {
            if (data.vHandCards.length > 0) {
                hasCardType = true;
                cardType = tmpCardType;
            } else {
                cardType = pf.languageManager.getString('ActionTips1');
            }
        }
        // 未弃牌
        else {
            if (data.bMuck) {
                if (data.vHandCards.length > 0) hasCardType = true;
                cardType = pf.languageManager.getString('ActionTips10');
            } else {
                if (data.vHandCards.length > 0) {
                    hasCardType = true;
                    cardType = tmpCardType;
                } else {
                    cardType = '';
                }
            }
        }

        // 显示牌型
        if (cardType.length > 0) {
            this.img_cardtype.node.active = true;
            const cardTypeLabel = this.img_cardtype.node.getChildByName('txt').getComponent(cc.Label);
            cardTypeLabel.string = cardType;
            // TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
            // cardTypeLabel.node.color =
            //     data.jackpotType >= AwardPlayerJackpotType.Mars
            //         ? new cc.Color().fromHEX('FBD888')
            //         : new cc.Color().fromHEX('FFFFFF');
            cardTypeLabel.node.color =
                data.jackpotType >= 2 ? new cc.Color().fromHEX('FBD888') : new cc.Color().fromHEX('FFFFFF');
        } else {
            this.img_cardtype.node.active = false;
        }

        // 有牌型且"奥马哈"(则标记最大牌型时所参与的手牌和公牌)
        if (hasCardType && data.nGameID === pf.client.GameId.PLO) {
            let game_mode: number = data.nGameMode;
            let poker: PokerData = new PokerData(game_mode);

            for (let i = 0; i < this._vHandCard.length; ++i) {
                let card: CardControl = this._vHandCard[i];
                if (card.node.active && card.isFace() && card.hasContent()) {
                    card.setGray(true, 0xff * 0.5);
                }
            }

            for (let i = 0; i < this._vPubsCard.length; ++i) {
                let card: CardControl = this._vPubsCard[i];
                if (card.node.active && card.isFace() && card.hasContent()) {
                    card.setGray(true, 0xff * 0.5);
                }
            }

            // 检测手牌
            for (let i = 0; i < out_hps.length; ++i) {
                poker.initWithNumber(out_hps[i]);
                for (let j = 0; j < this._vHandCard.length; ++j) {
                    let card: CardControl = this._vHandCard[j];
                    let value: number = PokerData.getLocalValue(card.eCardNum, game_mode);
                    let color: number = card.eCardSuit;
                    if (poker.value === value && poker.color === color) {
                        card.setGray(false);
                        break;
                    }
                }
            }

            // 检测公牌
            for (let i = 0; i < out_pps.length; ++i) {
                poker.initWithNumber(out_pps[i]);
                for (let j = 0; j < this._vPubsCard.length; ++j) {
                    let card: CardControl = this._vPubsCard[j];
                    let value: number = PokerData.getLocalValue(card.eCardNum, game_mode);
                    let color: number = card.eCardSuit;
                    if (poker.value === value && poker.color === color) {
                        card.setGray(false);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 发发看功能(显示额外看到的共牌, 只有该玩家下过注参与才生效)
     * @param data
     * @param selfPubCardLen
     */
    private _checkSendOut(data: GameReviewItemData, selfPubCardLen: number): void {
        if (data.nPlayerID !== this._authService.currentUser.userId) return;

        let nSendOutOpacity: number = data.nGameID === pf.client.GameId.PLO ? 255 : 255 / 2; // 发发看的牌透明度
        let vALlPubCards: HandCardType[] = data.vPubsCards.concat(data.vUnsendPublicCards); // 所有共牌
        let nSelfPubCardLen: number = selfPubCardLen; // 当前自己能看到的公牌长度
        let nCompletCardLen: number = data.vPubsCards.length - nSelfPubCardLen; // 当前所需的补牌长度

        // 刷新已发发看的状态
        if (data.nReviewSendOutLen > data.vPubsCards.length) {
            for (let i = 0; i < this._vPubsCard.length; ++i) {
                if (i < data.nReviewSendOutLen - data.nReviewSendOutActLen) {
                    this._vPubsCard[i].node.active = true;
                    this._vPubsCard[i].setFace(true);
                    this._vPubsCard[i].setContent(vALlPubCards[i].number, vALlPubCards[i].suit);
                    this._vPubsCard[i].updateCardFace();
                    this._vPubsCard[i].updateCardBack();

                    // 若存在公牌
                    if (i < data.vPubsCards.length) {
                        // 自己的公牌长度不足, 则补牌
                        if (nCompletCardLen > 0) {
                            switch (nCompletCardLen) {
                                case 1:
                                    this.img_pub_card_box_1.node.active = true;
                                    break;
                                case 3:
                                    this.img_pub_card_box_3.node.active = true;
                                    break;
                                case 4:
                                    this.img_pub_card_box_4.node.active = true;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    // 若不存在公牌或已补完公牌, 则该玩家剩下的公牌全是发发看
                    else {
                        this._vPubsCard[i].node.opacity = nSendOutOpacity;
                    }
                } else {
                    this._vPubsCard[i].node.active = false;
                }
            }

            // 填充过公牌后, 要累计上已发发看的牌重新计算 nSelfPubCardLen 和 nCompletCardLen, 为动画流程做精确计算
            do {
                let selfVisiblePubCardLen = 0;
                for (let i = 0; i < this._vPubsCard.length; ++i) {
                    if (i < vALlPubCards.length && this._vPubsCard[i].node.active) {
                        ++selfVisiblePubCardLen;
                    }
                }
                nSelfPubCardLen = selfVisiblePubCardLen;
                nCompletCardLen = data.vPubsCards.length - nSelfPubCardLen;
            } while (false);

            // 计算牌型(暂时屏蔽)
            // this._calculateCardType(data);
        }

        // 即将发发看的动画
        if (data.nReviewSendOutActLen > 0) {
            let nFadeDuring = 0.5; // 渐变时间
            let nTurnDuring = 0.4; // 翻转时间

            let nCompletCardCount = 0; // 补全检索次数
            let nCompletCardStartIdx: number = data.nReviewSendOutLen - data.nReviewSendOutActLen; // 补全起始索引(无需补牌, 则索引从已知共牌长度开始)

            switch (nCompletCardLen) {
                // 补1张, 说明自己在"翻牌"阶段, 而牌局结束在"转牌"阶段
                case 1:
                    {
                        nCompletCardCount = 1;
                        nCompletCardStartIdx = 3;
                        this.img_pub_card_box_1.node.active = true;
                    }
                    break;

                // 补3张, 说明自己在"翻牌前"阶段, 而牌局结束在"翻牌"阶段
                case 3:
                    {
                        nCompletCardCount = 3;
                        nCompletCardStartIdx = 0;
                        this.img_pub_card_box_3.node.active = true;
                    }
                    break;

                // 补4张, 说明自己在"翻牌前"阶段, 而牌局结束在"转牌"阶段
                case 4:
                    {
                        nCompletCardCount = 4;
                        nCompletCardStartIdx = 0;
                        this.img_pub_card_box_4.node.active = true;
                    }
                    break;

                default:
                    break;
            }

            for (let i = nCompletCardStartIdx, m = 0, n = 0; i < this._vPubsCard.length; ++i) {
                if (i < vALlPubCards.length) {
                    // 补全动画: 渐显至透明度为 255, 然后翻转
                    if (nCompletCardLen > 0 && m++ < nCompletCardCount) {
                        this._vPubsCard[i].node.active = true;
                        this._vPubsCard[i].setFace(false);
                        this._vPubsCard[i].setContent(vALlPubCards[i].number, vALlPubCards[i].suit);
                        this._vPubsCard[i].updateCardFace();
                        this._vPubsCard[i].updateCardBack();
                        this._vPubsCard[i].node.opacity = 0;

                        this._vPubsCard[i].node.runAction(cc.fadeTo(nFadeDuring, 255));
                        this._vPubsCard[i].turn(nFadeDuring, true);
                    }
                    // 发发看动画: 渐显至透明度为 255/2, 然后翻转
                    else if (n++ < data.nReviewSendOutActLen) {
                        this._vPubsCard[i].node.active = true;
                        this._vPubsCard[i].setFace(false);
                        this._vPubsCard[i].setContent(vALlPubCards[i].number, vALlPubCards[i].suit);
                        this._vPubsCard[i].updateCardFace();
                        this._vPubsCard[i].updateCardBack();
                        this._vPubsCard[i].node.opacity = 0;

                        let delay: number = nCompletCardLen > 0 ? nFadeDuring + nTurnDuring : 0;
                        this._vPubsCard[i].node.runAction(
                            cc.sequence(cc.delayTime(delay), cc.fadeTo(nFadeDuring, nSendOutOpacity))
                        );
                        delay += nFadeDuring;
                        this._vPubsCard[i].turn(delay, true);
                        delay += nTurnDuring;
                        this.scheduleOnce((elapsed: number): void => {
                            // 计算牌型(暂时屏蔽)
                            // this._calculateCardType(data);
                        }, delay);
                    }
                }
            }

            // 清空发发看动画标记
            data.nReviewSendOutActLen = 0;
        }
    }

    /**
     * 更新数据
     */
    updateSVReuseData(data: GameReviewItemData, totalCount: number): void {
        // 头像
        // CircleSprite.setCircleSprite(this.img_head.node, data.sPlayerHead, data.plat);
        this.selfAvatar.loadHeadImage(data.sPlayerHead, data.plat);
        this.panel_card.active = true;

        // 重置相关控件状态
        this._resetCtl();

        // Check is falling mars jackpot
        // TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
        // const isFallingMars = data.jackpotType >= AwardPlayerJackpotType.Mars;
        const isFallingMars = data.jackpotType >= 2;
        const usernameTxt =
            isFallingMars && this.txt_falling_mars_username ? this.txt_falling_mars_username : this.txt_username;
        this.txt_username.node.active = !isFallingMars;

        // 昵称(是否有备注)
        // let tRemarkData: RemarkData = cv.dataHandler.getUserData().vRemarkData.get(data.nPlayerID);
        // if (!tRemarkData || !tRemarkData.sRemark) {
        //     cv.StringTools.setShrinkString(usernameTxt.node, data.sPlayerName, true);
        // } else {
        //     cv.StringTools.setShrinkString(usernameTxt.node, tRemarkData.sRemark, true);
        // }

        if (this.img_bg) {
            this.img_bg.enabled = isFallingMars;
        }

        if (this.falling_mars_holder) {
            this.falling_mars_holder.active = isFallingMars;
        }

        if (isFallingMars) {
            // TODO Cannot use JackpotData because jackpot bundle is not loaded yet
            // const isEarthPlayer = data.jackpotType === AwardPlayerJackpotType.Earth;
            const isEarthPlayer = data.jackpotType === 3;
            if (this.txt_player_type) {
                this.txt_player_type.string = pf.StringUtil.formatC(
                    pf.languageManager.getString(`${isEarthPlayer ? 'Earth_Player' : 'Mars_Player'}`)
                );
            }

            if (this.img_jackpot_mars) {
                this.img_jackpot_mars.spriteFrame = isEarthPlayer ? this.jackpot_mars_sf[0] : this.jackpot_mars_sf[1];
            }
        }
        if (this.super_squid_icon) this.super_squid_icon.active = false;
        this.squid_loss_or_gain.node.active = false;
        if (this.Squid_Temp_Away) this.Squid_Temp_Away.active = false;
        if (this.squid_counter) this.squid_counter.node.parent.parent.active = false;

        // 设置"输赢"金额
        this._setWinAmount(data.nWinBet);

        // 设置保险"赔付/投保"金额
        this._setInsuredAmount(data.nInsuranceAmount, data.nInsuranceBet);

        // 设置"JP"输赢
        this._setJackPotAmout(data.nJackWinbet);

        // 排版底栏面板
        this._layoutPanelBottom();

        // 手牌堆
        for (let i = 0; i < this._vHandCard.length; ++i) {
            // 恢复牌状态
            this._vHandCard[i].node.active = true;
            this._vHandCard[i].node.stopAllActions();
            this._vHandCard[i].node.setScale(this._handCardScaleRatio);
            this._vHandCard[i].node.opacity = 255;
            this._vHandCard[i].setGray(false);
            this._vHandCard[i].clearContent();
            this._vHandCard[i].setGameID(data.nGameID);

            if (i < data.vHandCards.length) {
                this._vHandCard[i].setFace(true);
                this._vHandCard[i].setContent(data.vHandCards[i].number, data.vHandCards[i].suit);
                this._vHandCard[i].updateCardFace();
                this._vHandCard[i].updateCardBack();

                // 是否有强制亮牌
                if (data.nForceShowedActLen > 0) {
                    // 长度为1, 说明第一张手牌已亮
                    if (data.nForceShowedActLen === 1) {
                        if (i === 0) continue;
                    }
                    // 长度为2, 说明两张手牌都没亮
                    else {
                    }

                    // 翻牌动画
                    this._vHandCard[i].setFace(false); // 设置为牌背
                    let delay = 0.2; // 初始延时
                    let nTurnDuring = 0.4; // 翻转时间
                    this._vHandCard[i].turn(delay, true); // 开始翻转
                    delay += nTurnDuring; // 延时后显示牌型
                    this.scheduleOnce((elapsed: number): void => {
                        data.nForceShowedActLen = 0; // 清空手牌动画标记
                        this._calculateCardType(data); // 计算牌型
                    }, delay);
                }
            } else {
                this._vHandCard[i].setFace(false);
                this._vHandCard[i].updateCardFace();
                this._vHandCard[i].updateCardBack();
            }
        }

        // 公共牌堆
        let nSelfPubCardLen = 0;
        let bHaveBets: boolean = data.nPlayerBettingRoundBet > 0;
        for (let i = 0; i < this._vPubsCard.length; ++i) {
            // 恢复牌状态
            this._vPubsCard[i].node.active = true;
            this._vPubsCard[i].node.stopAllActions();
            this._vPubsCard[i].node.setScale(this._pubsCardScaleRatio);
            this._vPubsCard[i].node.opacity = 255;
            this._vPubsCard[i].setGray(false);
            this._vPubsCard[i].clearContent();
            this._vPubsCard[i].setGameID(data.nGameID);

            if (bHaveBets) {
                if (i < data.vPubsCards.length) {
                    // 老数据(这里的 LastRoundType 在牌局中服务器下发是-1的, 但是从data服下发是没有-1, 目的是为了给牌局回顾判断新老数据)
                    if (data.nLastRoundType <= 0) {
                        this._vPubsCard[i].node.active = true;
                        ++nSelfPubCardLen;
                    }
                    // 新数据逻辑
                    else {
                        switch (data.nLastRoundType) {
                            case GameReviewBettingRoundType.Enum_BettingRound_None:
                                break;
                            case GameReviewBettingRoundType.Enum_BettingRound_Preflop:
                                nSelfPubCardLen = 0;
                                break;
                            case GameReviewBettingRoundType.Enum_BettingRound_Flop:
                                nSelfPubCardLen = 3;
                                break;
                            case GameReviewBettingRoundType.Enum_BettingRound_Turn:
                                nSelfPubCardLen = 4;
                                break;
                            case GameReviewBettingRoundType.Enum_BettingRound_River:
                                nSelfPubCardLen = 5;
                                break;
                            case GameReviewBettingRoundType.Enum_BettingRound_ShowDown:
                                nSelfPubCardLen = 5;
                                break;
                            default:
                                break;
                        }
                        this._vPubsCard[i].node.active = i < nSelfPubCardLen;
                    }

                    // 填充牌内容
                    if (this._vPubsCard[i].node.active) {
                        this._vPubsCard[i].setFace(true);
                        this._vPubsCard[i].setContent(data.vPubsCards[i].number, data.vPubsCards[i].suit);
                        this._vPubsCard[i].updateCardFace();
                        this._vPubsCard[i].updateCardBack();
                    }
                } else {
                    this._vPubsCard[i].node.active = false;
                }
            } else {
                this._vPubsCard[i].node.active = false;
            }
        }

        // "长/短"牌增加D图标显示(>=2个玩家都只显示D)
        // 000=default, 001=D, 010=SB, 100=BB
        this.img_d.node.active = (data.seatInfo & 1) === 1 && totalCount >= 2;

        // 设置下注信息
        if (bHaveBets) this._setBetRoundsInfo(data);

        // 计算牌型(如果正在翻手牌动画则不显示牌型, 翻完手牌在显示牌型)
        if (data.nForceShowedActLen <= 0) {
            this._calculateCardType(data);
        }

        // 发发看(无检测 bHaveBets 条件, 这里的 data 数据都是牌桌参与者)
        this._checkSendOut(data, nSelfPubCardLen);

        if (data.nGameID === pf.client.GameId.Squid) this.setSquidInfoActive(data);
    }
}
