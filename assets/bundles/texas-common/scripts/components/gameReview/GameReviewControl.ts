/* eslint-disable @typescript-eslint/prefer-for-of */
/* eslint-disable complexity */
/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */

import * as pf from 'pf';
import * as common from 'common';
import { macros } from '../../common/texas-macros';
import commonMacros = common.macros;
import * as gs_protocol from '../../network/pkw/pb/gs_protocol';
import game_pb = gs_protocol.protocol;
import { GameReplayControl } from './GameReplayControl';
import { GameReviewItemComp, GameReviewItemData } from './GameReviewItemComp';
import CustomToggle = common.components.CustomToggle;
import ScrollViewItemPool = common.components.ScrollViewItemPoolControl;
import PopupManager = common.components.PopupManager;
import { action } from '../../../../common/scripts/components/ActionControl';
import PokerHandData = pf.data.PokerHandData;
import PlayerRecord = pf.data.PlayerRecord;
import ReplayData = pf.data.ReplayData;
import HandMapCacheManagerControl = common.components.HandMapCacheManagerControl;
import { CollectPokerMapData, GameRecordsData } from '../../domain/game-records-data';
import type * as domain from '../../domain/texas-domain-index';
import { GameReviewService } from '../../domain/texas-domain-index';
// TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
// import { AwardPlayerJackpotType } from '../../../../jackpot/scripts/domain/jackpot-data';
import type { FeeItem } from '../../domain/texas-domain-index';
import { HandCardType } from '../../../../../poker-framework/scripts/services/data/data-service-index';
import { TexasCommonDef } from '../../common/texas-define';

/**
 *  牌局回顾面板逻辑
 */
const { ccclass, property } = cc._decorator;

export enum SquidHandType {
    Normal_Hand = 0,
    Squid_Final_Hand = 1,
    Squid_Settlement_Hand = 2
}

export enum ButtonEvent {
    First = 0,
    Prev = 2,
    Next = 3,
    Last = 4
}

@ccclass
export class GameReviewControl extends cc.Component {
    private _pokerRoom: domain.TexasRoom = null;
    private _authService: pf.services.AuthService = null;
    private _gameReviewService: GameReviewService = null;
    private _auditService: pf.services.AuditService = null;
    private _dataService: pf.services.DataService = null;

    // Bound callback functions for event listeners
    private _boundOnMsgUpdateDataHand = this._onMsgUpdateDataHand.bind(this);
    private _boundOnMsgLastHand = this._onMsgLastHand.bind(this);
    private _boundOnMsgNextHand = this._onMsgNextHand.bind(this);
    @property(cc.Prefab) prefab_game_replay: cc.Prefab = null; // 牌局回顾预制
    @property(cc.Prefab) prefab_report: cc.Prefab = null; // 举报框预设体

    @property(cc.Node) panel_main: cc.Node = null; // 主面板
    @property(cc.Node) panel_top: cc.Node = null; // 顶栏面板
    @property(cc.Node) panel_bottom: cc.Node = null; // 底栏面板

    @property(cc.Label) txt_title: cc.Label = null; // 标题
    @property(cc.Label) txt_serial: cc.Label = null; // 牌局编号
    @property(cc.Label) txt_time: cc.Label = null; // 牌局时间

    // panel_1
    @property(cc.Label) txt_pot_word: cc.Label = null; // 底池 文字
    @property(cc.Label) txt_insurance_word: cc.Label = null; // 保险 文字
    @property(cc.Label) txt_jackpot_word: cc.Label = null; // jackpot 文字

    @property(cc.Label) txt_pot: cc.Label = null; // 底池 数量
    @property(cc.Label) txt_insurance: cc.Label = null; // 保险 数量
    @property(cc.Label) txt_jackpot: cc.Label = null; // jackpot 数量

    // panel_3
    @property(cc.Slider) slider: cc.Slider = null; // 滑条控件
    @property(cc.Sprite) slider_bg: cc.Sprite = null; // 滑条底图
    @property(cc.Sprite) slider_sp: cc.Sprite = null; // 滑条精灵

    @property(cc.Label) txt_page: cc.Label = null; // 页数
    @property(cc.Label) txt_invalid_data_desc: cc.Label = null; // 无效数据描述文本

    @property(cc.Button) btn_forceshow: cc.Button = null; // 强制亮牌
    @property(cc.Button) btn_sendout: cc.Button = null; // 发发看
    @property(cc.Button) btn_audit: cc.Button = null; // 举报
    @property(cc.Button) btn_play: cc.Button = null; // 播放
    @property(cc.Button) btn_collect: cc.Button = null; // 收藏
    @property(cc.Button) btn_first: cc.Button = null; // 最前页按钮
    @property(cc.Button) btn_last: cc.Button = null; // 最后页按钮
    @property(cc.Button) btn_before: cc.Button = null; // 上一步按钮
    @property(cc.Button) btn_next: cc.Button = null; // 下一步按钮

    @property(cc.Button) btn_FeatureHand: cc.Button = null;

    @property(cc.Node) panel_toggle_pot: cc.Node = null; // 精彩对局筛选面板
    @property(CustomToggle) toggle_pot: CustomToggle = null; // 精彩对局筛选

    @property(cc.SpriteFrame) icon_usd: cc.SpriteFrame = null;

    // jackpot mars
    @property(cc.Node) jackpot_holder: cc.Node = null;
    @property(cc.Label) txt_total_jackpot: cc.Label = null;
    @property(cc.Widget) panel_scrollview_widget: cc.Widget = null;
    @property(cc.Sprite) jackpot_title_sprite: cc.Sprite = null;
    @property(cc.SpriteFrame) jackpot_title_en_sprite_frame: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) jackpot_title_zh_sprite_frame: cc.SpriteFrame = null;

    // Scroll view
    @property(ScrollViewItemPool) scroll_view_item_pool: ScrollViewItemPool = null;
    @property(ScrollViewItemPool) scroll_view_item_pool_plo: ScrollViewItemPool = null;

    @property(cc.Label) txt_FeatureSubmit: cc.Label = null;
    @property(cc.Color) featureHandLabelActiveColor: cc.Color = null;
    @property(cc.Color) featureHandLabelInactiveColor: cc.Color = null;
    @property(cc.Node) featureHand_StarIcon: cc.Node = null;
    // @property(SquidSettlementReview) squidSettlementReview: SquidSettlementReview = null;

    // 类名
    static gClassName: string = 'GameReview';

    // 是否已初始化实例(是否add到对应节点)
    private _bInit: boolean = false;

    // 当前显示的牌局uuid
    private _sCurGameUUID: string = '';

    // 当前牌局正在结算的uuid (不能及时取 gameDataMgr.tGameData.game_settlement_uuid 的值, 有可能在切页的时候牌局状态改变导致值改变, 这里要保存打开牌谱时刻的值)
    private _sGameSettlementUUID: string = '';

    // 牌谱数据源数组
    private _vGameUUID: string[] = [];

    // 牌谱数组当前索引
    private _nCurGameUUIDIndex: number = -1;

    // 上次保存的牌谱页签(切换场景时自动被清理)
    private _nLastSaveGameUUIDIndex: number = -1;

    // 数据源类型
    private _dataSourceType: TexasCommonDef.GameReviewDataType = TexasCommonDef.GameReviewDataType.EDST_NONE;

    // 上次滑条进度
    private _nLastSliderProgress: number = 0;

    // 滑条滚动系数
    private _nSliderPerRatio: number = 1;

    // 屏蔽层(总体)
    private _shieldLayer: cc.Node = null;

    // 是否显示举报按钮
    private _bShowAudit: boolean = false;

    // 是有存在回放数据(有的时候 send uuid 后收到的回访数据为空或空串, 这里标记下)
    private _bHasReplayData: boolean = false;

    // 牌局回放对象
    private _tGameReplay: GameReplayControl = null;

    // 顶栏面板字体大小
    private _nTopPanelFontSize: number = 0;

    // 翻页页签文本原始位置
    private _txt_page_src_pos: cc.Vec2 = cc.Vec2.ZERO;

    // 上次"精彩对局筛选"的勾选状态
    private _last_toggle_pot_check_status: boolean = false;

    private _last_game_id: number;

    private _currRoomId: number = null;

    private currentHandData: PokerHandData = null;

    private tGameRecords: GameRecordsData = new GameRecordsData();

    private tCollectPokerMapData: CollectPokerMapData = new CollectPokerMapData();

    private _handMapCache: HandMapCacheManagerControl = null;

    private _vGameUUID_SquidFinalSettelment: string[] = [];

    private _buttonEvent: ButtonEvent = ButtonEvent.Prev;
    private _squidHandType: SquidHandType = SquidHandType.Normal_Hand;
    private _sliblingIndex: number = 0;

    tPokerHandData: PokerHandData = null;
    /**
     * 获取预制件单实例(一个场景中只存在一个)
     */
    static getSinglePrefabInst(prefab: cc.Prefab, siblingIndex?: number): cc.Node {
        if (!(prefab instanceof cc.Prefab)) return null;

        let instNode: cc.Node = cc.director.getScene().getChildByName(GameReviewControl.gClassName);
        if (!instNode) {
            instNode = cc.instantiate(prefab);
            if (!instNode.getComponent(GameReviewControl)) {
                instNode.destroy();
                return null;
            }
            instNode.getComponent(GameReviewControl)._sliblingIndex = siblingIndex
                ? siblingIndex
                : common.macros.ZORDER_TYPE.ZORDER_4;
        }
        return instNode;
    }

    /**
     * 是否初始化(add到对应的父节点上)
     */
    isInit(): boolean {
        return this._bInit;
    }

    /**
     * 显示视图(内部已自动处理数据)
     * @param dataSourceType    数据源类型
     * @param vGameUUID         牌局uuid数组(可选, 默认读取对应缓存数据)
     * @param zorder            视图深度值(可选, 默认当前父节点 childrenCount + 1)
     */
    autoShow(
        dataSourceType: TexasCommonDef.GameReviewDataType,
        vGameUUID?: string[],
        zorder?: number,
        bAnim: boolean = true
    ): void {
        if (!this._pokerRoom) {
            const context = pf.app.getGameContext<pf.services.GameContext>();
            this._pokerRoom = context.room as domain.TexasRoom;
        }
        // data
        this._dataSourceType = dataSourceType;

        let pointedTotheLatest = false;
        if (this._vGameUUID && this._vGameUUID.length > 0) {
            if (this._nLastSaveGameUUIDIndex > -1 && this._nLastSaveGameUUIDIndex === this._vGameUUID.length - 1) {
                pointedTotheLatest = true;
            }
        }

        this._vGameUUID = vGameUUID ? vGameUUID : this._getGameuuids();

        // point to the latest gameReview if it was before
        if (pointedTotheLatest) this._nLastSaveGameUUIDIndex = this._vGameUUID.length - 1;

        this._sGameSettlementUUID = this._pokerRoom.gameParam.gameSettlementUuid;

        let scene = cc.director.getScene();
        zorder = zorder ? zorder : this._sliblingIndex;

        // shieldLayer
        if (!this._shieldLayer) {
            this._shieldLayer = action.createShieldLayer(
                scene,
                'shieldLayer-gameReview',
                -1,
                cc.Color.BLACK,
                255 * 0.3
            );
        }
        this._shieldLayer.setSiblingIndex(zorder);

        // add
        let viewNode = scene.getChildByName(GameReviewControl.gClassName);
        if (!viewNode) {
            viewNode = this.node;
            viewNode.name = GameReviewControl.gClassName;
            scene.addChild(viewNode);
            viewNode.setSiblingIndex(zorder + 1);
            this._bInit = true;
        } else {
            this._resetView();
        }

        let finishCb: Function;
        let startCb: Function;

        if (this._currRoomId === this._pokerRoom.id) {
            // it is not the first time open game review panel
            startCb = this._actFuncFinish;
            finishCb = this._actFunc;
        } else {
            // it is the first time that user open this room's game review panel
            startCb = this._actFunc;
            finishCb = this._actFuncFinish;
        }

        this._currRoomId = this._pokerRoom.roomData.u32RoomId;

        // action
        this._shieldLayer.active = true;
        if (bAnim) {
            action.showAction(
                this.node,
                action.eMoveActionDir.EMAD_TO_LEFT,
                action.eMoveActionType.EMAT_FADE_IN,
                action.delayType.NORMAL,
                {
                    actFunc: startCb.bind(this),
                    actFuncFinish: finishCb.bind(this),
                    actFinishCallbackDelay: 1 / cc.game.getFrameRate()
                }
            );
        } else {
            this.node.active = true;
            this._preUpdateView();
        }
    }

    /**
     * 隐藏
     */
    autoHide(bAnim: boolean = true): void {
        if (!this.isInit()) return;

        if (this.node.active) {
            if (this._shieldLayer) this._shieldLayer.active = false;
            if (this._tGameReplay) this._tGameReplay.autoHide(false);

            if (bAnim) {
                action.showAction(
                    this.node,
                    action.eMoveActionDir.EMAD_TO_RIGHT,
                    action.eMoveActionType.EMAT_FADE_OUT,
                    action.delayType.NORMAL,
                    {
                        actFunc: this._actFunc.bind(this),
                        actFuncFinish: this._actFuncFinish.bind(this)
                    }
                );
            } else {
                this.node.active = false;
                this._resetView();
            }
        }
    }

    /**
     * 前一页
     */
    beforePage(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (!this.isInit()) return;
        if (this._vGameUUID.length <= 1) return;
        this._buttonEvent = ButtonEvent.Prev;
        if (this._squidHandType === SquidHandType.Squid_Settlement_Hand) {
            this._onMsgUpdateDataHand();
            return;
        }

        let progress: number = Math.max(0, pf.MathUtil.minus(this.slider.progress, this._nSliderPerRatio));
        this._setSliderProgress(progress);
        this._updateSliderPercent();
    }

    /**
     * 后一页
     */
    nextPage(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (!this.isInit()) return;
        if (this._vGameUUID.length <= 1) return;
        this._buttonEvent = ButtonEvent.Next;
        if (this._squidHandType === SquidHandType.Squid_Final_Hand) {
            this._onMsgUpdateDataHand();
            return;
        }
        let progress: number = Math.min(1, pf.MathUtil.plus(this.slider.progress, this._nSliderPerRatio));
        this._setSliderProgress(progress);
        this._updateSliderPercent();
    }

    /**
     * 第一页
     */
    firstPage(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (!this.isInit()) return;
        if (this._vGameUUID.length <= 1) return;
        this._buttonEvent = ButtonEvent.First;
        this._setSliderProgress(0);
        this._updateSliderPercent();
    }

    /**
     * 最后一页
     */
    lastPage(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (!this.isInit()) return;
        if (this._vGameUUID.length <= 1) return;
        this._buttonEvent = ButtonEvent.Last;
        this._setSliderProgress(1);
        this._updateSliderPercent();
    }

    /**
     * 是否显示举报按钮()
     * @param bShow
     */
    setShowAudit(bShow: boolean): void {
        this._bShowAudit = bShow;

        if (this.isInit()) {
            this._onMsgUpdateDataHand();
        }
    }

    /**
     * 重置上次保存的牌谱页签
     */
    resetSavePage(): void {
        this._nLastSaveGameUUIDIndex = -1;
    }

    /**
     * 处理收藏牌局数据
     */
    private _processFavoriteHandData(value: string): void {
        try {
            const data = JSON.parse(value);
            // Process the favorite hand data similar to how cv.httpHandler._onFavoriteHand would handle it
            this._onMsgUpdateDataHand();
        } catch (error) {
            console.error('Error processing favorite hand data:', error);
        }
    }

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.GameContext>();
        this._pokerRoom = context.room as domain.TexasRoom;
        this._authService = pf.serviceManager.get(pf.services.AuthService);
        this._gameReviewService = new GameReviewService();
        this._auditService = pf.serviceManager.get(pf.services.AuditService);
        this._dataService = pf.serviceManager.get(pf.services.DataService);

        if (!pf.system.view.isFullScreen() || !pf.system.isNative) {
            this.panel_main.getComponent(cc.Widget).top = 50;
        }

        // let nodeStatusListener = this.node.addComponent(NodeStatusListener);
        // nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);

        common.UIUtil.adaptWidget(this.node, true);
        let widget: cc.Widget = this.node.getComponent(cc.Widget);
        if (widget) {
            widget.destroy();
        }

        // 点击 移出ui
        this.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event): void => {
                this.autoHide();
            },
            this
        );

        // 翻页
        this.btn_first.node.on(
            'click',
            (event: cc.Event): void => {
                this.firstPage();
            },
            this
        );
        this.btn_last.node.on(
            'click',
            (event: cc.Event): void => {
                this.lastPage();
            },
            this
        );
        this.btn_before.node.on(
            'click',
            (event: cc.Event): void => {
                this.beforePage();
            },
            this
        );
        this.btn_next.node.on(
            'click',
            (event: cc.Event): void => {
                this.nextPage();
            },
            this
        );

        this.panel_toggle_pot.on('click', this._onClickPanelTogglePot, this);
        this.toggle_pot.node.on('toggle', this._onClickTogglePot, this);

        // 编号/播放/举报/收藏/强制亮牌/发发看
        this.txt_serial.node.on('click', this._onClickSerialNumber, this);
        this.btn_play.node.on('click', this._onClickPlay, this);
        this.btn_audit.node.on('click', this._onClickAudit, this);
        this.btn_collect.node.on('click', this._onClickCollect, this);
        this.btn_forceshow.node.on('click', this._onClickForceShow, this);
        this.btn_sendout.node.on('click', this._onClickSendOut, this);

        // 设置进度条
        this.slider.node.on('slide', this._onSliderEvent, this);

        // 保存顶栏面板字体大小
        this._nTopPanelFontSize = this.txt_pot_word.fontSize;

        // 翻页页签文本原始位置
        this._txt_page_src_pos = cc.v2(this.txt_page.node.position);

        // 隐藏相关控件
        this._setForceShowBtnVisible(false);
        this._setSendOutBtnVisible(false);
        this._handMapCache = new HandMapCacheManagerControl();
        this._handMapCache.loadCache();
    }

    protected start(): void {
        this._resetView();
    }

    protected onEnable(): void {
        if (!CC_EDITOR) {
            this._registerEvent();
        }
    }

    protected onDisable(): void {
        if (!CC_EDITOR) {
            this._unregisterEvent();
        }
    }

    protected onDestroy(): void {
        console.log(`${GameReviewControl.gClassName}: onDestroy`);
        this._handMapCache.saveCache();
    }

    private _registerEvent(): void {
        this._gameReviewService.addListener('update_hand', this._boundOnMsgUpdateDataHand);
        this._gameReviewService.addListener('game_replay_lastHand', this._boundOnMsgLastHand);
        this._gameReviewService.addListener('game_replay_nextHand', this._boundOnMsgNextHand);
        // cv.MessageCenter.register('show_Audit', this._onMsgShowAudit.bind(this), this.node);
        this._pokerRoom.addListener('on_replay_forceshow', this._onMsgForceShow.bind(this));
        this._pokerRoom.addListener('on_replay_sendout', this._onMsgSendOut.bind(this));
        // cv.MessageCenter.register('OnSumbitHadnResponseRecieved', this.onHandRequestResponseRecieved.bind(this), this.node);
        // cv.MessageCenter.register('OnFeatureHandConfigResponse', this.onFeatureHandConfig.bind(this), this.node);
    }

    private _unregisterEvent(): void {
        this._gameReviewService.removeListener('update_hand', this._boundOnMsgUpdateDataHand);
        this._gameReviewService.removeListener('game_replay_lastHand', this._boundOnMsgLastHand);
        this._gameReviewService.removeListener('game_replay_nextHand', this._boundOnMsgNextHand);
        // cv.MessageCenter.unregister('show_Audit', this.node);
        this._pokerRoom.removeListener('on_replay_forceshow', this._onMsgForceShow.bind(this));
        this._pokerRoom.removeListener('on_replay_sendout', this._onMsgSendOut.bind(this));
        // cv.MessageCenter.unregister('OnSumbitHadnResponseRecieved', this.node);
        // cv.MessageCenter.unregister('OnFeatureHandConfigResponse', this.node);
    }

    /**
     * 动作前回调
     * @param target
     * @param actIO
     */
    private _actFunc(target: cc.Node, actIO: number): void {}

    /**
     * 动作后回调
     * @param target
     * @param actIO
     */
    private _actFuncFinish(target: cc.Node, actIO: number): void {
        // 切入
        if (actIO === action.eMoveActionType.EMAT_FADE_IN) {
            this._preUpdateView();
        }
        // 切出
        else {
            this._resetView();
        }
    }

    /**
     * 获取牌局uuid列表
     */
    private _getGameuuids(): string[] {
        let vGameUUID: string[] = [];

        // 数据要深拷贝
        switch (this._dataSourceType) {
            case TexasCommonDef.GameReviewDataType.EDST_NONE:
            case TexasCommonDef.GameReviewDataType.EDST_RECORD:
                {
                    let vHandUUIDList: string[] = this.tGameRecords.tPokerInfoData.vHandUUIDList;
                    vGameUUID = vHandUUIDList.slice(0, vHandUUIDList.length);
                }
                break;

            case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                {
                    let vHandUUIDList: string[] = this._pokerRoom.roomData.game_uuids_js;
                    vGameUUID = vHandUUIDList.slice(0, vHandUUIDList.length);
                }
                break;

            case TexasCommonDef.GameReviewDataType.EDST_COLLECTION:
                break;
            default:
                break;
        }

        return vGameUUID;
    }

    /**
     * 重置视图
     */
    private _resetView(): void {
        this.txt_invalid_data_desc.node.active = false;
        this._nCurGameUUIDIndex = -1;
        this._setSliderProgress(0);

        // 显示前就更新"勾选大pot"设置
        this.panel_toggle_pot.active = false;
        this.txt_page.node.y = this.btn_first.node.y;

        // 获取游戏id
        let gameid = 0;
        switch (this._dataSourceType) {
            case TexasCommonDef.GameReviewDataType.EDST_RECORD:
                gameid = this.tGameRecords.gameID;
                break;

            case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                gameid = this._pokerRoom.roomData.u32GameID;
                break;

            default:
                break;
        }

        // 初始化精彩对局状态
        if (gameid > 0) {
            let visible = true;
            visible &&= this._vGameUUID.length > 0;
            visible &&= !this._pokerRoom.roomData.isZoom();

            this.panel_toggle_pot.active = visible;
            if (this.panel_toggle_pot.active) {
                // 房间中保留上次勾选状态
                if (this._dataSourceType === TexasCommonDef.GameReviewDataType.EDST_GAMEROOM) {
                    this.toggle_pot.isChecked = this._last_toggle_pot_check_status;
                }
                // 战绩列表中
                else {
                    // 若上次已勾选, 则本次取消勾选且重置牌谱保存的页签
                    if (this._last_toggle_pot_check_status) {
                        this._last_toggle_pot_check_status = false;
                        this.resetSavePage();
                    }

                    // 取消勾选
                    this.toggle_pot.isChecked = this._last_toggle_pot_check_status;
                }

                this.txt_page.node.y = this._txt_page_src_pos.y;

                // 若已勾选则重置"滑条 + 页数"
                if (this.toggle_pot.isChecked) {
                    this._vGameUUID = [];
                    this._setSliderProgress(0);
                }
            }
        }

        // 清理视图
        let t: PokerHandData = new PokerHandData();
        // t.reset();
        // 更新静态文本
        this._updateStaticText();
        this._updateDataView(t);
    }

    /**
     * 预更新视图
     */
    private _preUpdateView(): void {
        if (this.panel_toggle_pot.active && this.toggle_pot.isChecked) {
            this._requestBigPotGameUUIDs(this._nLastSaveGameUUIDIndex);
        } else {
            this._updateView(this._nLastSaveGameUUIDIndex);
        }
    }

    /**
     * 更新视图 + 数据
     * @param transGameUUIDIndex 跳转第几手牌局索引(默认缺省,主要用于记录下次打开牌谱是的第几手的页签)
     */
    private _updateView(transGameUUIDIndex: number = -1): void {
        // 计算滑条滚动系数
        if (this._vGameUUID.length > 1) {
            this._nSliderPerRatio = pf.MathUtil.div(1, this._vGameUUID.length - 1);
        } else if (this._vGameUUID.length === 1) {
            this._nSliderPerRatio = 1;
        }

        // 重置牌谱数组当前索引
        this._nCurGameUUIDIndex = -1;
        this._setSliderProgress(this._vGameUUID.length <= 0 ? 0 : 1);

        // 检测保存的页签值
        if (transGameUUIDIndex >= 0) {
            transGameUUIDIndex = Math.min(transGameUUIDIndex, this._vGameUUID.length);
            let progress: number = pf.MathUtil.times(transGameUUIDIndex, this._nSliderPerRatio);
            progress = Math.min(1, progress);
            this._setSliderProgress(progress);
        }

        // 更新数据
        this._updateSliderPercent(true);
        this._onMsgUpdateDataHand();
    }

    /**
     * 更新静态文本描述
     */
    private _updateStaticText(): void {
        this.txt_title.string = pf.languageManager.getString('allReview_allReview_panel_review_txt'); // 标题
        this.txt_pot_word.string = pf.languageManager.getString('allReview_allReview_panel_pot_txt'); // 底池
        this.txt_insurance_word.string = pf.languageManager.getString('allReview_allReview_panel_insurance_txt'); // 保险

        const txt_forceshow: cc.Label = this.btn_forceshow.node.getChildByName('txt_title').getComponent(cc.Label);
        txt_forceshow.string = pf.languageManager.getString('allReview_allReview_panel_forceshow_txt'); // 强制亮牌

        const txt_sendout: cc.Label = this.btn_sendout.node.getChildByName('txt_title').getComponent(cc.Label);
        txt_sendout.string = pf.languageManager.getString('allReview_allReview_panel_sendout_txt'); // 强制亮牌

        const panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
        const txt_audit: cc.Label = panel_bottom_top.getChildByName('txt_audit').getComponent(cc.Label);
        txt_audit.string = pf.languageManager.getString('allReview_allReview_panel_audit_img_audit_txt'); // 举报

        const txt_play: cc.Label = panel_bottom_top.getChildByName('txt_play').getComponent(cc.Label);
        txt_play.string = pf.languageManager.getString('allReview_allReview_panel_replay_img_replay_txt'); // 播放

        const txt_collect: cc.Label = panel_bottom_top.getChildByName('txt_collect').getComponent(cc.Label);
        txt_collect.string = pf.languageManager.getString('allReview_allReview_panel_collect_img_collect_txt'); // 收藏

        this.txt_FeatureSubmit.string = pf.languageManager.getString('game_review_feature_hand_submit');

        // 精彩对局(大"pot"筛选)
        if (this.panel_toggle_pot.active) {
            const txt_toggle_pot: cc.Label = this.panel_toggle_pot
                .getChildByName('txt_toggle_pot')
                .getComponent(cc.Label);
            txt_toggle_pot.string = pf.languageManager.getString('allReview_allReview_panel_toggle_img_toggle_txt');

            // 大"pot"勾选面板居中
            const offset_x = 15;
            const txt_w: number = common.UIUtil.updateAndMeasureLabel(txt_toggle_pot).width;
            this.panel_toggle_pot.setContentSize(
                txt_w + offset_x + this.toggle_pot.node.width,
                this.panel_toggle_pot.height
            );

            // 适配文本和筛选按钮位置
            let x: number = -this.panel_toggle_pot.width * this.panel_toggle_pot.anchorX;
            const y: number = txt_toggle_pot.node.y;
            x += (this.panel_toggle_pot.width - (txt_w + offset_x + this.toggle_pot.node.width)) / 2;
            x += txt_w * txt_toggle_pot.node.anchorX;
            txt_toggle_pot.node.setPosition(x, y);
            x += txt_w * (1 - txt_toggle_pot.node.anchorX);
            x += offset_x;
            x += this.toggle_pot.node.width * this.toggle_pot.node.anchorX;
            this.toggle_pot.node.setPosition(x, y);
        }

        // jackpot text update
        this.jackpot_title_sprite.spriteFrame =
            pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
                ? this.jackpot_title_zh_sprite_frame
                : this.jackpot_title_en_sprite_frame;
    }

    /**
     * 更新 UI 显影状态等
     */
    private _updateUIShowHideStatus(pokerHandData: PokerHandData) {
        this.btn_play.node.active = true;
        this.btn_audit.node.active = true;
        this.btn_collect.node.active = true;

        this.txt_pot_word.node.active = true;
        this.txt_pot.node.active = true;

        this.txt_insurance_word.node.active = true;
        this.txt_insurance.node.active = true;

        this.txt_jackpot_word.node.active = true;
        this.txt_jackpot.node.active = true;

        // 是否有关联的JP(是否显示JP)
        do {
            let bAssociatedJackpot = true;
            switch (this._dataSourceType) {
                case TexasCommonDef.GameReviewDataType.EDST_RECORD:
                    bAssociatedJackpot = this.tGameRecords.tPokerInfoData.tRoomParam.is_associated_jackpot;
                    this._setForceShowBtnVisible(false);
                    this._setSendOutBtnVisible(false);
                    break;

                case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                    bAssociatedJackpot = this._pokerRoom.roomData.pkRoomParam.is_associated_jackpot;
                    this.btn_audit.node.active = this._bShowAudit;
                    break;

                case TexasCommonDef.GameReviewDataType.EDST_COLLECTION:
                    bAssociatedJackpot = this.tCollectPokerMapData.tPokerHandData.bAssociatedJackpot;
                    this._setForceShowBtnVisible(false);
                    this._setSendOutBtnVisible(false);
                    break;

                case TexasCommonDef.GameReviewDataType.EDST_NONE:
                    break;
                default:
                    break;
            }
            this.txt_jackpot_word.node.active = bAssociatedJackpot;
            this.txt_jackpot.node.active = bAssociatedJackpot;

            const fallingMarsWinner =
                pokerHandData &&
                pokerHandData.vPlayerRecords.length > 0 &&
                pokerHandData.vPlayerRecords.filter((record) => {
                    // TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
                    // return record.jackpotType >= AwardPlayerJackpotType.Mars;
                    return record.jackpotType >= 2; // Assuming 2 is the enum value for Mars
                }).length > 0;
            const isFallingMars = bAssociatedJackpot && fallingMarsWinner;
            this.jackpot_holder.active = isFallingMars;

            this.panel_scrollview_widget.top = this.jackpot_holder.active ? 205.8 : 114.8;
            this.panel_scrollview_widget.updateAlignment();
        } while (false);

        if (this._dataSourceType === TexasCommonDef.GameReviewDataType.EDST_COLLECTION) {
            this.btn_audit.node.active = false;
            this.btn_collect.node.active = false;
        }

        // let gameid = this.tGameRecords.tPokerHandData.nGameid;
        // if (gameid === 0) {
        //     gameid = this._pokerRoom.roomData.u32GameID;
        // }

        // if (gameid === 0) {
        //     this.btn_audit.node.active = false;
        //     this._setForceShowBtnVisible(false);
        //     this._setSendOutBtnVisible(false);
        // } else if (gameid === pf.client.GameId.Allin) {
        //     this.btn_audit.node.active = false;
        //     this._setForceShowBtnVisible(false);
        //     this._setSendOutBtnVisible(false);

        //     this.txt_insurance_word.node.active = false;
        //     this.txt_insurance.node.active = false;

        //     this.txt_jackpot_word.node.active = true;
        //     this.txt_jackpot.node.active = true;
        // } else if (this._pokerRoom.roomData.checkGameIsZoom(gameid)) {
        //     this.btn_audit.node.active = false;
        //     this.btn_collect.node.active = false;
        //     this._setSendOutBtnVisible(false); // 急速牌桌无发发看, 所以这里就屏蔽入口

        //     this.txt_jackpot_word.node.active = false;
        //     this.txt_jackpot.node.active = false;
        // }

        // 更新"强制亮牌/发发看"等按钮等位置
        this._layoutForceShowAndSendOutBtn();

        // 更新"举报/播放/收藏"等按钮等位置
        do {
            let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
            let txt_audit: cc.Label = panel_bottom_top.getChildByName('txt_audit').getComponent(cc.Label);
            txt_audit.node.active = this.btn_audit.node.active;
            let txt_play: cc.Label = panel_bottom_top.getChildByName('txt_play').getComponent(cc.Label);
            txt_play.node.active = this.btn_play.node.active;
            let txt_collect: cc.Label = panel_bottom_top.getChildByName('txt_collect').getComponent(cc.Label);
            txt_collect.node.active = this.btn_collect.node.active;

            // 从右往左排版
            let offset_x_sides = 20;
            let offset_x_nextto = 0;
            let offset_x_middle = 42;

            let start_x: number =
                panel_bottom_top.x + panel_bottom_top.width * panel_bottom_top.scaleX * panel_bottom_top.anchorX;
            let pos_x: number = start_x - offset_x_sides;
            let pos_y = 0;

            // 收藏
            if (this.btn_collect.node.active) {
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_collect).width *
                    txt_collect.node.scaleX *
                    (1 - txt_collect.node.anchorX);
                txt_collect.node.setPosition(pos_x, pos_y);
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_collect).width *
                    txt_collect.node.scaleX *
                    txt_collect.node.anchorX;
                pos_x -= offset_x_nextto;
                pos_x -=
                    this.btn_collect.node.width * this.btn_collect.node.scaleX * (1 - this.btn_collect.node.anchorX);
                this.btn_collect.node.setPosition(pos_x, pos_y);
                pos_x -= this.btn_collect.node.width * this.btn_collect.node.scaleX * this.btn_collect.node.anchorX;
                pos_x -= offset_x_middle;
            }

            // 播放
            if (this.btn_play.node.active) {
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_play).width *
                    txt_play.node.scaleX *
                    (1 - txt_play.node.anchorX);
                txt_play.node.setPosition(pos_x, pos_y);
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_play).width * txt_play.node.scaleX * txt_play.node.anchorX;
                pos_x -= offset_x_nextto;
                pos_x -= this.btn_play.node.width * this.btn_play.node.scaleX * (1 - this.btn_play.node.anchorX);
                this.btn_play.node.setPosition(pos_x, pos_y);
                pos_x -= this.btn_play.node.width * this.btn_play.node.scaleX * this.btn_play.node.anchorX;
                pos_x -= offset_x_middle;
            }

            // 举报
            if (this.btn_audit.node.active) {
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_audit).width *
                    txt_audit.node.scaleX *
                    (1 - txt_audit.node.anchorX);
                txt_audit.node.setPosition(pos_x, pos_y);
                pos_x -=
                    common.UIUtil.updateAndMeasureLabel(txt_audit).width *
                    txt_audit.node.scaleX *
                    txt_audit.node.anchorX;
                pos_x -= offset_x_nextto;
                pos_x -= this.btn_audit.node.width * this.btn_audit.node.scaleX * (1 - this.btn_audit.node.anchorX);
                this.btn_audit.node.setPosition(pos_x, pos_y);
                pos_x -= this.btn_audit.node.width * this.btn_audit.node.scaleX * this.btn_audit.node.anchorX;
                pos_x -= offset_x_middle;
            }
        } while (false);
    }

    /**
     * 更新 强制亮牌 按钮 显隐状态
     * @param data
     */
    private _updateForceShowState(data: PokerHandData): void {
        let ret = false;
        let isSpectator = false;
        if (
            this._vGameUUID.length > 0 && // 有牌谱数据
            data.bForceShowcard // 该手牌局是否开启"强制亮牌"功能
        ) {
            let playRet = false; // 自己是参与者
            let needToShowRet = false; // 需要亮牌
            const handCardsMaxLen: number = data.nGameid === pf.client.GameId.PLO ? 4 : 2; // 手牌上限数量

            for (const playerRecord of data.vPlayerRecords) {
                // 自己是参与者
                if (playerRecord.playerid === this._authService.currentUser.userId) {
                    playRet = true;
                }
                // 只要存在一个玩家不全亮牌都可以进行强制亮牌
                else if (!needToShowRet && playerRecord.cards.length < handCardsMaxLen) {
                    needToShowRet = playerRecord.lastRoundType > 0;
                }

                if (playRet && needToShowRet) break;
            }

            isSpectator = this._getIsSpectatorRevealEnabled() && !playRet;
            ret = (isSpectator || playRet) && needToShowRet;
        }
        this._setForceShowBtnVisible(ret, isSpectator);
    }

    private _getIsSpectatorRevealEnabled(): boolean {
        return false; // cv.roomManager.getIsSpectatorRevealEnabled()
    }

    /**
     * 更新 发发看 按钮 显影状态
     * @param data
     */
    private _updateSendOutState(data: PokerHandData): void {
        let ret = false;

        // 当前手不是牌桌正在结算的那一手),,老数据nReviewSendOutLen为空
        if (this._vGameUUID.length > 0) {
            // 当前手不是牌桌正在结算的那一手

            // 兼容牌局中历史回顾老数据
            // 直接读取 data 服原始 json 字段去判断是否为老版本数据
            // unsend_public_cards 为空2种情况(老数据;未发牌为0;都满足不显示的条件)
            let bOldData = false;
            let gameHandMapData: pf.data.PokerHandData = this._handMapCache.getHand(
                this.tGameRecords.tPokerHandData.sGameUUID
            );

            if (gameHandMapData) {
                let unSendPublicCards: any = gameHandMapData.gameRecord.unSendPublicCards;
                if (unSendPublicCards === null || typeof unSendPublicCards === 'undefined') {
                    bOldData = true;
                }
            }

            if (!bOldData) {
                let totalHand: number = this._vGameUUID.length;
                let curHand: number = this._nCurGameUUIDIndex + 1;
                let recentHandCount: number = this._sGameSettlementUUID !== '' ? 4 : 3; // 最近3手(若当前局正在结算则是4手)
                let roundRet: boolean = curHand > totalHand - recentHandCount && curHand <= totalHand; // 最近有效局

                if (roundRet) {
                    for (const playerRecord of data.vPlayerRecords) {
                        if (playerRecord.playerid === this._authService.currentUser.userId) {
                            // 自己是参与者
                            ret = playerRecord.reviewSendOutLen < 5; // 发发看长度 < 5
                            ret = ret && playerRecord.lastRoundType > 0; // 玩家坚持到哪一阶段(确保不是老数据)
                            break;
                        }
                    }
                }
            }
        }

        this._setSendOutBtnVisible(ret);
    }

    /**
     * 更新"强制亮牌/发发看"等按钮等位置
     */
    private _layoutForceShowAndSendOutBtn(): void {
        let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');

        // 从左往右排版
        let offset_x_sides = 30;
        let offset_x_nextto = 30;

        let start_x: number =
            panel_bottom_top.x + panel_bottom_top.width * panel_bottom_top.scaleX * -panel_bottom_top.anchorX;
        let pos_x: number = start_x + offset_x_sides;

        // 强制亮牌
        if (this.btn_forceshow.node.active) {
            pos_x += this.btn_forceshow.node.width * this.btn_forceshow.node.scaleX * this.btn_forceshow.node.anchorX;
            this.btn_forceshow.node.setPosition(pos_x, this.btn_forceshow.node.y);

            let ctx_forceshow: cc.Node = panel_bottom_top.getChildByName('ctx_forceshow');
            ctx_forceshow.setPosition(pos_x, ctx_forceshow.y);

            pos_x +=
                this.btn_forceshow.node.width * this.btn_forceshow.node.scaleX * (1 - this.btn_forceshow.node.anchorX);
            pos_x += offset_x_nextto;
        }

        // 发发看
        if (this.btn_sendout.node.active) {
            pos_x += this.btn_sendout.node.width * this.btn_sendout.node.scaleX * this.btn_sendout.node.anchorX;
            this.btn_sendout.node.setPosition(pos_x, this.btn_sendout.node.y);

            let ctx_sendout: cc.Node = panel_bottom_top.getChildByName('ctx_sendout');
            ctx_sendout.setPosition(pos_x, ctx_sendout.y);

            pos_x += this.btn_sendout.node.width * this.btn_sendout.node.scaleX * (1 - this.btn_sendout.node.anchorX);
            pos_x += offset_x_nextto;
        }
    }

    /**
     * 更新"强制亮牌"所需金额
     */
    private _updateForceShowCoin(isSpectator: boolean): void {
        if (!this.btn_forceshow.node.active) return;

        let nCount: number = isSpectator
            ? this._pokerRoom.roomData.pkPayMoneyItem.spectatorShowCardCount + 1
            : this._pokerRoom.roomData.pkPayMoneyItem.showCardCount + 1;
        let vFee: FeeItem[] = isSpectator
            ? this._pokerRoom.roomData.pkPayMoneyItem.showCardCountsSpectatorFee
            : this._pokerRoom.roomData.pkPayMoneyItem.showCardCountsFee;

        let nPrice = 0;
        let bLayout = false;
        for (let i = 0; i < pf.DataUtil.getArrayLength(vFee); ++i) {
            if (nCount >= vFee[i].startCount && nCount <= vFee[i].endCount) {
                nPrice = this.isUSDTable() ? vFee[i].needUsd : vFee[i].needCoin;
                break;
            }
        }

        // 延时价格
        let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
        let ctx_forceshow: cc.Node = panel_bottom_top.getChildByName('ctx_forceshow');
        let txt_forceshow_coin: cc.Label = ctx_forceshow.getChildByName('txt_coin').getComponent(cc.Label);
        if (this.isUSDTable())
            ctx_forceshow.getChildByName('img_coin').getComponent(cc.Sprite).spriteFrame = this.icon_usd;
        bLayout = nPrice !== pf.TypeUtil.toSafeNumber(txt_forceshow_coin.string);
        txt_forceshow_coin.string = common.CurrencyUtil.convertServerAmountToDisplayString(nPrice);

        // 排版
        if (bLayout) {
            let txt_size: cc.Size = common.UIUtil.updateAndMeasureLabel(txt_forceshow_coin);
            let img_forceshow_coin: cc.Sprite = ctx_forceshow.getChildByName('img_coin').getComponent(cc.Sprite);
            let start_x: number = ctx_forceshow.width * ctx_forceshow.scaleX * -ctx_forceshow.anchorX;
            let pos_x: number = start_x;
            let offset_x_sides = 0;
            let offset_x_middle = 15;

            let left_w: number =
                ctx_forceshow.width -
                img_forceshow_coin.node.width * img_forceshow_coin.node.scaleX -
                txt_size.width * txt_forceshow_coin.node.scaleX;
            offset_x_middle = Math.min(offset_x_middle, left_w / 3);
            offset_x_sides = (left_w - offset_x_middle) / 2;

            pos_x +=
                offset_x_sides +
                img_forceshow_coin.node.width * img_forceshow_coin.node.scaleX * img_forceshow_coin.node.anchorX;
            img_forceshow_coin.node.setPosition(pos_x, img_forceshow_coin.node.y);
            pos_x +=
                img_forceshow_coin.node.width * img_forceshow_coin.node.scaleX * (1 - img_forceshow_coin.node.anchorX);

            pos_x += offset_x_middle + txt_size.width * txt_forceshow_coin.node.anchorX;
            txt_forceshow_coin.node.setPosition(pos_x, txt_forceshow_coin.node.y);
        }
    }

    /**
     * 更新"发发看"所需金额
     */
    private _updateSendOutCoin(): void {
        if (!this.btn_sendout.node.active) return;

        let nReviewSendOutLen = 0;
        let vFee: FeeItem[] = this._pokerRoom.roomData.pkPayMoneyItem.showLeftCardFee;
        let vRecord: PlayerRecord[] = this.tGameRecords.tPokerHandData.vPlayerRecords;
        for (const record of vRecord) {
            if (record.playerid === this._authService.currentUser.userId) {
                nReviewSendOutLen = record.reviewSendOutLen;
                break;
            }
        }

        let nIdx = 0;
        switch (nReviewSendOutLen) {
            case 0:
                nIdx = 0;
                break;
            case 3:
                nIdx = 1;
                break;
            case 4:
                nIdx = 2;
                break;
            case 5:
                nIdx = 3;
                break;
            default:
                break;
        }

        let nPrice = 0;
        let bLayout = false;
        if (nIdx >= 0 && nIdx < vFee.length) {
            nPrice = this.isUSDTable() ? vFee[nIdx].needUsd : vFee[nIdx].needCoin;
        }

        // 延时价格
        let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
        let ctx_sendout: cc.Node = panel_bottom_top.getChildByName('ctx_sendout');
        let txt_sendout_coin: cc.Label = ctx_sendout.getChildByName('txt_coin').getComponent(cc.Label);
        if (this.isUSDTable())
            ctx_sendout.getChildByName('img_coin').getComponent(cc.Sprite).spriteFrame = this.icon_usd;
        bLayout = nPrice !== pf.TypeUtil.toSafeNumber(txt_sendout_coin.string);
        txt_sendout_coin.string = common.CurrencyUtil.convertServerAmountToDisplayString(nPrice);

        // 排版
        if (bLayout) {
            let txt_size: cc.Size = common.UIUtil.updateAndMeasureLabel(txt_sendout_coin);
            let img_sendout_coin: cc.Sprite = ctx_sendout.getChildByName('img_coin').getComponent(cc.Sprite);
            let start_x: number = ctx_sendout.width * ctx_sendout.scaleX * -ctx_sendout.anchorX;
            let pos_x: number = start_x;
            let offset_x_sides = 0;
            let offset_x_middle = 15;

            let left_w: number =
                ctx_sendout.width -
                img_sendout_coin.node.width * img_sendout_coin.node.scaleX -
                txt_size.width * txt_sendout_coin.node.scaleX;
            offset_x_middle = Math.min(offset_x_middle, left_w / 3);
            offset_x_sides = (left_w - offset_x_middle) / 2;

            pos_x +=
                offset_x_sides +
                img_sendout_coin.node.width * img_sendout_coin.node.scaleX * img_sendout_coin.node.anchorX;
            img_sendout_coin.node.setPosition(pos_x, img_sendout_coin.node.y);
            pos_x += img_sendout_coin.node.width * img_sendout_coin.node.scaleX * (1 - img_sendout_coin.node.anchorX);

            pos_x += offset_x_middle + txt_size.width * txt_sendout_coin.node.anchorX;
            txt_sendout_coin.node.setPosition(pos_x, txt_sendout_coin.node.y);
        }
    }

    /**
     * 设置滑条进度
     * @param progress
     */
    private _setSliderProgress(progress: number): void {
        this.slider.progress = progress;
        this.slider_sp.node.width = pf.MathUtil.times(this.slider.progress, this.slider.node.width);
    }

    /**
     * 更新滑条视图
     */
    private _updateSliderPercent(skipUpdateHand?: boolean): void {
        this._nLastSliderProgress = this.slider.progress;

        // 牌谱数组当前索引
        let nCurIndex = 0;
        if (this._vGameUUID.length > 1) {
            nCurIndex = Math.round(pf.MathUtil.div(this.slider.progress, this._nSliderPerRatio));
        }

        // 刷新翻页后的数据
        if (this._nCurGameUUIDIndex !== nCurIndex) {
            this._nCurGameUUIDIndex = nCurIndex;
            this._nLastSaveGameUUIDIndex = nCurIndex;
            if (skipUpdateHand) {
                return;
            }
            this._onMsgUpdateDataHand();
        } else if (this._squidHandType === SquidHandType.Squid_Final_Hand) {
            this._onMsgUpdateDataHand();
        }
    }

    /**
     * 更新视图 - 数据显示
     * @param pokerHandData
     */
    private _updateDataView(pokerHandData: PokerHandData): void {
        // 显示页数
        let nCurPage = this._vGameUUID.length > 0 ? this._nCurGameUUIDIndex + 1 : 0;
        this.txt_page.string = pf.StringUtil.formatC('%d/%d', nCurPage, this._vGameUUID.length);

        this._updateForceShowState(pokerHandData);
        this._updateSendOutState(pokerHandData);
        this._updateUIShowHideStatus(pokerHandData);

        this.txt_time.string = '';
        this.txt_time.node.active = false;

        this.txt_serial.string = '';
        this.txt_serial.node.active = false;

        // 时间/编号
        if (pokerHandData) {
            // 时间(格式: y-M-D H:M)
            let format_time: string = pf.TimeUtil.formatTime(
                pokerHandData.nCreateTime,
                pf.eTimeType.Year_Month_Day_Hour_Min
            );
            this.txt_time.string = format_time;
            this.txt_time.node.active = this.txt_time.string.length > 0;

            // 编号
            let format_key: string = pf.languageManager.getString('allReview_allReview_panel_serial_number_text');
            this.txt_serial.string = pf.StringUtil.formatC(format_key, pokerHandData.sGameUUID);
            this.txt_serial.node.active = pokerHandData.sGameUUID.length > 0;
            this.currentHandData = pokerHandData;
            // don't show featureHandBtn by AT-7163
            // this.shouldShowFeatureHandBtn(pokerHandData.nGameid, pokerHandData.nGameMode);
            this.btn_FeatureHand.node.active = false;
        } else this.btn_FeatureHand.node.active = false;

        // Commented this out because seems a bit unnecessary and all this can be done with a layout too.
        // 适配"时间/编号"文本排版(从右至左排版)
        // do {
        //     let fontSize: number = 30;
        //     let offset_side: number = 20;
        //     let offset_middle: number = 25;

        //     let panel: cc.Node = this.txt_serial.node.parent;
        //     let criticalWidth: number = (0 - panel.anchorX) * panel.width;

        //     // 适用于: 横向任意锚点, 纵向锚点"0.5"
        //     let x: number = 0;

        //     do {
        //         x = (1 - panel.anchorX) * panel.width;
        //         x -= offset_side;

        //         if (this.txt_time.node.active) {
        //             this.txt_time.fontSize = fontSize;
        //             let txt_time_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_time).width;
        //             x -= txt_time_w * (1 - this.txt_time.node.anchorX) * this.txt_time.node.scaleX;
        //             this.txt_time.node.setPosition(x, this.txt_time.node.y);
        //             x -= txt_time_w * this.txt_time.node.anchorX * this.txt_time.node.scaleX;
        //             if (txt_time_w > 0) x -= offset_middle;
        //         }

        //         if (this.txt_serial.node.active) {
        //             this.txt_serial.fontSize = fontSize;
        //             let txt_serial_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_serial).width;
        //             x -= txt_serial_w * (1 - this.txt_serial.node.anchorX) * this.txt_serial.node.scaleX;
        //             this.txt_serial.node.setPosition(x, this.txt_serial.node.y);
        //             x -= txt_serial_w * this.txt_serial.node.anchorX * this.txt_serial.node.scaleX;
        //         }

        //         x -= offset_side;
        //         --fontSize;
        //     } while (x < criticalWidth);
        // } while (false);

        // 底池
        if (this.txt_pot.node.active) {
            let value = 0;
            if (pokerHandData) {
                value = common.CurrencyUtil.convertToClientAmount(pokerHandData.nTotalPot);
            }
            this.txt_pot.string = common.CurrencyUtil.applyDisplayRatioToFormattedString(value);
        }

        // 保险
        if (this.txt_insurance.node.active) {
            let value = 0;
            if (pokerHandData) {
                value = common.CurrencyUtil.convertServerAmountToDisplayNumber(pokerHandData.nInsuranceWinbet);
            }
            this.txt_insurance.string = common.CurrencyUtil.getSignedString(value);
            this.txt_insurance.node.color = common.CurrencyUtil.getSignColor(value);
        }

        // jackpot
        if (this.txt_jackpot.node.active) {
            let value = 0;
            if (pokerHandData) {
                value = common.CurrencyUtil.convertServerAmountToDisplayNumber(pokerHandData.nJackpotWinbet);
            }
            this.txt_jackpot.string = common.CurrencyUtil.getSignedString(value);
            this.txt_jackpot.node.color = common.CurrencyUtil.getSignColor(value);
        }

        if (this.jackpot_holder.active) {
            let value = 0;
            if (pokerHandData) {
                value = common.CurrencyUtil.convertServerAmountToDisplayNumber(pokerHandData.nJackpotTotalWinbet);
            }
            this.txt_total_jackpot.string = value.toString();
        }

        // 适配"底池, 保险, JP"文本排版(从左至右排版)
        do {
            let fontSize: number = this._nTopPanelFontSize + 1;
            let panel: cc.Node = this.panel_top;

            let offsetMin = 10; // 最小均分值
            let offsetMiddle = 0; // 最终均分值
            let offsetNextto = 10; // 比邻间距

            // 计算均分值(适用于: 横向任意锚点, 纵向锚点"0.5")
            do {
                --fontSize;
                let w = 0;
                let count = 0;

                if (this.txt_pot.node.active) {
                    ++count;

                    this.txt_pot_word.fontSize = fontSize;
                    w += common.UIUtil.updateAndMeasureLabel(this.txt_pot_word).width * this.txt_pot_word.node.scaleX;
                    w += offsetNextto;

                    this.txt_pot.fontSize = fontSize;
                    w += common.UIUtil.updateAndMeasureLabel(this.txt_pot).width * this.txt_pot.node.scaleX;
                }

                if (this.txt_insurance.node.active) {
                    ++count;

                    this.txt_insurance_word.fontSize = fontSize;
                    w +=
                        common.UIUtil.updateAndMeasureLabel(this.txt_insurance_word).width *
                        this.txt_insurance_word.node.scaleX;
                    w += offsetNextto;

                    this.txt_insurance.fontSize = fontSize;
                    w += common.UIUtil.updateAndMeasureLabel(this.txt_insurance).width * this.txt_insurance.node.scaleX;
                }

                if (this.txt_jackpot.node.active) {
                    ++count;

                    this.txt_jackpot_word.fontSize = fontSize;
                    w +=
                        common.UIUtil.updateAndMeasureLabel(this.txt_jackpot_word).width *
                        this.txt_jackpot_word.node.scaleX;
                    w += offsetNextto;

                    this.txt_jackpot.fontSize = fontSize;
                    w += common.UIUtil.updateAndMeasureLabel(this.txt_jackpot).width * this.txt_jackpot.node.scaleX;
                }

                offsetMiddle = (panel.width - w) / (count + 1);
            } while (offsetMiddle < offsetMin);

            // 设置位置
            let x = 0;
            let y: number = (0.5 - panel.anchorY) * panel.height;
            x = (0 - panel.anchorX) * panel.width;
            x += offsetMiddle;
            if (this.txt_pot.node.active) {
                let txt_pot_word_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_pot_word).width;
                x += txt_pot_word_w * this.txt_pot_word.node.anchorX * this.txt_pot_word.node.scaleX;
                this.txt_pot_word.node.setPosition(x, y);
                x += txt_pot_word_w * (1 - this.txt_pot_word.node.anchorX) * this.txt_pot_word.node.scaleX;

                let txt_pot_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_pot).width;
                x += offsetNextto;
                x += txt_pot_w * this.txt_pot.node.anchorX * this.txt_pot.node.scaleX;
                this.txt_pot.node.setPosition(x, y);
                x += txt_pot_w * (1 - this.txt_pot.node.anchorX) * this.txt_pot.node.scaleX;
                x += offsetMiddle;
            }

            if (this.txt_insurance.node.active) {
                let txt_insurance_word_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_insurance_word).width;
                x += txt_insurance_word_w * this.txt_insurance_word.node.anchorX * this.txt_insurance_word.node.scaleX;
                this.txt_insurance_word.node.setPosition(x, y);
                x +=
                    txt_insurance_word_w *
                    (1 - this.txt_insurance_word.node.anchorX) *
                    this.txt_insurance_word.node.scaleX;

                let txt_insurance_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_insurance).width;
                x += offsetNextto;
                x += txt_insurance_w * this.txt_insurance.node.anchorX * this.txt_insurance.node.scaleX;
                this.txt_insurance.node.setPosition(x, y);
                x += txt_insurance_w * (1 - this.txt_insurance.node.anchorX) * this.txt_insurance.node.scaleX;
                x += offsetMiddle;
            }

            if (this.txt_jackpot.node.active) {
                let txt_jackpot_word_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_jackpot_word).width;
                x += txt_jackpot_word_w * this.txt_jackpot_word.node.anchorX * this.txt_jackpot_word.node.scaleX;
                this.txt_jackpot_word.node.setPosition(x, y);
                x += txt_jackpot_word_w * (1 - this.txt_jackpot_word.node.anchorX) * this.txt_jackpot_word.node.scaleX;

                let txt_jackpot_w: number = common.UIUtil.updateAndMeasureLabel(this.txt_jackpot).width;
                x += offsetNextto;
                x += txt_jackpot_w * this.txt_jackpot.node.anchorX * this.txt_jackpot.node.scaleX;
                this.txt_jackpot.node.setPosition(x, y);
                x += txt_jackpot_w * (1 - this.txt_jackpot.node.anchorX) * this.txt_jackpot.node.scaleX;
            }
        } while (false);

        // 更新子项列表视图
        this._updateTableView(pokerHandData);

        // 检测是否发送"更新播放牌局回顾"消息
        this._bHasReplayData = false;
        if (pokerHandData && pokerHandData.objReplay) {
            this._bHasReplayData = true;
        }

        let param: { uuid: string; hasReplayData: boolean } = {
            uuid: this._sCurGameUUID,
            hasReplayData: this._bHasReplayData
        };
        this._gameReviewService.emit('update_replay_data', param);
    }

    /**
     * 更新视图 - 子项列表
     * @param pokerHandData
     */
    private _updateTableView(pokerData: PokerHandData): void {
        // mock gameReview data list, can be cancel
        //  pokerHandData = this._mockDataSet.getGameReviewData();
        // 填充数据
        const pokerHandData = new PokerHandData();
        pf.DataUtil.deepCopy(pokerData, pokerHandData);
        // 填充数据
        if (pokerHandData) {
            const nCount: number = pokerHandData.vPlayerRecords.length;

            const marsPlayers = pokerHandData.vPlayerRecords.filter(
                // TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
                // (player) => player.jackpotType === AwardPlayerJackpotType.Mars
                (player) => player.jackpotType === 2
            );

            const others = pokerHandData.vPlayerRecords.filter(
                // TODO Cannot use AwardPlayerJackpotType because jackpot bundle is not loaded yet
                // (player) => player.jackpotType !== AwardPlayerJackpotType.Mars
                (player) => player.jackpotType !== 2
            );

            pokerHandData.vPlayerRecords = marsPlayers.concat(others);

            if (this._last_game_id !== pokerHandData.nGameid) {
                const lastItemPool =
                    this._last_game_id === pf.client.GameId.PLO
                        ? this.scroll_view_item_pool_plo
                        : this.scroll_view_item_pool;
                lastItemPool.clear();
            }
            const itemPool =
                pokerHandData.nGameid === pf.client.GameId.PLO
                    ? this.scroll_view_item_pool_plo
                    : this.scroll_view_item_pool;
            itemPool.init({ itemCount: nCount });
            const items = itemPool.getItems();
            // this.showSquidReviewSettlement();
            // if(pokerHandData.nGameid === pf.client.GameId.Squid){
            //     if(pokerHandData.gameRecord.records[0]?.squidWinLoseAmount){
            //         pokerHandData.gameRecord.records.sort(this.sortListAssendingOrder.bind(this));// sort all data
            //         pokerHandData.gameRecord.records.sort(this.sortBySeatStatus.bind(this));// Sorting player for "Waiting" && "Away" status
            //         this._squidHandType = this.getSquidHandType();
            //         if (this._squidHandType === SquidHandType.Squid_Settlement_Hand) {
            //             this.showSquidReviewSettlement(true);
            //             this.squidSettlementReview.show(pokerHandData,nCount,this);
            //         }
            //         else
            //         {
            //             this._squidHandType = SquidHandType.Squid_Final_Hand;
            //         }

            //     }
            //     else{
            //         this._squidHandType = SquidHandType.Normal_Hand;
            //         pokerHandData.gameRecord.records.sort(this.sortBySeatStatus.bind(this));// Sorting player for "Waiting" && "Away" status
            //     }
            // }

            for (let i = 0; i < nCount; i++) {
                let tData: GameReviewItemData = new GameReviewItemData();
                tData.nGameID = pokerHandData.nGameid;
                tData.sGameUUID = pokerHandData.sGameUUID;
                tData.nGameMode = pokerHandData.nGameMode;
                tData.objReplay = pokerHandData.objReplay;
                tData.nShortFull = pokerHandData.nShortFull;
                tData.vPubsCards = pokerHandData.vPublicCards;
                tData.vUnsendPublicCards = pokerHandData.vUnsendPublicCards;

                tData.nPlayerID = pokerHandData.vPlayerRecords[i].playerid;
                tData.sPlayerName = pokerHandData.vPlayerRecords[i].playerName;
                tData.sPlayerHead = pokerHandData.vPlayerRecords[i].playerHead;
                tData.plat = pokerHandData.vPlayerRecords[i].plat;
                tData.nWinBet = pokerHandData.vPlayerRecords[i].winBet;
                tData.nInsuranceBet = pokerHandData.vPlayerRecords[i].insuranceBet;
                tData.nInsuranceAmount = pokerHandData.vPlayerRecords[i].insuranceAmount;
                tData.nPlayerBettingRoundBet = pokerHandData.vPlayerRecords[i].playerBettingRoundBet;
                tData.bMuck = pokerHandData.vPlayerRecords[i].bMuck;
                tData.bActiveShow = pokerHandData.vPlayerRecords[i].bActiveShow;
                tData.bForceShowDown = pokerHandData.vPlayerRecords[i].bForceShowDown;
                tData.nLastRoundType = pokerHandData.vPlayerRecords[i].lastRoundType;
                tData.vHandCards = pokerHandData.vPlayerRecords[i].cards;
                tData.seatNo = pokerHandData.vPlayerRecords[i].seatNo;
                tData.seatInfo = pokerHandData.vPlayerRecords[i].seatInfo;
                tData.bFold = pokerHandData.vPlayerRecords[i].bFold;
                tData.nReviewSendOutLen = pokerHandData.vPlayerRecords[i].reviewSendOutLen;
                tData.nForceShowedActLen = pokerHandData.vPlayerRecords[i].forceShowedActLen;
                tData.nReviewSendOutActLen = pokerHandData.vPlayerRecords[i].reviewSendOutActLen;
                pokerHandData.vPlayerRecords[i].forceShowedActLen = 0;
                pokerHandData.vPlayerRecords[i].reviewSendOutActLen = 0;
                tData.squidCount = pokerHandData.vPlayerRecords[i].squidCount;
                tData.squidWinLoseAmount = pokerHandData.vPlayerRecords[i].squidWinLoseAmount;
                tData.superSquidCount = pokerHandData.vPlayerRecords[i].superSquidCount;
                tData.jackpotType = pokerHandData.vPlayerRecords[i].jackpotType;
                tData.seatStatus = pokerHandData.vPlayerRecords[i].seatStatus;
                tData.squidMultiplier = pokerHandData.vPlayerRecords[i].squidMultiplier;
                tData.currentHandWinnerNoOfNormalSquids =
                    pokerHandData.vPlayerRecords[i].currentHandWinnerNoOfNormalSquids;
                // 添加"JP"详情数据
                if (this.txt_jackpot.node.active) {
                    tData.nJackWinbet = pokerHandData.vPlayerRecords[i].nJackWinbet;
                }

                const gameReviewItem: GameReviewItemComp = items[i].getComponent(GameReviewItemComp);
                gameReviewItem.updateSVReuseData(tData, nCount);
            }

            this._last_game_id = pokerHandData.nGameid;
        } else {
            // 清空itemPool以便更新试图
            const lastItemPool =
                this._last_game_id === pf.client.GameId.PLO
                    ? this.scroll_view_item_pool_plo
                    : this.scroll_view_item_pool;
            lastItemPool.clear();
        }
    }

    /**
     * 设置"强制亮牌"显隐
     * @param visible
     */
    private _setForceShowBtnVisible(visible: boolean, isSpectator: boolean = false): void {
        let layout: boolean = this.btn_forceshow.node.active !== visible;
        this.btn_forceshow.node.active = visible;

        let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
        let ctx_forceshow: cc.Node = panel_bottom_top.getChildByName('ctx_forceshow');
        ctx_forceshow.active = this.btn_forceshow.node.active;

        if (layout) {
            this._layoutForceShowAndSendOutBtn();
        }

        this._updateForceShowCoin(isSpectator);
    }

    /**
     * 设置"发发看"显隐
     * @param visible
     */
    private _setSendOutBtnVisible(visible: boolean): void {
        let layout: boolean = this.btn_sendout.node.active !== visible;
        this.btn_sendout.node.active = visible;

        let panel_bottom_top: cc.Node = this.panel_bottom.getChildByName('panel_top');
        let ctx_sendout: cc.Node = panel_bottom_top.getChildByName('ctx_sendout');
        ctx_sendout.active = this.btn_sendout.node.active;

        if (layout) {
            this._layoutForceShowAndSendOutBtn();
        }

        this._updateSendOutCoin();
    }

    /**
     * 刷新(拉取)数据
     */
    private async _onMsgUpdateDataHand(): Promise<void> {
        if (this._vGameUUID.length <= 0) {
            this._updateDataView(null);
        } else {
            // 获取牌局uuid
            let nIndex: number = this._nCurGameUUIDIndex;
            if (nIndex < 0 || nIndex >= this._vGameUUID.length) return;
            this._sCurGameUUID = this._vGameUUID[nIndex];

            // 请求牌局数据
            let uid: number = this._authService.currentUser.userId;
            switch (this._dataSourceType) {
                case TexasCommonDef.GameReviewDataType.EDST_NONE:
                    break;

                case TexasCommonDef.GameReviewDataType.EDST_RECORD:
                case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                    {
                        // 游戏中即时写入牌谱时 读缓存会造成玩家秀牌 无法刷新的bug, 所以这里采用及时请求
                        // const value: string = this._handMapCache?.getHand(this._sCurGameUUID);
                        // if (value) {
                        //     cv.httpHandler._onGameHand(value);
                        // } else {
                        //     let gameid: number = this.tGameRecords.gameID;
                        //     if (gameid === 0) {
                        //         gameid = this._pokerRoom.roomData.u32GameID;
                        //     }
                        //     const onCallback: Function = (gameUUID: string, value: any) => {
                        //         this._handMapCache?.addHand(gameUUID, value);

                        //          // this commented line is for next release use
                        //         // if( value.gameid === pf.client.GameId.Squid && value?.squidHuntGameSettlement?.squid_game_uuids_js?.length>0)
                        //         //     this._updateGameuuidsSquidFinalSettelment(value?.squidHuntGameSettlement?.squid_game_uuids_js);
                        //     }
                        //     cv.httpHandler.requestGameHand(uid, this._sCurGameUUID, gameid, onCallback);
                        // }
                        // this.getGameHandData(this._sCurGameUUID, this._pokerRoom.roomData.u32GameID);

                        const handData = await this._dataService.getGameHand(
                            this._sCurGameUUID,
                            this._pokerRoom.roomData.u32GameID
                        );

                        this.tGameRecords.tPokerHandData = handData;
                        this._handMapCache?.addHand(this._sCurGameUUID, handData);
                        const err_code: number = pf.TypeUtil.toSafeNumber(handData.errCode);
                        const sGameUUID: string = pf.TypeUtil.toSafeString(handData.sGameUUID);
                        this.tGameRecords.mHandMapCache.set(sGameUUID, handData);

                        this._onMsgUpdateHandMap(err_code);
                    }
                    break;

                case TexasCommonDef.GameReviewDataType.EDST_COLLECTION:
                    if (this.tCollectPokerMapData.mHandMapCache.has(this._sCurGameUUID)) {
                        let value: string = this.tCollectPokerMapData.mHandMapCache.get(this._sCurGameUUID);
                        // Process the cached favorite hand data
                        this._processFavoriteHandData(value);
                    } else {
                        // Request favorite hand data using the new service
                        this._dataService.getGameFavoriteHand(uid, this._sCurGameUUID);
                    }
                    break;

                default:
                    break;
            }
        }
    }

    /**
     * 请求"gameuuid"数据 回调
     * @param err_code 错误码(非0表示无效数据)
     */
    private _onMsgUpdateHandMap(err_code: number): void {
        let tPokerHandData: PokerHandData = null;
        switch (this._dataSourceType) {
            case TexasCommonDef.GameReviewDataType.EDST_RECORD:
            case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                tPokerHandData = this.tGameRecords.tPokerHandData;
                break;

            case TexasCommonDef.GameReviewDataType.EDST_COLLECTION:
                tPokerHandData = this.tCollectPokerMapData.tPokerHandData;
                break;

            case TexasCommonDef.GameReviewDataType.EDST_NONE:
                break;
            default:
                break;
        }
        if (!tPokerHandData) tPokerHandData = new PokerHandData();
        this.currentHandData = tPokerHandData;
        this._updateDataView(tPokerHandData);

        // 标记该手牌局数据真实有效性
        let is_valid_data: boolean = err_code === 0;
        this.txt_invalid_data_desc.node.active = !is_valid_data;

        if (!is_valid_data) {
            let key: string = pf.languageManager.getString('game_review_favor_detail_invalid_data_txt');
            this.txt_invalid_data_desc.string = `${key}[${err_code}]`;
        }
    }

    /**
     * 请求大pot的"gameuuid"数据
     * @param transGameUUIDIndex 跳转第几手牌局索引(默认缺省, 这里主要是传递参数至回调函数里)
     */
    private _requestBigPotGameUUIDs(transGameUUIDIndex: number = -1): void {
        let gameid = 0;
        let roomUuidJs = '';
        let isReq = true;

        switch (this._dataSourceType) {
            case TexasCommonDef.GameReviewDataType.EDST_RECORD:
                gameid = this.tGameRecords.gameID;
                roomUuidJs = this.tGameRecords.tPokerInfoData.sRoomUUID;
                break;

            case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                gameid = this._pokerRoom.roomData.u32GameID;
                roomUuidJs = this._pokerRoom.roomData.roomUuidJs;
                break;

            default:
                isReq = false;
                break;
        }

        if (isReq) {
            // gameRecord optimization todo:
            //   cache roomUuidJs, If current roomUuidJs is diff from the last one, then it needs to request new data
            //   Else, just use the cached one
            if (this._bigPot_uuids_js && this._dataSourceType === TexasCommonDef.GameReviewDataType.EDST_GAMEROOM) {
                this._onMsgBigPotGameUUIDs(transGameUUIDIndex, this._bigPot_uuids_js);
            } else {
                this._dataService.getBigPotGameUUIDs(roomUuidJs, gameid).then((value: any) => {
                    this._onMsgBigPotGameUUIDs(transGameUUIDIndex, value);
                });
            }
        }
    }

    /**
     * 请求大pot的"gameuuid"数据 - 回调
     * @param transGameUUIDIndex
     * @param value
     */
    private _bigPot_uuids_js: any = null;
    private _onMsgBigPotGameUUIDs(transGameUUIDIndex: number, value: any): void {
        // 确保下匿名回调回来的时候该类实例还有效
        if (!this.node || !cc.isValid(this.node)) return;

        // let uid: number = pf.TypeUtil.toSafeNumber(value.uid);
        // let room_uuid_js: string = pf.TypeUtil.toSafeString(value.room_uuid_js);
        let game_uuids_js: string[] = [];
        if (Array.isArray(value.game_uuids_js))
            game_uuids_js = value.game_uuids_js.slice(0, value.game_uuids_js.length);

        this._vGameUUID = game_uuids_js;
        this._bigPot_uuids_js = value;
        this._updateView(transGameUUIDIndex);
    }

    /**
     * 滑条滑动事件
     * @param event
     */
    private _onSliderEvent(event: cc.Event): void {
        if (this._vGameUUID.length <= 1) {
            this._setSliderProgress(this._vGameUUID.length === 1 ? 1 : 0);
            this._updateSliderPercent();
            return;
        }

        let nPer = this._nSliderPerRatio;
        let offset: number = this._nLastSliderProgress - this.slider.progress;
        let progress: number = this._nLastSliderProgress;

        // 过滤一下偏移距离, 否则距离太短会出现闪烁
        if (Math.abs(offset) >= nPer / 2) {
            // 前进
            if (this.slider.progress >= this._nLastSliderProgress) {
                let count = Math.ceil(this.slider.progress / nPer);
                progress = Math.min(1, nPer * count);
            }
            // 回退
            else {
                let count = Math.floor(this.slider.progress / nPer);
                progress = Math.max(0, nPer * count);
            }
        }

        this._setSliderProgress(progress);
        this._updateSliderPercent();
    }

    /**
     * 上一手
     */
    private _onMsgLastHand(): void {
        this.beforePage();
    }

    /**
     * 下一手
     */
    private _onMsgNextHand(): void {
        this.nextPage();
    }

    /**
     * 打开举报面板消息回调(关闭牌谱面板)
     */
    private _onMsgShowAudit(): void {
        this.autoHide();
    }

    /**
     * 强制亮牌消息回调(该功能入口只针对牌局中)
     * @param data
     */
    private _onMsgForceShow(data: game_pb.NoticeReplayForceShowCard): void {
        let selfid: number = data.playerid;
        let game_uuid: string = this.tGameRecords.tPokerHandData.sGameUUID;
        if (selfid !== this._authService.currentUser.userId || !this._handMapCache.has(game_uuid)) {
            console.error(`${GameReviewControl.gClassName} - error: response forceshow data faild`);
            return;
        }

        // 刷新"手牌"缓存
        let freshCardsCache: (
            uid: number,
            cards: game_pb.ICardItem[],
            replayData: ReplayData,
            records: PlayerRecord[]
        ) => void = (
            uid: number,
            cards: game_pb.ICardItem[],
            replayData: ReplayData,
            records: PlayerRecord[]
        ): void => {
            let record: PlayerRecord = null;
            for (let i = 0; i < pf.DataUtil.getArrayLength(records); ++i) {
                if (records[i].playerid === uid) {
                    record = records[i];
                    break;
                }
            }

            let seatInfo: any = null;
            for (let i = 0; i < pf.DataUtil.getArrayLength(replayData.seatsInfo.seatsInfo); ++i) {
                if (replayData.seatsInfo.seatsInfo[i].uid === uid) {
                    seatInfo = replayData.seatsInfo.seatsInfo[i];
                    break;
                }
            }

            // 填充牌值
            if (record && seatInfo) {
                for (let i = 0; i < pf.DataUtil.getArrayLength(cards); ++i) {
                    let number: number = pf.TypeUtil.toSafeNumber(cards[i].number);
                    let suit: number = pf.TypeUtil.toSafeNumber(cards[i].suit);

                    // "record.cards"中是否已存在相同的牌
                    let isExist1 = false;
                    for (let j = 0; j < pf.DataUtil.getArrayLength(record.cards); ++j) {
                        if (
                            pf.TypeUtil.toSafeNumber(record.cards[j].number) === number &&
                            pf.TypeUtil.toSafeNumber(record.cards[j].suit) === suit
                        ) {
                            isExist1 = true;
                            break;
                        }
                    }
                    if (!isExist1) {
                        if (record.cards === null || typeof record.cards === 'undefined') {
                            record.cards = [];
                        }
                        let t: HandCardType = new HandCardType();
                        t.number = number;
                        t.suit = suit;
                        record.cards.push(t);
                    }

                    // "seatInfo.holecards"中是否已存在相同的牌
                    let isExist2 = false;
                    for (let j = 0; j < pf.DataUtil.getArrayLength(seatInfo.holecards); ++j) {
                        if (
                            pf.TypeUtil.toSafeNumber(seatInfo.holecards[j].number) === number &&
                            pf.TypeUtil.toSafeNumber(seatInfo.holecards[j].suit) === suit
                        ) {
                            isExist2 = true;
                            break;
                        }
                    }
                    if (!isExist1) {
                        if (seatInfo.holecards === null || typeof seatInfo.holecards === 'undefined') {
                            seatInfo.holecards = [];
                        }
                        seatInfo.holecards.push({ number: number, suit: suit });
                    }
                }
            }
        };

        // 刷新"旁观者列表"缓存
        let freshStanderUIDCache: (uid: number, standerUIDs: number[]) => number[] = (
            uid: number,
            standerUIDs: number[]
        ): number[] => {
            if (standerUIDs === null || typeof standerUIDs === 'undefined') {
                standerUIDs = [];
            }

            let isExist = false;
            for (let i = 0; i < standerUIDs.length; ++i) {
                if (uid === standerUIDs[i]) {
                    isExist = true;
                    break;
                }
            }

            if (!isExist) {
                standerUIDs.push(uid);
            }

            return standerUIDs;
        };

        // 更新战绩缓存
        do {
            let gameHandMapData: pf.data.PokerHandData = this._handMapCache.getHand(game_uuid);
            if (gameHandMapData) {
                // 更新手牌
                for (let i = 0; i < pf.DataUtil.getArrayLength(data.show_seats); ++i) {
                    let uid: number = pf.TypeUtil.toSafeNumber(data.show_seats[i].uid);
                    let cards = data.show_seats[i].cards;
                    freshCardsCache(uid, cards, gameHandMapData.objReplay, gameHandMapData.gameRecord.records);
                }

                // 更新亮牌权限
                let isSelfOnTheSeat = false;
                for (let i = 0; i < pf.DataUtil.getArrayLength(gameHandMapData.gameRecord.records); ++i) {
                    if (selfid === gameHandMapData.gameRecord.records[i].playerid) {
                        isSelfOnTheSeat = true;
                        gameHandMapData.gameRecord.records[i].bForceShowDown = true;
                        break;
                    }
                }

                // 若自己不在座位上, 那么使用了强制亮牌就说明自己是旁观者, 刷新缓存
                if (!isSelfOnTheSeat) {
                    gameHandMapData.vShowCardByStanderUID = freshStanderUIDCache(
                        selfid,
                        gameHandMapData.vShowCardByStanderUID
                    );
                }
            }
        } while (false);

        // 更新收藏缓存
        do {
            let collecthandMapData: pf.data.PokerHandData = this.tCollectPokerMapData.mHandMapCache.get(game_uuid);
            if (collecthandMapData) {
                // 更新手牌
                for (let i = 0; i < pf.DataUtil.getArrayLength(data.show_seats); ++i) {
                    let uid: number = pf.TypeUtil.toSafeNumber(data.show_seats[i].uid);
                    const cards = data.show_seats[i].cards;
                    freshCardsCache(uid, cards, collecthandMapData.objReplay, collecthandMapData.gameRecord.records);
                }

                // 更新亮牌权限
                let isSelfOnTheSeat = false;
                for (let i = 0; i < pf.DataUtil.getArrayLength(collecthandMapData.gameRecord.records); ++i) {
                    if (selfid === collecthandMapData.gameRecord.records[i].playerid) {
                        isSelfOnTheSeat = true;
                        collecthandMapData.gameRecord.records[i].bForceShowDown = true;
                        break;
                    }
                }

                // 若自己不在座位上, 那么使用了强制亮牌就说明自己是旁观者, 刷新缓存
                if (!isSelfOnTheSeat) {
                    collecthandMapData.vShowCardByStanderUID = freshStanderUIDCache(
                        selfid,
                        collecthandMapData.vShowCardByStanderUID
                    );
                }
            }
        } while (false);

        // 更新当下牌谱状态(下发的全是其他人的手牌)
        do {
            // 当前牌谱(牌局中)
            let tPokerHandData: PokerHandData = this.tGameRecords.tPokerHandData;

            // 更新手牌
            for (let i = 0; i < pf.DataUtil.getArrayLength(data.show_seats); ++i) {
                let uid: number = pf.TypeUtil.toSafeNumber(data.show_seats[i].uid);
                let cards: game_pb.ICardItem[] = data.show_seats[i].cards;

                let record: PlayerRecord = null;
                for (let j = 0; j < tPokerHandData.vPlayerRecords.length; ++j) {
                    if (tPokerHandData.vPlayerRecords[j].playerid === uid) {
                        record = tPokerHandData.vPlayerRecords[j];
                        break;
                    }
                }

                // 填充牌值
                if (record) {
                    for (let j = 0; j < pf.DataUtil.getArrayLength(cards); ++j) {
                        let number: number = pf.TypeUtil.toSafeNumber(cards[j].number);
                        let suit: number = pf.TypeUtil.toSafeNumber(cards[j].suit);

                        // 手牌中是否已存在相同的牌
                        let isExist = false;
                        for (let k = 0; k < record.cards.length; ++k) {
                            if (
                                pf.TypeUtil.toSafeNumber(record.cards[k].number) === number &&
                                pf.TypeUtil.toSafeNumber(record.cards[k].suit) === suit
                            ) {
                                isExist = true;
                                break;
                            }
                        }

                        // 若不存在则插入
                        if (!isExist) {
                            let t: HandCardType = new HandCardType();
                            t.number = number;
                            t.suit = suit;
                            record.cards.push(t);

                            // 标记亮牌长度
                            ++record.forceShowedActLen;
                        }
                    }
                }
            }

            // 更新自己的亮牌权限
            let isSelfOnTheSeat = false;
            for (let i = 0; i < tPokerHandData.vPlayerRecords.length; ++i) {
                if (selfid === tPokerHandData.vPlayerRecords[i].playerid) {
                    isSelfOnTheSeat = true;
                    tPokerHandData.vPlayerRecords[i].bForceShowDown = true;
                    break;
                }
            }

            // 若自己不在座位上, 那么使用了强制亮牌就说明自己是旁观者, 刷新缓存
            if (!isSelfOnTheSeat) {
                tPokerHandData.vShowCardByStanderUID = freshStanderUIDCache(
                    selfid,
                    tPokerHandData.vShowCardByStanderUID
                );
            }

            // 亮玩牌后隐藏按钮, 刷新列表
            this._pokerRoom.roomData.pkPayMoneyItem.showCardCount = data.count;
            this._pokerRoom.roomData.pkPayMoneyItem.spectatorShowCardCount = data.spectatorCount;
            this._setForceShowBtnVisible(false);
            this._updateTableView(tPokerHandData);
            this._resetForceShowCard(tPokerHandData);
            PopupManager.showToast({ content: pf.languageManager.getString('ForceShowCardToast') });
        } while (false);
    }

    private _resetForceShowCard(pokerHandData: PokerHandData): void {
        pokerHandData.vPlayerRecords.forEach((playerData) => {
            if (playerData.playerid === this._authService.currentUser.userId) {
                playerData.reviewSendOutActLen = 0;
            } else {
                playerData.forceShowedActLen = 0;
            }
        });
    }

    /**
     * 发发看消息回调(该功能入口只针对牌局中)
     * @param data
     */
    private _onMsgSendOut(data: game_pb.NoticeReplaySendCard): void {
        let game_uuid: string = this.tGameRecords.tPokerHandData.sGameUUID;
        if (data.player_id !== this._authService.currentUser.userId || !this._handMapCache.has(game_uuid)) {
            console.error(`${GameReviewControl.gClassName} - error: response sendout data faild`);
            return;
        }

        // 更新战绩缓存
        do {
            let gameHandMapData: pf.data.PokerHandData = this._handMapCache.getHand(game_uuid);
            if (gameHandMapData && typeof gameHandMapData !== 'undefined') {
                let records = gameHandMapData.gameRecord.records;
                for (let i = 0; i < pf.DataUtil.getArrayLength(records); ++i) {
                    if (records[i].playerid === data.player_id) {
                        // 从 data 服拉取 json 数据, 若无值会字段缺失, 这里做个转换赋值, 保证字段存在
                        records[i].reviewSendOutLen = pf.TypeUtil.toSafeNumber(records[i].reviewSendOutLen);
                        records[i].reviewSendOutLen += data.cards.length;
                        break;
                    }
                }
            }
        } while (false);

        // 更新收藏缓存
        do {
            let collecthandMapData: PokerHandData = this.tCollectPokerMapData.mHandMapCache.get(game_uuid);
            if (collecthandMapData && typeof collecthandMapData !== 'undefined') {
                let records = collecthandMapData.gameRecord.records;
                for (let i = 0; i < pf.DataUtil.getArrayLength(records); ++i) {
                    if (records[i].playerid === data.player_id) {
                        // 从 data 服拉取 json 数据, 若无值会字段缺失, 这里做个转换赋值, 保证字段存在
                        records[i].reviewSendOutLen = pf.TypeUtil.toSafeNumber(records[i].reviewSendOutLen);
                        records[i].reviewSendOutLen += data.cards.length;
                        break;
                    }
                }
            }
        } while (false);

        // 更新当下牌谱数据
        do {
            // 更新数据
            let refresh = false;
            let tPokerHandData: PokerHandData = this.tGameRecords.tPokerHandData;
            for (const playerRecord of tPokerHandData.vPlayerRecords) {
                if (playerRecord.playerid === data.player_id) {
                    playerRecord.reviewSendOutLen += data.cards.length;
                    playerRecord.reviewSendOutActLen = data.cards.length;
                    refresh = playerRecord.reviewSendOutActLen > 0;
                    break;
                }
            }

            // 标记牌谱"发发看"动画, 刷新列表
            if (refresh) {
                this._updateTableView(tPokerHandData);
                this._resetForceShowCard(tPokerHandData);
            }
            this._updateSendOutState(tPokerHandData);
            PopupManager.showToast({ content: pf.languageManager.getString('GameReplaySendOutToast') });
        } while (false);
    }

    /**
     * 牌局编号
     * @param event
     */
    private _onClickSerialNumber(event: cc.Event): void {
        PopupManager.showToast({
            content: pf.languageManager.getString('allReview_allReview_panel_serial_number_copy_text')
        });
        let game_uuid: string = pf.TypeUtil.toSafeString(this._sCurGameUUID);
        common.StringUtils.copyToClipboard(game_uuid);
    }

    /**
     * 播放
     * @param event
     */
    private _onClickPlay(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (this._vGameUUID.length <= 0 || !this._bHasReplayData) {
            PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips') });
        } else {
            if (!this._tGameReplay) {
                this._tGameReplay = cc.instantiate(this.prefab_game_replay).getComponent(GameReplayControl);
                this._tGameReplay.initialize(this._gameReviewService);
                this.node.addChild(this._tGameReplay.node);
            }

            this._tGameReplay.autoShow(this._dataSourceType, this._sCurGameUUID, true, this.tGameRecords);
        }
    }

    /**
     * 举报
     * @param event
     */
    private _onClickAudit(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (this._vGameUUID.length <= 0 || !this._bHasReplayData) {
            PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips') });
        } else {
            // 判断是否带入了, 带入过才可以举报
            if (pf.app.getCurrentScene() !== commonMacros.SCENE.GAME_SCENE) {
                if (!this._bShowAudit) {
                    PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips3') });
                    return;
                }
            }

            let clubid: number = this.tGameRecords.tPokerHandData.nClubID;
            let sRoomUUID: string = this.tGameRecords.tPokerHandData.sRoomUUID;
            this._auditService.requestInitAudit(clubid, this._sCurGameUUID, sRoomUUID);
        }
    }

    /**
     * 收藏
     * @param event
     */
    private _onClickCollect(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (this._vGameUUID.length <= 0 || !this._bHasReplayData) {
            PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips') });
        } else {
            let gameId = this._pokerRoom.roomData.u32GameID;
            if (pf.app.getCurrentScene() === commonMacros.SCENE.HALL_SCENE) {
                gameId = this.tGameRecords.gameID;
            }

            this._doFavorite(this._sCurGameUUID, gameId);

            // 向"game"服请求收藏
            switch (this._dataSourceType) {
                case TexasCommonDef.GameReviewDataType.EDST_GAMEROOM:
                    {
                        let roomid: number = this._pokerRoom.roomData.u32RoomId;
                        this._pokerRoom.requestDoFavoriteHand(game_pb.FavoriteHandType.gaming, this._sCurGameUUID);
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private async _doFavorite(uuidJs: string, gameId: number) {
        const response = await this._dataService.doFavorite(this._sCurGameUUID, gameId);
        const code = response.result;
        let content = `data server DO_FAVORITE_RESP Unknown Error ${code}`;
        if (code === 0) {
            ++this.tCollectPokerMapData.totalCount;
            content = pf.languageManager.getString('UICollectSuccess');
        } else if (code === 2) {
            content = pf.languageManager.getString('UIHasCollect');
        }

        PopupManager.showToast({
            content: content
        });
    }
    /**
     * 强制亮牌
     * @param event
     */
    private _onClickForceShow(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (this._vGameUUID.length <= 0 || !this._bHasReplayData) {
            PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips') });
        } else {
            this._pokerRoom.requestReplayForceShowCard(this._pokerRoom.roomData.u32RoomId, this._sCurGameUUID);
        }
    }

    /**
     * 发发看
     * @param event
     */
    private _onClickSendOut(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (this._vGameUUID.length <= 0 || !this._bHasReplayData) {
            PopupManager.showToast({ content: pf.languageManager.getString('UIAllreviewReplayTips') });
        } else {
            this._pokerRoom.requestReplaySendCard(this._pokerRoom.roomData.u32RoomId, this._sCurGameUUID);
        }
    }

    /**
     * 大"pot"筛选(加个面板节点增大整体点击区域)
     */
    private _onClickPanelTogglePot(event: cc.Event): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        if (this.toggle_pot.isChecked) {
            this.toggle_pot.uncheck();
        } else {
            this.toggle_pot.check();
        }
    }

    /**
     * 大"pot"筛选
     */
    private _onClickTogglePot(t: CustomToggle): void {
        if (t.isChecked) {
            this._requestBigPotGameUUIDs();
        } else {
            this._vGameUUID = this._getGameuuids();
            this._updateView();
        }

        // 保存上次勾选状态
        this._last_toggle_pot_check_status = t.isChecked;
    }

    private isUSDTable(): boolean {
        return this._pokerRoom.roomData.pkRoomParam.currencyType === pf.client.CurrencyType.CurrencyTypeUSD;
    }

    shouldShowFeatureHandBtn(): void {
        const isGameEligible = this._pokerRoom.roomData.IsEligibleForFeaturedHandSubmission();
        if (!isGameEligible) {
            this.btn_FeatureHand.node.active = false;
            return;
        }
        this.btn_FeatureHand.node.active = false;
    }

    private getOwnPlayerRecord() {
        return this.currentHandData.vPlayerRecords.find(
            (record) => record.playerid === this._authService.currentUser.userId
        );
    }

    private onFeatureHandConfig(status_code: number) {
        switch (status_code) {
            case 400: // Record not found - game type not support
            case 404: // Record not found
            case 40003: // Record does not qualify
            case 40004: // Already Submitted by others
            case 40005: // Game Hand expired
                this.btn_FeatureHand.node.active = false;
                break;

            case 40002: // Already Submitted by self
                this.btn_FeatureHand.node.active = this.canShowFeatureHandBtn();
                this.setFeatureHandBtnInteractable(
                    false,
                    pf.languageManager.getString('game_review_feature_hand_submitted')
                );
                break;

            case 0: // Hand is Eligible to submit and not submitted yet
            case 40001: // Reached daily limit, bt we will show btn
            default:
                this.btn_FeatureHand.node.active = this.canShowFeatureHandBtn();
                this.setFeatureHandBtnInteractable(
                    true,
                    pf.languageManager.getString('game_review_feature_hand_submit')
                );
                break;
        }
    }

    OnFeatureHandBtnClicked() {
        /*
        const str = pf.StringUtil.formatC(
            pf.languageManager.getString(
                this.isUSDTable()
                    ? 'game_review_feature_hand_required_amount_usd'
                    : 'game_review_feature_hand_required_amount'
            ),
            common.CurrencyUtil.convertToClientAmount(this.currentHandData.featureHandFee)
        );
        cv.TP.showRichText(str,
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.onRequestFeatureHandSubmit.bind(this),
            null
        );
        cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_FEATURE_HAND);
    */
    }

    private onRequestFeatureHandSubmit() {
        this._dataService.setFeatureHandSubmit(
            pf.TypeUtil.toSafeNumber(this.currentHandData.sGameUUID),
            this.currentHandData.sGameUUID,
            this.currentHandData.nGameid,
            pf.TypeUtil.toSafeNumber(this.currentHandData.sRoomUUID)
        );
    }

    private onHandRequestResponseRecieved(err_code: number) {
        switch (err_code) {
            case 1:
                this.setFeatureHandBtnInteractable(
                    false,
                    pf.languageManager.getString('game_review_feature_hand_submitted')
                );
                break;

            case 40002:
                this.setFeatureHandBtnInteractable(
                    false,
                    pf.languageManager.getString('game_review_feature_hand_submitted')
                );
                PopupManager.showToast({
                    content: pf.languageManager.getString('game_review_feature_hand_already_submitted')
                }); // Already uploaded by self
                break;

            case 40004:
                this.btn_FeatureHand.node.active = false;
                PopupManager.showToast({
                    content: pf.languageManager.getString('game_review_feature_hand_already_submitted')
                }); // Already uploaded by other user
                break;

            case 40001:
                this.showFeatureHandErrorMsg('game_review_feature_hand_reached_daily_limit'); // Daily Limit reached
                break;

            case 400:
            case 404:
            case 40003:
            case 40005:
                this.showFeatureHandErrorMsg('UIMainTips01'); // Record not quailified/found
                break;

            case 401:
                this.showFeatureHandErrorMsg('ServerErrorCode5'); // Invalid token
                break;

            case 402:
                this.showFeatureHandErrorMsg('ServerErrorCode53'); // Insufficient balance
                break;

            case 500:
                this.showFeatureHandErrorMsg('ServerErrorCode237'); // Internal server error
                break;

            default:
                this.setFeatureHandBtnInteractable(
                    false,
                    pf.languageManager.getString('game_review_feature_hand_submit')
                );
                break;
        }
    }

    private showFeatureHandErrorMsg(errorToastMsg: string) {
        this.setFeatureHandBtnInteractable(true, pf.languageManager.getString('game_review_feature_hand_submit'));
        PopupManager.showToast({ content: pf.languageManager.getString(errorToastMsg) });
    }

    private setFeatureHandBtnInteractable(isInteractable: boolean, submitLabelText?: string) {
        this.btn_FeatureHand.interactable = isInteractable;
        this.txt_FeatureSubmit.node.color = isInteractable
            ? this.featureHandLabelActiveColor
            : this.featureHandLabelInactiveColor;
        this.featureHand_StarIcon.color = this.txt_FeatureSubmit.node.color;
        if (submitLabelText !== null) {
            this.txt_FeatureSubmit.string = submitLabelText;
        }
    }

    sortListAssendingOrder(a: any, b: any): number {
        return a.squidWinLoseAmount < b.squidWinLoseAmount ? 1 : -1;
    }

    sortBySeatStatus(a: any, b: any): number {
        const seatOrder = [
            game_pb.HandRecord.SeatStatus.IsWaitingHasCard,
            game_pb.HandRecord.SeatStatus.IsWaitingNoCard,
            game_pb.HandRecord.SeatStatus.IsAwayHasCard,
            game_pb.HandRecord.SeatStatus.IsAwayHasNoCard
        ];

        const indexA = seatOrder.indexOf(a.seatStatus);
        const indexB = seatOrder.indexOf(b.seatStatus);

        // If both statuses are the same, maintain order (return 0)
        if (indexA === indexB) {
            return 0;
        }

        // Sort based on index in the seatOrder array
        return indexA - indexB;
    }

    canShowFeatureHandBtn(): boolean {
        const gameId = this._pokerRoom.roomData.u32GameID;
        const gameMode = this._pokerRoom.roomData.pkRoomParam.game_mode;
        if (gameMode === pf.client.CreateGameMode.CreateGame_Mode_Short) {
            return false;
        }
        return gameId === pf.client.GameId.Texas || gameId === pf.client.GameId.StarSeat;
    }

    // showSquidReviewSettlement(isshowSquidReviewSettlement:boolean=false){
    //     this.txt_title.string = pf.languageManager.getString(isshowSquidReviewSettlement?'review_squid_game_status':'allReview_allReview_panel_review_txt'); // 标题
    //     this.panel_scrollview_widget.node.active=!isshowSquidReviewSettlement;
    //     this.squidSettlementReview.node.active=isshowSquidReviewSettlement;

    //     this.panel_top.parent.active=!isshowSquidReviewSettlement;
    //     this.panel_bottom.getChildByName("panel_top").active=!isshowSquidReviewSettlement;
    //     this.setSquidSettlementPageIndexInfo(isshowSquidReviewSettlement);
    //     if(isshowSquidReviewSettlement)
    //         this.panel_toggle_pot.active=false;
    //     // this commented line is for next release use due to BE not ready but fuctionallity is ready
    //     /*
    //     this.panel_toggle_pot.active=!isshowSquidReviewSettlement;
    //     this.toggle_pot.node.active=!isshowSquidReviewSettlement;
    //     this.panel_toggle_pot.getComponent(cc.Button).enabled=!isshowSquidReviewSettlement;
    //     const txt_toggle_pot: cc.Label = this.panel_toggle_pot.getChildByName('txt_toggle_pot').getComponent(cc.Label);
    //      const nCurPage = this._vGameUUID.length > 0 ? this._nCurGameUUIDIndex + 1 : 0;
    //      txt_toggle_pot.string = isshowSquidReviewSettlement? pf.StringUtil.formatC(pf.languageManager.getString('review_squid_status'),nCurPage,this._vGameUUID.length) : pf.languageManager.getString('allReview_allReview_panel_toggle_img_toggle_txt');
    //      */
    // }

    // squidFinalSettlementLastPage(){
    //     if (!this.isInit()) return;
    //     if (this._vGameUUID.length <= 1) return;
    //     this.txt_title.string = pf.languageManager.getString('review_squid_game_status');// when squid final settlement is active then before showing text should be change
    //     this._nLastSaveGameUUIDIndex = this._vGameUUID.length - 1;

    //     // this block is use lastGameUUID is already get data from BE or not befor final Settelment Auto call. then replace other wise AT-8027 tickt related problem occured
    //    {
    //     const sCurGameUUID = this._vGameUUID[this._nLastSaveGameUUIDIndex];
    //     const value: any = this._handMapCache?.getHand(sCurGameUUID);
    //     if(value?.squidHuntGameSettlement){
    //         if(!value.squidHuntGameSettlement.finalSettlementPlayers){
    //             this._handMapCache.removeItem(sCurGameUUID);
    //         }
    //     }
    //    }

    //     this._updateView(this._nLastSaveGameUUIDIndex);
    //     this._setSliderProgress(1);
    //     this._updateSliderPercent();
    //     this._buttonEvent = ButtonEvent.Last;
    // }

    // this commented line is for next release use due to BE not ready but fuctionallity is ready
    /*
    private _updateGameuuidsSquidFinalSettelment(GameUUID_SquidFinalSettelment:string[]) {

        if(this._vGameUUID_SquidFinalSettelment.length<GameUUID_SquidFinalSettelment.length)
            this._vGameUUID_SquidFinalSettelment=GameUUID_SquidFinalSettelment;
    }

    public jumpSquidFinalSettelmentPage(gameUUID:string): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        if (!this.isInit()) return;
        if (this._vGameUUID.length < 0) return;
        const index=this._vGameUUID.indexOf(gameUUID);
        this._nCurGameUUIDIndex =index;
        const progress: number = Math.min(1, pf.MathUtil.plus(this.slider.progress, this._nSliderPerRatio));
        this._setSliderProgress(progress);
        this._updateSliderPercent();
    }
        */

    // setSquidSettlementPageIndexInfo(isShowSquidReviewSettlement:boolean)
    // {
    //     const nCurPage = this._vGameUUID.length > 0 ? this._nCurGameUUIDIndex + 1 : 0;
    //     this.txt_page.string =isShowSquidReviewSettlement? pf.StringUtil.formatC('%d%s/%d',nCurPage, pf.languageManager.getString("review_squid_game_final_record"),this._vGameUUID.length) : pf.StringUtil.formatC('%d/%d', nCurPage, this._vGameUUID.length);
    // }

    // getSquidHandType() : SquidHandType
    // {
    //     switch (this._buttonEvent) {
    //         case ButtonEvent.Last:
    //             return SquidHandType.Squid_Settlement_Hand;
    //         case ButtonEvent.First:
    //             return SquidHandType.Normal_Hand;
    //         case ButtonEvent.Next:
    //             return this._squidHandType === SquidHandType.Squid_Final_Hand
    //                 ? SquidHandType.Squid_Settlement_Hand
    //                 : SquidHandType.Squid_Final_Hand;
    //         case ButtonEvent.Prev:
    //             return this._squidHandType === SquidHandType.Squid_Settlement_Hand
    //                 ? SquidHandType.Squid_Final_Hand
    //                 : SquidHandType.Squid_Settlement_Hand;
    //         default:
    //             return SquidHandType.Normal_Hand;
    //     }
    // }
}
