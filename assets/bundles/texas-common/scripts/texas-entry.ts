import * as pf from 'pf';
import * as common from 'common';
import registerEntry = pf.registerEntry;
import { macros } from './common/texas-macros';
import LoadingViewManager = common.components.LoadingViewManager;

import BUNDLE_NAME = macros.BUNDLE_NAME;

@registerEntry(macros.BUNDLE_NAME)
export class TexasEntry extends pf.BundleEntryBase {
    private _changeLanguageBinder = this.onChangeLanguage.bind(this);
    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_GAME;
    }

    protected getLanguageStringPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Language_String_Path.ZH_CN;
            case pf.LANGUAGE_GROUPS.yn_TH:
                return macros.Language_String_Path.YN_TH;
            case pf.LANGUAGE_GROUPS.th_PH:
                return macros.Language_String_Path.TH_PH;
            case pf.LANGUAGE_GROUPS.hi_IN:
                return macros.Language_String_Path.HI_IN;
            default:
                return macros.Language_String_Path.EN_US;
        }
    }

    protected getAddressableConfigPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Addressable_Config_Path.ZH_CN;
            default:
                return macros.Addressable_Config_Path.EN_US;
        }
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onLoad`);
        pf.languageManager.addListener('beforeLanguageChange', this._changeLanguageBinder);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onEnter`);
        const jackpotEntry = await pf.updateManager.loadBundle(
            pf.updateManager.getUpdateItem(macros.JACKPOT_BUNDLE_NAME)
        );
        await jackpotEntry.enter();

        await this.loadConfigs();
        // download assets
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask(macros.BUNDLE_NAME);
        // NOTE:
        // make address progress not over 20%
        let addressalbeTotalCount = 0;
        await assetLoader.start((finish, total) => {
            if (addressalbeTotalCount === 0) {
                addressalbeTotalCount = total * 5;
            }
            // options?.onProgress(finish, addressalbeTotalCount);
        });
        return Promise.resolve();
    }

    async onExit(): Promise<void> {
        // LoadingViewManager.getInstance().showLoadingView(pf.languageManager.getString('Common_LoadingLobby'));
        cc.log(`bundle ${macros.BUNDLE_NAME} onExit`);
        pf.bundleManager.exitBundle(macros.JACKPOT_BUNDLE_NAME);
        pf.bundleManager.releaseAll(this.bundle);
    }

    onChangeLanguage(newLanguage: string, promises: Promise<unknown>[]): void {
        const promise = this.loadConfigs(newLanguage);
        promises.push(promise);
    }

    onUnload(): void {
        cc.log(`bundle ${BUNDLE_NAME} onUnload`);
        pf.languageManager.removeListener('beforeLanguageChange', this._changeLanguageBinder);
    }
}
