export namespace macros {
    export const BUNDLE_NAME = 'texas-common';
    export const JACKPOT_BUNDLE_NAME = 'jackpot';
    export const PORTRAIT_BUNDLE_NAME = 'texas-portrait';
    export const LANDSCAPE_BUNDLE_NAME = 'texas-landscape';
    export const RULE_URL = 'user/article/getimage?img=';

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/texas-common-addressable-assets-zh_cn',
        EN_US = 'configs/texas-common-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        HI_IN = 'languages/string-hi_in',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph'
    }

    export enum Assets {
        PUSH_NOTIFICATION = 'common.push-notice',
        CURRENCY_ICON_GC = 'common.common-currency-icon-gc',
        CURRENCY_ICON_SC = 'common.common-currency-icon-sc',
        CURRENCY_ICON_CASINO_COIN = 'common.common-currency-icon-casino-coin',
        CURRENCY_ICON_CNY = 'common.common-currency-icon-cny',
        CURRENCY_ICON_EUR = 'common.common-currency-icon-eur',
        CURRENCY_ICON_GBP = 'common.common-currency-icon-gbp',
        CURRENCY_ICON_GOLD_COIN = 'common.common-currency-icon-gold-coin',
        CURRENCY_ICON_USD = 'common.common-currency-icon-usd',
        CURRENCY_ICON_USDT = 'common.common-currency-icon-usdt',
        COIN_DROP_ANIM_GC = 'texas-common.coin-drop-GC',
        COIN_DROP_ANIM_SC = 'texas-common.coin-drop-SC',
        SC_STACK_1 = 'common.sc-stack-1',
        SC_STACK_2 = 'common.sc-stack-2',
        GC_STACK_1 = 'common.gc-stack-1',
        GC_STACK_2 = 'common.gc-stack-2',
        SIDE_POT_VALUE = 'texas-common.side-pot-value',

        LUCK_TURNTABLE_BUTTON = 'common.luck-turntables-button',

        SPORTS_BETTING_CONTENT_ITEM = 'texas-common.sports-betting-content-item',
        SPORTS_BETTING_POPUP_PANEL = 'texas-common.sports-betting-popup-panel',
        SPORTS_BETTING_PROMPT_PANEL = 'texas-common.sports-betting-prompt-panel',
        SPORTS_BETTING_SCROLL_INDICATOR_ITEM = 'texas-common.sports-betting-scroll-indicator-item'
    }

    export enum DynamicAssets {
        JACKPOT_NUMBER_PANEL = 'jackpot-dynamic.number-panel',
        ZOOM_STATISTICS = 'texas-common-dynamic.zoom-statistics',
        BOMB_POT_ANIMATION = 'texas-common-dynamic.bomb-pot-animation',
        LABA_ANIMATION = 'texas-common-dynamic.laba-animation',
        LUCKY_REWARD = 'texas-common-dynamic.lucky-reward'
    }

    export enum Audio {
        BUTTON_CLICK = 'common-audio.button-click',
        TAB = 'common-audio.tab',
        PLAYER_TURN = 'common-audio.player-turn',
        INSURANCE_CONFIRM = 'texas-common-audio.insurance-confirm',
        INSURANCE_SUCCESS = 'texas-common-audio.insurance-success',
        DEAL_CARD = 'common-audio.deal-card',
        CHIPS_TO_POT = 'common-audio.chips-to-pot',
        CHIPS_TO_TABLE = 'texas-common-audio.chips-to-table',
        CHECK_SOUND = 'texas-common-audio.check-sound',
        FOLD_SOUND = 'texas-common-audio.fold-sound',
        SLIDER = 'texas-common-audio.slider',
        SLIDER_TOP = 'texas-common-audio.slider-top',
        CHIP_FLY = 'common-audio.chip-fly',
        DANMU = 'texas-common-audio.danmu',
        BAOJIFIRE = 'texas-common-audio.baojiFire'
    }

    export enum AudioSettingKeys {
        MUSIC = 'client_music_key',
        SOUND_EFFECT = 'client_sound_key'
    }
}
