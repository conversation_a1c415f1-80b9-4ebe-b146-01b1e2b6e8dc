/* eslint-disable camelcase */
import { TypeSafeEventEmitter } from '../../../../poker-framework/scripts/core/event/event-emitter';

export interface GameReviewEvents {
    update_replay_data: (param: { uuid: string; hasReplayData: boolean }) => void;
    game_replay_lastHand: () => void;
    game_replay_nextHand: () => void;
    update_hand: () => void;
}

export class GameReviewService extends TypeSafeEventEmitter<GameReviewEvents> {
    static readonly serviceName = 'GameReviewService';
    readonly name = 'GameReviewService';
}
