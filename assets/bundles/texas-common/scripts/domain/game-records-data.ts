/* eslint-disable camelcase */
import { PokerHandData } from '../../../../poker-framework/scripts/services/data/hand-data';
import { RoomParams } from './texas-room-data';

/**
 * 带入信息
 */
export class BuyInsData {
    nUID: number = 0;
    nTotalBuyin: number = 0;
    nWinBet: number = 0;
    nInsuraceWinbet: number = 0;
    nInsuranceBetAmount: number = 0;
    nHand: number = 0;
    nDrawin: number = 0;
    nJackpotWinbet: number = 0;
    sPlayername: string = '';
    sPlayerHead: string = '';
    nLastBuyinClubid: number = 0; // 是次买入的俱乐部ID
    nAward2CludFund: number = 0; // jackpot奖励给俱乐部基金
}

/**
 * 牌局信息
 */
export class PokerInfoData {
    sRoomUUID: string = '';
    nCreateTime: number = 0;
    nTotalHand: number = 0;
    nSelfWinbet: number = 0;
    nMaxPot: number = 0;
    nInsurance_Winbet: number = 0;
    nJackpotWinbet: number = 0;
    nTotalBuyin: number = 0;

    sOwnerName: string = '';

    vHandUUIDList: string[] = []; // 手牌UUID列表，通过手牌UUID查询每手牌的牌谱
    vBuyinList: BuyInsData[] = []; // 带入列表

    tRoomParam: RoomParams = new RoomParams();

    /**
     * 重置初值
     */
    reset(): void {
        this.sRoomUUID = '';
        this.nCreateTime = 0;
        this.nTotalHand = 0;
        this.nSelfWinbet = 0;
        this.nMaxPot = 0;
        this.nInsurance_Winbet = 0;
        this.nJackpotWinbet = 0;
        this.nTotalBuyin = 0;
        this.sOwnerName = '';
        this.vHandUUIDList = [];
        this.vBuyinList = [];
        this.tRoomParam = new RoomParams();
    }
}

/**
 * Business logic class for managing game records data
 * Moved from poker-framework to texas-common domain
 */
export class GameRecordsData {
    nRecordsTexasCount: number = 0; //
    nRecordsAofCount: number = 0; //
    nRecordsBetCount: number = 0; //
    gameID: number = 0; // 当前游戏ID
    tPokerHandData: PokerHandData = new PokerHandData(); // 手牌信息
    tPokerInfoData: PokerInfoData = new PokerInfoData(); // 当前查看的牌局信息
    mHandMapCache: Map<string, any> = new Map(); // 所有牌局缓存
    // vRecordTexasList: RecordsData[] = [];                           //
    // vRecordAofList: RecordsData[] = [];                             //
    // vRecordBetList: RecordsData[] = [];                             //
}

export class CollectPokerMapData {
    totalCount: number = 0; // 服务端已收藏牌谱的总数量
    mUUIDCache: Map<string, any> = new Map(); // "uuid"缓存
    tPokerHandData: PokerHandData = new PokerHandData(); // 手牌信息
    mHandMapCache: Map<string, any> = new Map(); // 所有牌局缓存
    mSimpleHandMapCache: Map<string, any> = new Map(); // 简单牌局缓存
}
