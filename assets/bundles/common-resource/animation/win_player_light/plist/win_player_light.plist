<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>win_player_light.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{1798,180}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{2,2},{1798,180}}</string>
                <key>sourceSize</key>
                <string>{1802,184}</string>
            </dict>
            <key>win_player_light1.png</key>
            <dict>
                <key>frame</key>
                <string>{{721,322},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>win_player_light10.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,388},{105,103}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{105,103}}</string>
                <key>sourceSize</key>
                <string>{105,103}</string>
            </dict>
            <key>win_player_light11.png</key>
            <dict>
                <key>frame</key>
                <string>{{317,184},{146,144}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{146,144}}</string>
                <key>sourceSize</key>
                <string>{146,144}</string>
            </dict>
            <key>win_player_light12.png</key>
            <dict>
                <key>frame</key>
                <string>{{739,184},{132,130}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{132,130}}</string>
                <key>sourceSize</key>
                <string>{132,130}</string>
            </dict>
            <key>win_player_light13.png</key>
            <dict>
                <key>frame</key>
                <string>{{1802,2},{180,202}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{180,202}}</string>
                <key>sourceSize</key>
                <string>{180,202}</string>
            </dict>
            <key>win_player_light2.png</key>
            <dict>
                <key>frame</key>
                <string>{{601,184},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>win_player_light3.png</key>
            <dict>
                <key>frame</key>
                <string>{{583,322},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>win_player_light4.png</key>
            <dict>
                <key>frame</key>
                <string>{{463,184},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>win_player_light5.png</key>
            <dict>
                <key>frame</key>
                <string>{{445,332},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>win_player_light6.png</key>
            <dict>
                <key>frame</key>
                <string>{{169,345},{154,124}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{154,124}}</string>
                <key>sourceSize</key>
                <string>{154,124}</string>
            </dict>
            <key>win_player_light7.png</key>
            <dict>
                <key>frame</key>
                <string>{{169,184},{146,159}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{146,159}}</string>
                <key>sourceSize</key>
                <string>{146,159}</string>
            </dict>
            <key>win_player_light8.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,184},{165,202}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{165,202}}</string>
                <key>sourceSize</key>
                <string>{165,202}</string>
            </dict>
            <key>win_player_light9.png</key>
            <dict>
                <key>frame</key>
                <string>{{295,345},{148,152}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{148,152}}</string>
                <key>sourceSize</key>
                <string>{148,152}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>win_player_light.png</string>
            <key>size</key>
            <string>{2048,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e53853c25947179c8880329991de1969$</string>
            <key>textureFileName</key>
            <string>win_player_light.png</string>
        </dict>
    </dict>
</plist>
