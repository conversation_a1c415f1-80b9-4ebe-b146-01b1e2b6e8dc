{"__type__": "cc.AnimationClip", "_name": "RewardPopup_Loop", "_objFlags": 0, "_native": "", "_duration": 64.33333333333333, "sample": 30, "speed": 1, "wrapMode": 2, "curveData": {"paths": {"Node_BehindPanel/Node_CoinPivot/Coins": {"comps": {}, "props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 4.333333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 7.166666666666667, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 9.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 12.166666666666666, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 14.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 15, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 17.166666666666668, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 19.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 20, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 22.166666666666668, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 24.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 25, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 27.166666666666668, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 29.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 30, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 32.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 34.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 35, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 37.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 39.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 40, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 42.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 44.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 45, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 47.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 49.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 50, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 52.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 54.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 55, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 57.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 59.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 60, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "sineOut"}, {"frame": 62.166666666666664, "value": {"__type__": "cc.Vec2", "x": 0.96, "y": 0.96}, "curve": "sineIn"}, {"frame": 64.33333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_BehindPanel/Node_Grass/Grass_L": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 4.333333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 7.166666666666667, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 9.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 12.166666666666666, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 14.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 15, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 17.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 19.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 20, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 22.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 24.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 25, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 27.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 29.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 30, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 32.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 34.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 35, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 37.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 39.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 40, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 42.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 44.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 45, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 47.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 49.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 50, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 52.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 54.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 55, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 57.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 59.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 60, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 62.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 64.33333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_BehindPanel/Node_Grass/Grass_R": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 4.333333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 7.166666666666667, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 9.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 12.166666666666666, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 14.333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 15, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 17.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 19.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 20, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 22.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 24.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 25, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 27.166666666666668, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 29.333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 30, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 32.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 34.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 35, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 37.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 39.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 40, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 42.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 44.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 45, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 47.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 49.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 50, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 52.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 54.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 55, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 57.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 59.333333333333336, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 60, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 62.166666666666664, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.2}, "curve": "linear"}, {"frame": 64.33333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Lights/Light": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 64.33333333333333, "value": -359}]}}, "Node_Lights/LightGlow": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 64.33333333333333, "value": 359}]}}}}, "events": []}