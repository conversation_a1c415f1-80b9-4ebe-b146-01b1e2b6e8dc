{"__type__": "cc.AnimationClip", "_name": "Leaderboard_NotInList", "_objFlags": 0, "_native": "", "_duration": 0.5, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_Root": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.25, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Root/Node_Base (Button)/Notify_Glow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_Value": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/particlesystem_Reward_Gravity": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/particlesystem_Reward_Radial": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/Node_Coins": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "y": [{"frame": 0, "value": 25}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_InList": {"props": {"active": [{"frame": 0, "value": false}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_NotInList": {"comps": {}, "props": {"active": [{"frame": 0, "value": true}], "x": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 0}, {"frame": 0.3, "value": -4}, {"frame": 0.36666666666666664, "value": 4, "curve": "cubicOut"}, {"frame": 0.5, "value": 0}]}}, "Node_Root/Node_Base (Button)/Node_ProgressBar": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Toast": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}], "opacity": [{"frame": 0, "value": 0}]}}, "Node_Root/Node_Base (Button)": {"comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "05825cf9-b128-4ca9-bed1-69a2b7d32cbb"}}]}}}}}, "events": []}