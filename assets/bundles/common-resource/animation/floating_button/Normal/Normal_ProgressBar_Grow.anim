{"__type__": "cc.AnimationClip", "_name": "Normal_ProgressBar_Grow", "_objFlags": 0, "_native": "", "_duration": 0.6666666666666666, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_Root/Node_Base (Button)/Node_ProgressBar/Bar_ProgressGrowGlow": {"props": {"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "linear"}, {"frame": 0.25, "value": 255, "curve": "linear"}, {"frame": 0.3333333333333333, "value": 255}, {"frame": 0.5833333333333334, "value": 0}], "scale": [{"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.2, "y": 1.2}}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/Node_Coins": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.2, "y": 1.2}, "curve": "linear"}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Root/Node_Base (Button)/Notify_Glow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "Node_Root/Node_Base (Button)/Node_ProgressBar/Bar1_Marker": {"props": {"color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 249, "g": 77, "b": 77, "a": 255}}, {"frame": 0.08333333333333333, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Color", "r": 249, "g": 77, "b": 77, "a": 255}}, {"frame": 0.25, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Color", "r": 249, "g": 77, "b": 77, "a": 255}}]}}}}, "events": []}