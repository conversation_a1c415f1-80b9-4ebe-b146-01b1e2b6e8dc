{"__type__": "cc.AnimationClip", "_name": "FloatingBall_Init", "_objFlags": 0, "_native": "", "_duration": 0.016666666666666666, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_Root/Node_Base (Button)/Node_ProgressBar/Bar_ProgressGrowGlow": {"props": {"opacity": [{"frame": 0, "value": 0, "curve": "linear"}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/Node_Coins": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}], "y": [{"frame": 0, "value": 25}]}}, "Node_Root/Node_Base (Button)/Notify_Glow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "Node_Root": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_Value": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/particlesystem_Reward_Gravity": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/particlesystem_Reward_Radial": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_ProgressBar": {"props": {"active": [{"frame": 0, "value": false}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_InList": {"props": {"active": [{"frame": 0, "value": false}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}}]}}, "Node_Root/Node_Base (Button)/Node_CoinsValue/label_NotInList": {"comps": {}, "props": {"active": [{"frame": 0, "value": false}], "x": [{"frame": 0, "value": 0}]}}, "Node_Toast": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}], "opacity": [{"frame": 0, "value": 0}]}}, "Node_Root/Node_Base (Button)": {"comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "05825cf9-b128-4ca9-bed1-69a2b7d32cbb"}}]}}}}}, "events": []}