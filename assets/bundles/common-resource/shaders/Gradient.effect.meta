{"ver": "1.0.27", "uuid": "94516b2b-2654-4968-90db-a5774e669bd0", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\nvarying vec2 v_relativePos;\nuniform vec2 nodeSize;\nuniform vec2 nodeOffset;\n#if USE_GRADIENT_COLORS\n    uniform vec2 startPos;\nuniform vec2 endPos;\n    varying float lerpt;\n#endif\nvoid main () {\n    vec4 pos = vec4(a_position, 1.0);\n    #if CC_USE_MODEL\n        pos = cc_matViewProj * cc_matWorld * pos;\n    #else\n        pos = cc_matViewProj * pos;\n    #endif\n    v_uv0 = a_uv0;\n    v_relativePos = vec2((a_position.x - nodeOffset.x) / nodeSize.x, (a_position.y - nodeOffset.y) / nodeSize.y);\n    #if USE_GRADIENT_COLORS\n        vec2 direction = endPos - startPos;\n        float lenSq = dot(direction, direction);\n        vec2 relativeCoord = v_relativePos - startPos;\n        lerpt = dot(relativeCoord, direction) / lenSq;\n    #endif\n    v_color = a_color;\n    gl_Position = pos;\n}", "frag": "\nprecision mediump float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nvarying vec2 v_relativePos;\nuniform sampler2D texture;\n#if USE_GRADIENT_TEXTURE\n    uniform sampler2D gradientTex;\n#elif USE_GRADIENT_COLORS\n    varying float lerpt;\n    uniform vec4 startColor;\nuniform vec4 endColor;\n    #if USE_EXTRA_COLOR\n        uniform vec4 middleColor;\nuniform float middlePos;\n    #endif\n#endif\nvoid main () {\n    vec4 o = vec4(1, 1, 1, 1);\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n    o *= v_color;\n    #if USE_GRADIENT_TEXTURE\n        o *= texture2D(gradientTex, v_relativePos);\n    #elif USE_GRADIENT_COLORS\n        #if USE_EXTRA_COLOR\n            o *= mix(mix(startColor, middleColor, lerpt/middlePos), mix(middleColor, endColor, (lerpt - middlePos)/(1.0 - middlePos)), step(middlePos, lerpt));\n        #else\n            o *= mix(startColor, endColor, lerpt);\n        #endif\n    #endif\n    ALPHA_TEST(o);\n    #if USE_BGRA\n        gl_FragColor = o.bgra;\n    #else\n        gl_FragColor = o.rgba;\n    #endif\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\nin vec2 a_uv0;\nout vec2 v_uv0;\nout vec2 v_relativePos;\nuniform ConstantVS1 {\n    vec2 nodeSize;\n    vec2 nodeOffset;\n};\n#if USE_GRADIENT_COLORS\n    uniform ConstantVS2 {\n        vec2 startPos;\n        vec2 endPos;\n    };\n    out float lerpt;\n#endif\nvoid main () {\n    vec4 pos = vec4(a_position, 1.0);\n    #if CC_USE_MODEL\n        pos = cc_matViewProj * cc_matWorld * pos;\n    #else\n        pos = cc_matViewProj * pos;\n    #endif\n    v_uv0 = a_uv0;\n    v_relativePos = vec2((a_position.x - nodeOffset.x) / nodeSize.x, (a_position.y - nodeOffset.y) / nodeSize.y);\n    #if USE_GRADIENT_COLORS\n        vec2 direction = endPos - startPos;\n        float lenSq = dot(direction, direction);\n        vec2 relativeCoord = v_relativePos - startPos;\n        lerpt = dot(relativeCoord, direction) / lenSq;\n    #endif\n    v_color = a_color;\n    gl_Position = pos;\n}", "frag": "\nprecision mediump float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\nin vec2 v_uv0;\nin vec2 v_relativePos;\nuniform sampler2D texture;\n#if USE_GRADIENT_TEXTURE\n    uniform sampler2D gradientTex;\n#elif USE_GRADIENT_COLORS\n    in float lerpt;\n    uniform ConstantFS1 {\n        vec4 startColor;\n        vec4 endColor;\n    };\n    #if USE_EXTRA_COLOR\n        uniform ConstantFS2 {\n            vec4 middleColor;\n            float middlePos;\n        };\n    #endif\n#endif\nvoid main () {\n    vec4 o = vec4(1, 1, 1, 1);\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n    o *= v_color;\n    #if USE_GRADIENT_TEXTURE\n        o *= texture2D(gradientTex, v_relativePos);\n    #elif USE_GRADIENT_COLORS\n        #if USE_EXTRA_COLOR\n            o *= mix(mix(startColor, middleColor, lerpt/middlePos), mix(middleColor, endColor, (lerpt - middlePos)/(1.0 - middlePos)), step(middlePos, lerpt));\n        #else\n            o *= mix(startColor, endColor, lerpt);\n        #endif\n    #endif\n    ALPHA_TEST(o);\n    #if USE_BGRA\n        gl_FragColor = o.bgra;\n    #else\n        gl_FragColor = o.rgba;\n    #endif\n}"}}], "subMetas": {}}