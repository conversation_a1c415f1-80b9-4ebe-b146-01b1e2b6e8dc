[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "HumanboyAdvancedSetting", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}], "_active": true, "_components": [{"__id__": 338}, {"__id__": 339}], "_prefab": {"__id__": 340}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_shiled", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 80, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2338, "_originalHeight": 1080, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "19LJMuV5pKBZwrZKui53yu", "sync": false}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 11}, {"__id__": 15}, {"__id__": 20}, {"__id__": 23}, {"__id__": 27}, {"__id__": 31}, {"__id__": 294}, {"__id__": 297}, {"__id__": 301}, {"__id__": 304}, {"__id__": 313}, {"__id__": 322}, {"__id__": 325}, {"__id__": 328}, {"__id__": 331}], "_active": true, "_components": [{"__id__": 335}, {"__id__": 336}], "_prefab": {"__id__": 337}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1421, "height": 839}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1340, "height": 680}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -42, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f3c230e-f5ca-404f-a01a-a6b00ec38beb"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "841StUqnxLpZ4Jzlbr+IKi", "sync": false}, {"__type__": "cc.Node", "_name": "img_title", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 290, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 350, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d1783f89-32c7-43d0-bfbc-71f4643baf0c"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "49ab10b4-9a0e-40d5-8a8e-7fee5ab81aa6"}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 16, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "d96W3KWKNMDrqo8MGDDGrx", "sync": false}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [708.5, 339.5, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.StudioWidget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": -37, "_top": 41, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3149ac62-9fe3-4915-8013-************"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ec6b0d89-bf65-4e76-901a-94de3bcd9a77"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "78Z5j9MIlLqKhsTdOMNo8N", "sync": false}, {"__type__": "cc.Node", "_name": "txt_desc_word", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 569.84, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-610, 215, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "下注按钮设置, 请选择%d个不同的下注按钮", "_N$string": "下注按钮设置, 请选择%d个不同的下注按钮", "_fontSize": 30, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "STHeitiSC", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "6aVgE7oWRIpYTX3d0sHRU4", "sync": false}, {"__type__": "cc.Node", "_name": "btn_clean", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 187, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [490, 215, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1ebd5d9f-e0cd-4f19-93b7-89dcff326a0e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "49ab10b4-9a0e-40d5-8a8e-7fee5ab81aa6"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "banW+0bndB97jJqIacNrK0", "sync": false}, {"__type__": "cc.Node", "_name": "img_line_1", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1240, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.017699999999990723, 170.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.StudioWidget", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 0.06369999999999998, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0b324e8d-7b4f-4849-b26a-300a8583e6c6"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "2cAgkwnhRA8YHAMJXf2hjh", "sync": false}, {"__type__": "cc.Node", "_name": "panel_opts", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 32}, {"__id__": 58}, {"__id__": 84}, {"__id__": 110}, {"__id__": 136}, {"__id__": 162}, {"__id__": 188}, {"__id__": 214}, {"__id__": 240}, {"__id__": 266}], "_active": true, "_components": [{"__id__": 292}], "_prefab": {"__id__": 293}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1250, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}, {"__id__": 48}], "_active": true, "_components": [], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-500, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 34}, {"__id__": 40}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}, {"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}], "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": false, "_components": [{"__id__": 41}, {"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "btn": {"__id__": 45}, "txtBetNode": {"__id__": 34}, "imgMask": {"__id__": 41}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "286iLgX5RD2Lj7SI0kUep6", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 49}, {"__id__": 52}], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "42b/mjZQVKlZ3kueTLueoi", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": false, "_components": [{"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "3alW4A/z9AVKASqHY75dKa", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 53}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "ecPFugdGdJsp/O/FRRcd27", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "58OQS0la5PiqH/rklSlP4H", "sync": false}, {"__type__": "cc.Node", "_name": "node_1", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 59}, {"__id__": 74}], "_active": true, "_components": [], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 60}, {"__id__": 66}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}, {"__id__": 72}], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [{"__id__": 61}], "_active": true, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_active": false, "_components": [{"__id__": 67}, {"__id__": 68}], "_prefab": {"__id__": 69}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "btn": {"__id__": 71}, "txtBetNode": {"__id__": 60}, "imgMask": {"__id__": 67}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "99sGLSHApIa6/KkYHGCgl+", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 75}, {"__id__": 78}], "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "e6s8eTRytHyK4bexchZmJg", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": false, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "f9oMLinRdPMK8fPDPEV8FF", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 79}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "4bLxiA0+VAA6U/Pogo3KSD", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "c171RUaJlCkLj0CBYal2h4", "sync": false}, {"__type__": "cc.Node", "_name": "node_2", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 85}, {"__id__": 100}], "_active": true, "_components": [], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}, {"__id__": 92}], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [{"__id__": 87}], "_active": true, "_components": [{"__id__": 90}], "_prefab": {"__id__": 91}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 85}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 85}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_active": false, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 85}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "btn": {"__id__": 97}, "txtBetNode": {"__id__": 86}, "imgMask": {"__id__": 93}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 85}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "367DYHJXNHSJWD6BnHlyPE", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 101}, {"__id__": 104}], "_active": true, "_components": [{"__id__": 107}], "_prefab": {"__id__": 108}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [{"__id__": 102}], "_prefab": {"__id__": 103}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "6f5R1hQdJHtoQn6P93YCWw", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": false, "_components": [{"__id__": 105}], "_prefab": {"__id__": 106}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "f1uIVRp8BI0qL9q06WLeVY", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 105}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "67XxUWBhFMD4hSfKhF5pSu", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "5eYPH2Bc5Gto+MbxavrwLI", "sync": false}, {"__type__": "cc.Node", "_name": "node_3", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 111}, {"__id__": 126}], "_active": true, "_components": [], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [250, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [{"__id__": 112}, {"__id__": 118}], "_active": true, "_components": [{"__id__": 122}, {"__id__": 123}, {"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [{"__id__": 113}], "_active": true, "_components": [{"__id__": 116}], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 112}, "_children": [], "_active": true, "_components": [{"__id__": 114}], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": false, "_components": [{"__id__": 119}, {"__id__": 120}], "_prefab": {"__id__": 121}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "btn": {"__id__": 123}, "txtBetNode": {"__id__": 112}, "imgMask": {"__id__": 119}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "8cUn3k7YNAkL8y083GG4Sb", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [{"__id__": 127}, {"__id__": 130}], "_active": true, "_components": [{"__id__": 133}], "_prefab": {"__id__": 134}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": true, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "63AJXD035NL4D8csHr+ddh", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": false, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "d1LQerQKJB5I9CrEMKaoGa", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 131}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "cesCLcLvNHLpNjC1bNzeNI", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "85GpJnhBRHOLUqdujqgFSA", "sync": false}, {"__type__": "cc.Node", "_name": "node_4", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 137}, {"__id__": 152}], "_active": true, "_components": [], "_prefab": {"__id__": 161}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [500, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [{"__id__": 138}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_prefab": {"__id__": 151}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [{"__id__": 139}], "_active": true, "_components": [{"__id__": 142}], "_prefab": {"__id__": 143}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 140}], "_prefab": {"__id__": 141}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [], "_active": false, "_components": [{"__id__": 145}, {"__id__": 146}], "_prefab": {"__id__": 147}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "btn": {"__id__": 149}, "txtBetNode": {"__id__": 138}, "imgMask": {"__id__": 145}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 137}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "b22bdmxo1O8Ie2CFPWtd5p", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [{"__id__": 153}, {"__id__": 156}], "_active": true, "_components": [{"__id__": 159}], "_prefab": {"__id__": 160}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 154}], "_prefab": {"__id__": 155}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "50d89xcPlKfbp7qSI8VypM", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [], "_active": false, "_components": [{"__id__": 157}], "_prefab": {"__id__": 158}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "e51e4AeS9Jl5hWvYCCQbwD", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 157}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "3crX+21fRLr5YRZGFs/3do", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "9ajBWMip1Nd5kKbgCVNBcj", "sync": false}, {"__type__": "cc.Node", "_name": "node_5", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 163}, {"__id__": 178}], "_active": true, "_components": [], "_prefab": {"__id__": 187}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-500, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 162}, "_children": [{"__id__": 164}, {"__id__": 170}], "_active": true, "_components": [{"__id__": 174}, {"__id__": 175}, {"__id__": 176}], "_prefab": {"__id__": 177}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [{"__id__": 165}], "_active": true, "_components": [{"__id__": 168}], "_prefab": {"__id__": 169}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}], "_prefab": {"__id__": 167}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 163}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 163}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [], "_active": false, "_components": [{"__id__": 171}, {"__id__": 172}], "_prefab": {"__id__": 173}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 163}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "btn": {"__id__": 175}, "txtBetNode": {"__id__": 164}, "imgMask": {"__id__": 171}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 163}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "16u0l88c5D0J+3yEul5Fah", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 162}, "_children": [{"__id__": 179}, {"__id__": 182}], "_active": true, "_components": [{"__id__": 185}], "_prefab": {"__id__": 186}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_active": true, "_components": [{"__id__": 180}], "_prefab": {"__id__": 181}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "d232Dwu5FLBLQ36jaZyYEp", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_active": false, "_components": [{"__id__": 183}], "_prefab": {"__id__": 184}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "73I00EMD1I5595V71Neosh", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 183}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "0dInTNVbhE8LXPGk+HWEY3", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "b2jEYH0fxHvr8+BlHmUeMf", "sync": false}, {"__type__": "cc.Node", "_name": "node_6", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 189}, {"__id__": 204}], "_active": true, "_components": [], "_prefab": {"__id__": 213}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 188}, "_children": [{"__id__": 190}, {"__id__": 196}], "_active": true, "_components": [{"__id__": 200}, {"__id__": 201}, {"__id__": 202}], "_prefab": {"__id__": 203}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [{"__id__": 191}], "_active": true, "_components": [{"__id__": 194}], "_prefab": {"__id__": 195}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}], "_prefab": {"__id__": 193}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 189}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 189}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [], "_active": false, "_components": [{"__id__": 197}, {"__id__": 198}], "_prefab": {"__id__": 199}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 189}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "btn": {"__id__": 201}, "txtBetNode": {"__id__": 190}, "imgMask": {"__id__": 197}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 189}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "9dUrggMexNOabbVz87RMBd", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 188}, "_children": [{"__id__": 205}, {"__id__": 208}], "_active": true, "_components": [{"__id__": 211}], "_prefab": {"__id__": 212}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 204}, "_children": [], "_active": true, "_components": [{"__id__": 206}], "_prefab": {"__id__": 207}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "186Nebk5JDoLZrLHvduTSJ", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 204}, "_children": [], "_active": false, "_components": [{"__id__": 209}], "_prefab": {"__id__": 210}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "74BiPCD9tHg6ejxwWVh7jR", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 209}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "91rzIFh9pFgpem/D7BoMIu", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "25gSUxVrxL+5MzsRFmB1yy", "sync": false}, {"__type__": "cc.Node", "_name": "node_7", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 215}, {"__id__": 230}], "_active": true, "_components": [], "_prefab": {"__id__": 239}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [{"__id__": 216}, {"__id__": 222}], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}, {"__id__": 228}], "_prefab": {"__id__": 229}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [{"__id__": 217}], "_active": true, "_components": [{"__id__": 220}], "_prefab": {"__id__": 221}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 216}, "_children": [], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 217}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_active": false, "_components": [{"__id__": 223}, {"__id__": 224}], "_prefab": {"__id__": 225}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "btn": {"__id__": 227}, "txtBetNode": {"__id__": 216}, "imgMask": {"__id__": 223}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "e6dTlb2fhCrY/thZiaHyAl", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [{"__id__": 231}, {"__id__": 234}], "_active": true, "_components": [{"__id__": 237}], "_prefab": {"__id__": 238}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 232}], "_prefab": {"__id__": 233}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "2etqRu1nBJErDtaLMK9Ezk", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": false, "_components": [{"__id__": 235}], "_prefab": {"__id__": 236}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 234}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "43Msru5h5DaqXm5LMoFzXk", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 235}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "198xfWf/JM0ZEAnERjaduK", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "acKWras3xK8qP++SDhb1Yr", "sync": false}, {"__type__": "cc.Node", "_name": "node_8", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 241}, {"__id__": 256}], "_active": true, "_components": [], "_prefab": {"__id__": 265}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [250, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 240}, "_children": [{"__id__": 242}, {"__id__": 248}], "_active": true, "_components": [{"__id__": 252}, {"__id__": 253}, {"__id__": 254}], "_prefab": {"__id__": 255}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 241}, "_children": [{"__id__": 243}], "_active": true, "_components": [{"__id__": 246}], "_prefab": {"__id__": 247}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 242}, "_children": [], "_active": true, "_components": [{"__id__": 244}], "_prefab": {"__id__": 245}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 242}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 241}, "_children": [], "_active": false, "_components": [{"__id__": 249}, {"__id__": 250}], "_prefab": {"__id__": 251}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "btn": {"__id__": 253}, "txtBetNode": {"__id__": 242}, "imgMask": {"__id__": 249}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "7aRe79mDJF1r5O186dzeoC", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 240}, "_children": [{"__id__": 257}, {"__id__": 260}], "_active": true, "_components": [{"__id__": 263}], "_prefab": {"__id__": 264}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 258}], "_prefab": {"__id__": 259}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 257}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "08ctQyYMBAnJDLCTBc4IgF", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 256}, "_children": [], "_active": false, "_components": [{"__id__": 261}], "_prefab": {"__id__": 262}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "81iCIfWO9LKbP2Uk6SBeXS", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 261}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "8cBh4IPg9EtIiOjwn36wWf", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "82TMR8a/5Ci7THaOP4o/70", "sync": false}, {"__type__": "cc.Node", "_name": "node_9", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 267}, {"__id__": 282}], "_active": true, "_components": [], "_prefab": {"__id__": 291}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [500, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "_parent": {"__id__": 266}, "_children": [{"__id__": 268}, {"__id__": 274}], "_active": true, "_components": [{"__id__": 278}, {"__id__": 279}, {"__id__": 280}], "_prefab": {"__id__": 281}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "txtBetNode", "_objFlags": 0, "_parent": {"__id__": 267}, "_children": [{"__id__": 269}], "_active": true, "_components": [{"__id__": 272}], "_prefab": {"__id__": 273}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "node_0", "_objFlags": 0, "_parent": {"__id__": 268}, "_children": [], "_active": true, "_components": [{"__id__": 270}], "_prefab": {"__id__": 271}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 269}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f65bae0-588a-4cf9-a6ef-826ab8be77e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "46Abz/tvhFQbKwIDaB5WyI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "38FaMoZ8RIJ7cRzceZRQzM", "sync": false}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 267}, "_children": [], "_active": false, "_components": [{"__id__": 275}, {"__id__": 276}], "_prefab": {"__id__": 277}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b4749c13-ce2d-4d9a-adb2-8e08f41b92eb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "79bwbxq6VLjrA4elCKGLjo", "sync": false}, {"__type__": "cc.Sprite", "_name": "HumanboyBetBtn<Sprite>", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "ddd085cd-67ca-4c7c-80f5-9de107c7b93a"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "pressedSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "hoverSprite": {"__uuid__": "675c11d6-0fea-4290-a367-30c4252e886f"}, "_N$disabledSprite": {"__uuid__": "c3779f3f-ac13-49b0-b6e4-bca2cf450293"}, "_N$target": null, "_id": ""}, {"__type__": "28e03P5/lNCtoDLCLfBLt8P", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "btn": {"__id__": 279}, "txtBetNode": {"__id__": 268}, "imgMask": {"__id__": 275}, "txtOffset": 4, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 267}, "asset": {"__uuid__": "205afde4-3b57-46a4-9c0f-e4b741895676"}, "fileId": "a1R3YcFlRCFpnPN3DCMk1b", "sync": false}, {"__type__": "cc.Node", "_name": "checkBox", "_objFlags": 0, "_parent": {"__id__": 266}, "_children": [{"__id__": 283}, {"__id__": 286}], "_active": true, "_components": [{"__id__": 289}], "_prefab": {"__id__": 290}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 282}, "_children": [], "_active": true, "_components": [{"__id__": 284}], "_prefab": {"__id__": 285}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "db100feb-3907-4dfe-8831-312a50d5c362"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "6drLluOHNHtpnS92tR3AME", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 282}, "_children": [], "_active": false, "_components": [{"__id__": 287}], "_prefab": {"__id__": 288}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "71f6a264-779d-4cb5-82da-6a29f759be8d"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "5ffEadcixLYbDGNu7cAK/w", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 287}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "a8q3EV7I1Gv4Z8MB2uZjP+", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "faqr6Q0+1ADrYXpDa5qgOr", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "b2Pt4k1rZHfJqYwpvzyzB9", "sync": false}, {"__type__": "cc.Node", "_name": "txt_auto_word", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 295}], "_prefab": {"__id__": 296}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-610, -194.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 294}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "续投按钮设置", "_N$string": "续投按钮设置", "_fontSize": 30, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "STHeitiSC", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "ebthdWWvhHDa/X5FFGjj87", "sync": false}, {"__type__": "cc.Node", "_name": "img_line_2", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 298}, {"__id__": 299}], "_prefab": {"__id__": 300}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1240, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.017699999999990723, -229.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.StudioWidget", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 0.06369999999999998, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "1fFTBBT1hKkIBFtMIHOw/n", "sync": false}, {"__type__": "cc.Node", "_name": "opt_setting_group", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 302}], "_prefab": {"__id__": 303}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ToggleGroup", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "allowSwitchOff": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "98v4aN2GtCXaOqzzyQJj9J", "sync": false}, {"__type__": "cc.Node", "_name": "opt_setting_normal", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 305}, {"__id__": 308}], "_active": true, "_components": [{"__id__": 311}], "_prefab": {"__id__": 312}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-580.5, -289.5, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 304}, "_children": [], "_active": true, "_components": [{"__id__": 306}], "_prefab": {"__id__": 307}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "265b9e5b-0bdc-44f1-867f-272a3b1ca435"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "1eRlG1/h9IFZnBd0pzy4mp", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 304}, "_children": [], "_active": true, "_components": [{"__id__": 309}], "_prefab": {"__id__": 310}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 308}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "41aaa86a-e216-419d-92d4-f20c9faec3c8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "dd3ogHfsZEOJbjfO/JROcq", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": true, "toggleGroup": {"__id__": 302}, "checkMark": {"__id__": 309}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "c2rBoNy71KTZ5OO3C42KzG", "sync": false}, {"__type__": "cc.Node", "_name": "opt_setting_advance", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 314}, {"__id__": 317}], "_active": true, "_components": [{"__id__": 320}], "_prefab": {"__id__": 321}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-230.5, -289.5, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "img_bg", "_objFlags": 0, "_parent": {"__id__": 313}, "_children": [], "_active": true, "_components": [{"__id__": 315}], "_prefab": {"__id__": 316}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 314}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "265b9e5b-0bdc-44f1-867f-272a3b1ca435"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "c4ymSjWH5EqKFkdtL1+sE8", "sync": false}, {"__type__": "cc.Node", "_name": "img_check", "_objFlags": 0, "_parent": {"__id__": 313}, "_children": [], "_active": false, "_components": [{"__id__": 318}], "_prefab": {"__id__": 319}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 317}, "_enabled": true, "_materials": [], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "41aaa86a-e216-419d-92d4-f20c9faec3c8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "a58388ee-c6a5-41d7-8594-ae928135d38d"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "c3bN5uYQRMRZX7R9hwzYR9", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 313}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_N$isChecked": false, "toggleGroup": {"__id__": 302}, "checkMark": {"__id__": 318}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "daiZqFnshF6r2VC7+Ou62I", "sync": false}, {"__type__": "cc.Node", "_name": "txt_opt_normal_word", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 323}], "_prefab": {"__id__": 324}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-530.5, -289.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 322}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "普通续投按钮", "_N$string": "普通续投按钮", "_fontSize": 30, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "STHeitiSC", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "5f9q0As55PBI8W46pqSP4W", "sync": false}, {"__type__": "cc.Node", "_name": "txt_opt_advance_word", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 326}], "_prefab": {"__id__": 327}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180.5, -289.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 325}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "高级续投按钮", "_N$string": "高级续投按钮", "_fontSize": 30, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "STHeitiSC", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "70WT1++W1PIaNY37wSDtJt", "sync": false}, {"__type__": "cc.Node", "_name": "txt_opt_advance_extra_word", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 329}], "_prefab": {"__id__": 330}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 171, "g": 203, "b": 179, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 207.4, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9.5, -289.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 328}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "(可自定义续投局数)", "_N$string": "(可自定义续投局数)", "_fontSize": 24, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "STHeitiSC", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "7d7Jbc+3lJdqWJvrI2hUZM", "sync": false}, {"__type__": "cc.Node", "_name": "btn_ensure", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 332}, {"__id__": 333}], "_prefab": {"__id__": 334}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 239, "height": 108}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [490, -287.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 331}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 331}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "8dTjRTafpAIb6Xl9g3wfHS", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6249affa-74c8-465b-aba2-600bc4e69f1c"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "59LqaLXJtPa4v266ZP+s0/", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2338, "_originalHeight": 1080, "_id": ""}, {"__type__": "d79cfjm/BJItrd4sI1jYDMm", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "prefab_dialog": {"__uuid__": "cb0a959e-88e9-493a-a623-d51f68e0c0c4"}, "imgBgRoot": {"__id__": 335}, "btn_close": {"__id__": 18}, "btn_clean": {"__id__": 25}, "btn_ensure": {"__id__": 333}, "opt_setting_normal": {"__id__": 311}, "opt_setting_advance": {"__id__": 320}, "MiniGameAddAdvancedAuto_prefab": {"__uuid__": "960ee452-7ab0-4652-b1e9-04d91ff28935"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3e916837-15d9-47a5-ad93-5cb069616cfa"}, "fileId": "", "sync": false}]