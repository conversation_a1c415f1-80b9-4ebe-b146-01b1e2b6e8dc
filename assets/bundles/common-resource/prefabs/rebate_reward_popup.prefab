[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "rebate_reward_popup", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "popup", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 8}, {"__id__": 19}, {"__id__": 60}, {"__id__": 85}], "_active": false, "_components": [{"__id__": 88}, {"__id__": 89}], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "sprite_ClickBlocker", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 128, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bed62a53-5d56-4f71-9d51-373a21b2b093"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 118, "_originalHeight": 117, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "c0KRj5dMxAvLbsQAQyOiGL", "sync": false}, {"__type__": "cc.Node", "_name": "Node_Lights", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 9}, {"__id__": 12}, {"__id__": 15}], "_active": true, "_components": [], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 84, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "LightColor", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 185, "g": 78, "b": 45, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1441, "height": 1081}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -84, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "92e3c9be-ac53-42da-a39a-28e40e746f25"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "f1HlXE7D9LSalQW+ar1G1s", "sync": false}, {"__type__": "cc.Node", "_name": "Light", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 207, "g": 207, "b": 207, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1128, "height": 842}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_spriteFrame": {"__uuid__": "b5442549-dd38-41eb-9d3c-c217ab3eecc3"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "b1y7Ys3qZPX72h93f0a3Ib", "sync": false}, {"__type__": "cc.Node", "_name": "LightGlow", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 870, "height": 742}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_spriteFrame": {"__uuid__": "cc310b41-d82e-451e-a8da-5deecaed90ef"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "7cDW5jhjRD7ID3ETXnCN6F", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "3eYUnVf6tCKahudZMipX/S", "sync": false}, {"__type__": "cc.Node", "_name": "Node_BehindPanel", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 20}, {"__id__": 23}, {"__id__": 31}, {"__id__": 51}], "_active": true, "_components": [], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 153.639, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 433, "height": 163}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -22, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f4ae8e2b-951d-4dcc-9aff-2e321fac8243"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "196Sb2GkVCg5KPjklZo+Hs", "sync": false}, {"__type__": "cc.Node", "_name": "Node_Grass", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [{"__id__": 24}, {"__id__": 27}], "_active": true, "_components": [], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Grass_L", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.916, "y": 0.17189812030075188}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-162.519, 41.998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "50637e61-64a9-4d92-86a9-14222bb96125"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "92X8Bp8w1PsYfyVBhNTu7I", "sync": false}, {"__type__": "cc.Node", "_name": "Grass_R", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.09645829959514168, "y": 0.19279812030075189}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [151.87, 43.802, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "91e26c0c-11ea-4e5a-afb4-f1e57858e09a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "30thrLj09FLapFGomkv2io", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "ed8NbQKHFO9YnvZt61I8UU", "sync": false}, {"__type__": "cc.Node", "_name": "Node_ConfettiGroup", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [{"__id__": 32}, {"__id__": 35}, {"__id__": 38}, {"__id__": 41}, {"__id__": 44}, {"__id__": 47}], "_active": true, "_components": [], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_1", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 139.84300000000007, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "3ee45ef7-7707-4999-bf62-439b7340f4f2"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 10, "duration": -1, "emissionRate": 35, "life": 0.55, "lifeVar": 1, "_startColor": {"__type__": "cc.Color", "r": 252, "g": 255, "b": 171, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 255, "b": 224, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 133, "b": 0, "a": 52}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 92, "b": 255, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 40, "startSizeVar": 20, "endSize": 30, "endSizeVar": 5, "startSpin": -50, "startSpinVar": 0, "endSpin": -50, "endSpinVar": 100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 500, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 100, "y": -650}, "speed": 0, "speedVar": 0, "tangentialAccel": 100, "tangentialAccelVar": 500, "radialAccel": 100, "radialAccelVar": 100, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "6etVSM385MJ4dcTKwqQs00", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_2", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 139.843, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "28f73f71-b5f5-48ae-ae7c-10562320d70b"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 3, "duration": -1, "emissionRate": 25, "life": 0.2, "lifeVar": 0.7, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 35, "startSizeVar": 50, "endSize": 30, "endSizeVar": 10, "startSpin": -50, "startSpinVar": 0, "endSpin": -50, "endSpinVar": 100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 360, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -1000}, "speed": 50, "speedVar": 190, "tangentialAccel": 0, "tangentialAccelVar": 500, "radialAccel": 0, "radialAccelVar": 200, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "85RgOQpkJMHoXS/bzeqQZY", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_3", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 139.843, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "251e6b35-5e44-4c4f-831d-151abb7b9ac0"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 3, "duration": -1, "emissionRate": 25, "life": 0.5, "lifeVar": 0.7, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 108}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 80, "startSizeVar": 25, "endSize": 40, "endSizeVar": 20, "startSpin": -50, "startSpinVar": 0, "endSpin": 50, "endSpinVar": -50, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 400, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -800}, "speed": 50, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 500, "radialAccel": 150, "radialAccelVar": 50, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "3cLifayKxFW6hrRsQaUu7+", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_4", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 139.843, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "1d52dfa1-460b-48b9-b96e-34adc6829962"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 3, "duration": -1, "emissionRate": 25, "life": 0.2, "lifeVar": 0.7, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 50, "startSizeVar": 100, "endSize": 0, "endSizeVar": 0, "startSpin": -50, "startSpinVar": 0, "endSpin": -50, "endSpinVar": 100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 300, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -1000}, "speed": 50, "speedVar": 190, "tangentialAccel": 0, "tangentialAccelVar": 500, "radialAccel": 0, "radialAccelVar": 200, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "4a4fuIc2BPgI/0R7aeXkMR", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_6", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 139.843, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "56e9c1ac-dc9d-41f1-b7f0-0735825fd36d"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 5, "duration": -1, "emissionRate": 50, "life": 0.2, "lifeVar": 0.7, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 50, "startSizeVar": 100, "endSize": 50, "endSizeVar": 30, "startSpin": -50, "startSpinVar": 0, "endSpin": -50, "endSpinVar": 100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 300, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -1000}, "speed": 50, "speedVar": 190, "tangentialAccel": 0, "tangentialAccelVar": 500, "radialAccel": 0, "radialAccelVar": 200, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "18y9DH5AZOe6RbGwzBH7Z3", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Stars", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -107.146, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "5b7348f5-ec86-4c8e-b1c9-d891d9eabb4c"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 25, "duration": -1, "emissionRate": 75, "life": 0.5, "lifeVar": 2, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 50, "startSizeVar": 25, "endSize": 0, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 0, "endSpin": -47.369998931884766, "endSpinVar": -142.11000061035156, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 10, "y": 10}, "_positionType": 0, "positionType": 0, "emitterMode": 1, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 190.7899932861328, "tangentialAccel": -92.11000061035156, "tangentialAccelVar": 65.79000091552734, "radialAccel": -671.0499877929688, "radialAccelVar": 65.79000091552734, "rotationIsDir": false, "startRadius": 250, "startRadiusVar": 50, "endRadius": 500, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "4b3dF53q9CkbUeSIgTExxE", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "93WxK4AIZEoYJ01N1Va4Vj", "sync": false}, {"__type__": "cc.Node", "_name": "Node_CoinPivot", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [{"__id__": 52}, {"__id__": 55}], "_active": true, "_components": [], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -23.469, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Coins", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 187}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cd105790-a0f7-418f-91ad-b3779f190e9a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "ddsw0dLGNFnYonYGWy/pnP", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Stars_Coins", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 56}], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [7.855, 131.569, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "5b7348f5-ec86-4c8e-b1c9-d891d9eabb4c"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 10, "duration": -1, "emissionRate": 75, "life": 0.5, "lifeVar": 2, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 40, "startSizeVar": 15, "endSize": 30, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 0, "endSpin": -47, "endSpinVar": -100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 150, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 100, "startRadiusVar": 50, "endRadius": 100, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "aeB4vylDFH1r8zOrV9NKYW", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "3d7cUZtwZN/6+ybrqjOIFh", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "01w8R/ZaVG7JD/sUDT2Fwd", "sync": false}, {"__type__": "cc.Node", "_name": "Node_Panel", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 61}, {"__id__": 73}], "_active": true, "_components": [{"__id__": 82}, {"__id__": 83}], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 808, "height": 307.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 168, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "richtext_Reward", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [{"__id__": 62}, {"__id__": 65}, {"__id__": 68}], "_active": true, "_components": [{"__id__": 71}], "_prefab": {"__id__": 72}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 61}, "_children": [], "_active": false, "_components": [{"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-240.025, -75.6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "恭喜获得", "_N$string": "恭喜获得", "_fontSize": 48, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "92NT4mzyVIUqSac6C/0gMd", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 61}, "_children": [], "_active": false, "_components": [{"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96.05, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48.025000000000006, -75.6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "XXX", "_N$string": "XXX", "_fontSize": 48, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "daKlwxhzpN7bx2yQY2XxHS", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 61}, "_children": [], "_active": false, "_components": [{"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [48.025000000000006, -75.6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "金币奖励", "_N$string": "金币奖励", "_fontSize": 48, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "c6A4yd1hFFr5ONe7wqqXxg", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "<color=#ffffff>恭喜获得</c><color=#FFFF00>XXX</c><color=#ffffff>金币奖励</c>", "_N$horizontalAlign": 1, "_N$fontSize": 48, "_N$font": null, "_N$cacheMode": 0, "_N$maxWidth": 660, "_N$lineHeight": 60, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "01Z6ZQxNNEfo+roB73lQsW", "sync": false}, {"__type__": "cc.Node", "_name": "Button_Confirm", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [{"__id__": 74}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}, {"__id__": 80}], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 231, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -201.6, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "确定", "_N$string": "确定", "_fontSize": 56, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "06577UPkcVMGZv7bBDSH0aH", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "textKey": "Confirm", "isCurrency": false, "flag": 0, "concat": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "c6UG5Dyk9JOa3nVq5z3uw1", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f6e10b4d-9438-4c64-a66f-0889b1020d46"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 60.00000000000003, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "c76jCVk2VC7I7BgOGk/7YD", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5f3c230e-f5ca-404f-a01a-a6b00ec38beb"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 808, "height": 307.6}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 50, "_N$paddingBottom": 60, "_N$spacingX": 0, "_N$spacingY": 30, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "295osSRf5L0rzPYvZACZkq", "sync": false}, {"__type__": "cc.Node", "_name": "particlesystem_Confetti_7", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 86}], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 293.482, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "c4d5837a-3f5b-4763-b32e-2b3e3e854f40"}, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": false, "totalParticles": 5, "duration": -1, "emissionRate": 50, "life": 0.2, "lifeVar": 0.7, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 224, "b": 0, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 255, "b": 20, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 0, "g": 143, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 0}, "angle": 360, "angleVar": 360, "startSize": 20, "startSizeVar": 20, "endSize": 30, "endSizeVar": 10, "startSpin": -50, "startSpinVar": 0, "endSpin": -50, "endSpinVar": 100, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 300, "y": 100}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -1000}, "speed": 50, "speedVar": 190, "tangentialAccel": 0, "tangentialAccelVar": 500, "radialAccel": 0, "radialAccelVar": 200, "rotationIsDir": true, "startRadius": 60, "startRadiusVar": 20, "endRadius": 160, "endRadiusVar": 50, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "01JEaw5+5IC6RKdZysO47e", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "4cde9160-8966-4f44-9a3c-fd7e21c0a2bb"}, {"__uuid__": "44609246-227d-4c84-8065-6d66ee464d22"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 118, "_originalHeight": 117, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "9e+BLDNh1N6K37ZkrdK/ET", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 118, "_originalHeight": 117, "_id": ""}, {"__type__": "88a0c5AlrJDi4gDNdYl4Cbb", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "sureButton": {"__id__": 79}, "cancelButton": null, "titleLabel": null, "content": {"__id__": 71}, "popUpNode": {"__id__": 2}, "rewardAnim": {"__id__": 88}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "88ec0ac1-b955-478d-bc95-2c105bd1c701"}, "fileId": "", "sync": false}]