// import type { CommonDialogControl } from './components/CommonDialogControl';
import type {
    CommonDialogControl,
    ShopControl,
    ToastMessageControl
    // CalmDownDialogControl
} from './components/common-resource-components-index';
import ErrorMessageControl from './common/mini-game-error-message-control';
import type { DialogHubControl } from './components/DialogHubControl';

export class CommonResourceAgent {
    _commonDialog: CommonDialogControl;
    _shop: ShopControl;
    _toastMessage: ToastMessageControl;
    _errorMessage: ErrorMessageControl;
    // _calmDownDialog: CalmDownDialogControl;
    _dialogHub: DialogHubControl;

    constructor() {
        cc.log('CommonResourceAgent constructor');
    }

    get commonDialog(): CommonDialogControl {
        return this._commonDialog;
    }

    set commonDialog(value: CommonDialogControl) {
        this._commonDialog = value;
    }

    get shop(): ShopControl {
        return this._shop;
    }

    set shop(value: ShopControl) {
        this._shop = value;
    }

    get toastMessage(): ToastMessageControl {
        return this._toastMessage;
    }

    set toastMessage(value: ToastMessageControl) {
        this._toastMessage = value;
    }

    initializeErrorMessageControl() {
        this._errorMessage = new ErrorMessageControl();
    }

    // get calmDownDialog(): CalmDownDialogControl {
    //     return this._calmDownDialog;
    // }

    // set calmDownDialog(value: CalmDownDialogControl) {
    //     this._calmDownDialog = value;
    // }

    get dialogHub(): DialogHubControl {
        return this._dialogHub;
    }

    set dialogHub(value: DialogHubControl) {
        this._dialogHub = value;
    }
}

export const commonResourceAgent = new CommonResourceAgent();
