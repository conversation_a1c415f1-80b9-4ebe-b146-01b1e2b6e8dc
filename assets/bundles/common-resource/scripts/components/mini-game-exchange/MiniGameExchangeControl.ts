/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/common-resource-macros';
import USDTAndCoinControl from './USDTAndCoinControl';
import * as cr from '../../../../common-resource/scripts/common-resource';

/**
 * 退出面板
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameExchangeControl extends USDTAndCoinControl {
    @property(cc.Layout)
    topLayout: cc.Layout = null;

    @property(cc.Sprite)
    titleSprite: cc.Sprite = null;

    @property(cc.Node)
    questionNode: cc.Node = null;

    @property(cc.Node)
    explainNode: cc.Node = null;

    @property(cc.Label)
    usdtText: cc.Label = null;

    @property(cc.Label)
    usdtBalanceText: cc.Label = null;

    @property(cc.Label)
    userBalanceNum: cc.Label = null;

    private _atlas_hb_exchange: cc.SpriteAtlas = null;

    onLoad() {
        super.onLoad();
        cr.UIUtil.adaptWidget(this.node);
    }

    start() {
        super.start();
        this.switchTab(0);
    }

    initLanguage() {
        this.coinNode.getChildByName('input_node').getChildByName('txt_coin').getComponent(cc.Label).string =
            pf.languageManager.getString('USDTView_coin_label');
        this._atlas_hb_exchange = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_EXCHANGE_ATLAS);
        this.titleSprite.spriteFrame = this._atlas_hb_exchange.getSpriteFrame('title_change_coin');

        this.topLayout.updateLayout();
        let srcPos: cc.Vec2 = cc.Vec2.ZERO;
        let dstPos: cc.Vec2 = cc.Vec2.ZERO;
        this.questionNode.convertToWorldSpaceAR(cc.Vec2.ZERO, srcPos);
        this.explainNode.convertToNodeSpaceAR(srcPos, dstPos);
        cc.find('exchange_explain_top', this.explainNode).x = dstPos.x;
        this.usdtBalanceText.string = pf.languageManager.getString('USDTView_usdt_balance_label');
        this.usdtText.string = pf.languageManager.getString('USDTView_txt_usdt');
        super.initLanguage();
    }

    initExplanTxtLanguage(): void {
        this.explanText.destroyAllChildren();
        this.explanText.removeAllChildren(true);
        let explanTxt: string = pf.languageManager.getString('USDTView_explan_label');
        let explanTxtArr: string[] = explanTxt.split('\n');
        if (explanTxtArr.length === 0) {
            return;
        }
        let tempNode: cc.Node = this.explainNode.getChildByName('txt_item');
        for (const txt of explanTxtArr) {
            let itemNode: cc.Node = cc.instantiate(tempNode);

            let size: cc.Size = cr.UIUtil.updateAndMeasureLabel(itemNode.children[1].getComponent(cc.Label), txt);
            itemNode.height = size.height;
            itemNode.active = true;
            this.explanText.addChild(itemNode);
        }
    }

    getTabExBtnLanguage(): string {
        return pf.languageManager.getString('USDTView_exchange_btn_label');
    }

    initCoinAndUSDT() {
        this.userBalanceNum.string = cr.CurrencyUtil.convertServerAmountToDisplayString(
            this._walletService.getWallet().usdt
        );
    }

    updateCoinAndUSDT(wallet: pf.services.Wallet) {
        this.userBalanceNum.string = cr.CurrencyUtil.convertServerAmountToDisplayString(wallet.usdt);
    }

    onUSDT2CoinEx(evt: cc.Event) {
        this.onHiddenExplain();
        super.onUSDT2CoinEx(evt);
    }

    onUSDT2CoinAll(evt: cc.Event) {
        this.onHiddenExplain();
        super.onUSDT2CoinAll(evt);
    }

    openView(): void {
        this.node.active = true;
        this.resetTabTestExNum(0);
        this.onHiddenExplain();

        this.requestTabHuilv(0);
    }

    onClose(): void {
        this.node.active = false;
    }

    onShowExplain(): void {
        this.explainNode.active = true;
    }

    onHiddenExplain(): void {
        this.explainNode.active = false;
    }
}
