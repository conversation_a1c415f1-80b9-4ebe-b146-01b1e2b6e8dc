/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

@ccclass
export default class USDTAndCoinControl extends cc.Component {
    @property(cc.Node) coinNode: cc.Node = null; // tab 0 - 兑换金币节点

    @property(cc.Node) usdtNode: cc.Node = null; // tab 1 - 兑换USDT节点

    @property(cc.Node) explanText: cc.Node = null;

    protected usdt_2_coin_ex_val: number = 0;

    protected coin_2_usdt_ex_val: number = 0;

    protected usdt_2_coin_ex: string = '';

    protected coin_2_usdt_ex: string = '';

    protected select_index: number = -1;

    protected usdt_ex_min: number = 2000;

    protected usdt_ex_max: number = 100000000;

    protected open_use_point: boolean = false;

    _boundChangeLanguage = this.initLanguage.bind(this);

    protected _boundUpdateUserData = this.updateCoinAndUSDT.bind(this);

    protected _walletService: pf.services.WalletService = null;

    protected _exchangeCurrencyService: pf.services.ExchangeCurrencyService = null;

    onLoad() {
        this._walletService = pf.serviceManager.get(pf.services.WalletService);
        this._exchangeCurrencyService = pf.serviceManager.get(pf.services.ExchangeCurrencyService);
    }

    start() {
        this.registerMsg();

        this.initLanguage();

        this.initCoinAndUSDT();
    }

    OnEnable() {}

    onDestroy() {
        pf.languageManager.removeListener('languageChange', this._boundChangeLanguage);

        this._walletService.removeListener('userGoldNum', this._boundUpdateUserData);
    }

    registerMsg() {
        pf.languageManager.addListener('languageChange', this._boundChangeLanguage);

        this._walletService.addListener('userGoldNum', this._boundUpdateUserData);
    }

    initLanguage() {
        this.initExplanTxtLanguage(null);

        this.initTabLanguage(0);

        this.initTabLanguage(1);
    }

    initTabLanguage(index: number) {
        let tabNode: cc.Node = this.getTabNode(index);

        if (!tabNode) {
            return;
        }

        let inputNode: cc.Node = cc.find('input_node', tabNode);

        // conversion_exchange
        let srcEditBox: cc.EditBox =
            index === 0
                ? this.getUSDTEditBoxNode(tabNode).getComponent(cc.EditBox)
                : this.getCoinEditBoxNode(tabNode).getComponent(cc.EditBox);

        let dstEditBox: cc.EditBox =
            index === 0
                ? this.getCoinEditBoxNode(tabNode).getComponent(cc.EditBox)
                : this.getUSDTEditBoxNode(tabNode).getComponent(cc.EditBox);

        srcEditBox.placeholder = pf.languageManager.getString('USDTView_input_num_label');

        dstEditBox.placeholder = pf.languageManager.getString('USDTView_exchange_num_label');

        this.updateTabExHuilv(index);
    }

    initExplanTxtLanguage(content: string): void {
        this.explanText.getComponent(cc.Label).string = pf.languageManager.getString('USDTView_explan_label');
    }

    getTabExBtnLanguage(): string {
        return pf.languageManager.getString('USDTView_exchange_label');
    }

    getTabNode(index: number): cc.Node {
        if (index !== 0 && index !== 1) {
            return null;
        }

        return index === 0 ? this.coinNode : this.usdtNode;
    }

    getTabExHuilv(index: number): string {
        if (index !== 0 && index !== 1) {
            return '';
        }

        return index === 0 ? this.usdt_2_coin_ex : this.coin_2_usdt_ex;
    }

    getUSDT2CoinExRate(): number {
        if (isNaN(this.usdt_2_coin_ex_val) || this.usdt_2_coin_ex_val <= 0) {
            return 0;
        }

        return this.usdt_2_coin_ex_val;
    }

    getCoin2USDTExRate(): number {
        if (isNaN(this.coin_2_usdt_ex_val) || this.coin_2_usdt_ex_val <= 0) {
            return 0;
        }

        return this.coin_2_usdt_ex_val;
    }

    initCoinAndUSDT() {}

    updateCoinAndUSDT(wallet: pf.services.Wallet) {}

    updateTabExHuilv(index: number) {
        let tabNode: cc.Node = this.getTabNode(index);

        if (tabNode) {
            let str = pf.StringUtil.formatC(
                pf.languageManager.getString('USDTView_usdt_coin_ex_label_' + index),
                this.getTabExHuilv(index)
            );

            let txt_huilv = cc.find('txt_huilv', tabNode);

            if (txt_huilv) {
                txt_huilv.getComponent(cc.Label).string = str;
            } else {
                cc.find('exchange_data/txt_huilv', tabNode).getComponent(cc.Label).string = str;
            }
        }
    }

    updateTabTestExNum(srcEditBox: cc.EditBox, dstEditBox: cc.EditBox, hlNum: number, positive: boolean = true) {
        if (!srcEditBox || !dstEditBox) {
            return;
        }

        let inputVal: string = srcEditBox.string;

        if (!this.checkTestExNumber(inputVal) || isNaN(hlNum) || hlNum <= 0 || inputVal === '') {
            dstEditBox.string = '';
            return;
        }

        let exNum: number = parseFloat(inputVal);

        if (isNaN(exNum) || exNum <= 0) {
            dstEditBox.string = '0';
        } else {
            exNum = cr.CurrencyUtil.convertToServerAmount(exNum);

            exNum = Math.floor(exNum); // coin/usdt在服务端都是一个整数存储

            let num: number = hlNum * exNum;

            num = positive ? Math.floor(num) : Math.ceil(num); // coin/usdt在服务端都是一个整数存储

            dstEditBox.string = cr.CurrencyUtil.serverAmountToDisplayString(num);
        }
    }

    updateTabTestExNumPositive(index: number) {
        let tabNode: cc.Node = this.getTabNode(index);

        if (!tabNode) {
            return;
        }

        let srcEditBox: cc.EditBox =
            index === 0
                ? this.getUSDTEditBoxNode(tabNode).getComponent(cc.EditBox)
                : this.getCoinEditBoxNode(tabNode).getComponent(cc.EditBox);

        let dstEditBox: cc.EditBox =
            index === 0
                ? this.getCoinEditBoxNode(tabNode).getComponent(cc.EditBox)
                : this.getUSDTEditBoxNode(tabNode).getComponent(cc.EditBox);

        let hlNum: number = index === 0 ? this.getUSDT2CoinExRate() : this.getCoin2USDTExRate();

        this.updateTabTestExNum(srcEditBox, dstEditBox, hlNum);
    }

    resetTabTestExNum(index: number) {
        let tabNode: cc.Node = this.getTabNode(index);

        if (tabNode) {
            this.getUSDTEditBoxNode(tabNode).getComponent(cc.EditBox).string = '';

            this.getCoinEditBoxNode(tabNode).getComponent(cc.EditBox).string = '';
        }
    }

    switchTab(index: number) {
        if ((index !== 0 && index !== 1) || this.select_index === index) {
            return;
        }

        this.select_index = index;

        if (this.coinNode) {
            this.coinNode.active = index === 0;
        }

        if (this.usdtNode) {
            this.usdtNode.active = index === 1;
        }

        this.resetTabTestExNum(index);

        this.requestTabHuilv(index);
    }

    fixTabTestExNum(editbox: cc.EditBox) {
        if (!editbox) {
            return;
        }

        let inputVal: string = editbox.string;

        if (!this.checkTestExNumber(inputVal)) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.languageManager.getString('USDTView_input_invalid_num_label')
            );

            return;
        }

        let dotIndex: number = inputVal.indexOf('.');

        if (dotIndex !== -1) {
            let lastIndex: number = inputVal.length - 1;
            if (lastIndex - dotIndex >= 3) {
                editbox.string = inputVal.slice(0, dotIndex + 3);
            }
        }
    }

    onTestUSDT2Coin(text: string, editbox: cc.EditBox, customEventData: string) {
        let tabIndex: number = parseInt(customEventData, macros.RADIX_DECIMAL);

        if (isNaN(tabIndex) || (tabIndex !== 0 && tabIndex !== 1)) {
            return;
        }

        let srcEditBox: cc.EditBox = editbox;

        let dstEditBox: cc.EditBox = this.getCoinEditBoxNode(this.getTabNode(tabIndex)).getComponent(cc.EditBox);

        // 2个汇率,同界面使用相同汇率
        let hlNum: number =
            tabIndex === 0
                ? this.getUSDT2CoinExRate()
                : this.getCoin2USDTExRate() === 0
                ? 0
                : 1 / this.getCoin2USDTExRate();

        let positive: boolean = tabIndex === 0;

        this.fixTabTestExNum(srcEditBox);

        this.updateTabTestExNum(srcEditBox, dstEditBox, hlNum, positive);
    }

    onTestCoin2USDT(text: string, editbox: cc.EditBox, customEventData: string) {
        let tabIndex: number = parseInt(customEventData, macros.RADIX_DECIMAL);

        if (isNaN(tabIndex) || (tabIndex !== 0 && tabIndex !== 1)) {
            return;
        }

        let srcEditBox: cc.EditBox = editbox;

        let dstEditBox: cc.EditBox = this.getUSDTEditBoxNode(this.getTabNode(tabIndex)).getComponent(cc.EditBox);

        let hlNum: number =
            tabIndex === 1
                ? this.getCoin2USDTExRate()
                : this.getUSDT2CoinExRate() === 0
                ? 0
                : 1 / this.getUSDT2CoinExRate();

        let positive: boolean = tabIndex === 1;

        this.fixTabTestExNum(srcEditBox);

        this.updateTabTestExNum(srcEditBox, dstEditBox, hlNum, positive);
    }

    onEditBegin(text: string, editbox: cc.EditBox, customEventData: string) {}

    checkTestExNumber(inputVal: string): boolean {
        let reg = /(^[1-9]\d*(\.\d{0,})?$)|(^0(\.\d{0,})?$)/;

        return reg.test(inputVal);
    }

    checkExNumber(inputVal: string): boolean {
        let reg = /(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/;

        return reg.test(inputVal);
    }

    onUSDT2CoinEx(evt: cc.Event) {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        let inputVal: string = this.getUSDTEditBoxNode(this.coinNode).getComponent(cc.EditBox).string;

        let exNum: number = parseFloat(inputVal);

        if (!this.checkExNumber(inputVal) || isNaN(exNum) || exNum <= 0) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.languageManager.getString('USDTView_input_invalid_num_label')
            );

            return;
        }

        exNum = cr.CurrencyUtil.convertToServerAmount(exNum);

        exNum = Math.floor(exNum);

        if (this._walletService.getWallet().usdt < exNum) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString('ServerErrorCode251'));

            return;
        }

        if (exNum < this.usdt_ex_min || exNum > this.usdt_ex_max) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString('ServerErrorCode253'));

            return;
        }

        this._exchangeCurrencyService
            .exchangeCurrency(1, exNum)
            .then((resp) => {
                this.onTabExResponse(resp);
            })
            .catch((error: Error) => {
                if (error instanceof pf.ServerError) {
                    cc.log('exchange usdt to coin error', error.errorCode);
                } else {
                    cc.log('exchange usdt to coin error', error.message);
                }
            });

        this.resetTabTestExNum(0);
    }

    onCoin2USDTEx(evt: cc.Event) {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        let inputVal: string = (
            this.getCoinEditBoxNode(this.usdtNode)
                ? this.getCoinEditBoxNode(this.usdtNode)
                : cc.find('input_node/input/editbox_coin', this.usdtNode)
        ).getComponent(cc.EditBox).string;

        let exNum: number = parseFloat(inputVal);

        if (!this.checkExNumber(inputVal) || isNaN(exNum) || exNum <= 0) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.languageManager.getString('USDTView_input_invalid_num_label')
            );

            return;
        }

        exNum = cr.CurrencyUtil.convertToServerAmount(exNum);

        exNum = Math.floor(exNum);

        if (this._walletService.getWallet().goldNum < exNum) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString('ServerErrorCode172'));

            return;
        }

        if (this.getCoin2USDTExRate() > 0) {
            let exNumAfter: number = Math.floor(exNum * this.getCoin2USDTExRate());

            if (exNumAfter < this.usdt_ex_min || exNumAfter > this.usdt_ex_max) {
                cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString('ServerErrorCode253'));

                return;
            }
        }

        this._exchangeCurrencyService
            .exchangeCurrency(0, exNum, this.open_use_point)
            .then((resp) => {
                this.onTabExResponse(resp);
            })
            .catch((error: Error) => {
                if (error instanceof pf.ServerError) {
                    cc.log('exchange coin to usdt error', error.errorCode);
                } else {
                    cc.log('exchange coin to usdt error', error.message);
                }
            });

        this.resetTabTestExNum(1);
    }

    getCoinEditBoxNode(nodeParam: cc.Node) {
        if (nodeParam.name === 'usdt_coin') return cc.find('input_node/to/input/editbox_coin', this.coinNode);

        if (nodeParam.name === 'node_coin') return cc.find('input_node/editbox_coin', this.coinNode);

        return cc.find('input_node/from/input/editbox_coin', this.usdtNode);
    }

    getUSDTEditBoxNode(nodeParam: cc.Node) {
        if (nodeParam.name === 'usdt_coin') return cc.find('input_node/from/input/editbox_usdt', this.coinNode);

        if (nodeParam.name === 'node_coin') return cc.find('input_node/editbox_usdt', this.coinNode);

        return cc.find('input_node/to/input/editbox_usdt', this.usdtNode);
    }

    onTabExResponse(msg: any) {
        let strFormat: string = msg.op_type === 1 ? 'USDTView_ex_coin_success_label' : 'USDTView_ex_usdt_success_label';

        let strTo: string = cr.CurrencyUtil.serverAmountToDisplayString(msg.to_amt);

        cr.commonResourceAgent.commonDialog.showMsg(
            pf.StringUtil.formatC(pf.languageManager.getString(strFormat), strTo),
            [pf.languageManager.getString('TipsPanel_sure_button')],
            null
        );
    }

    onUSDT2CoinAll(evt: cc.Event) {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        let srcEditBox: cc.EditBox = this.getUSDTEditBoxNode(this.coinNode).getComponent(cc.EditBox);

        let dstEditBox: cc.EditBox = this.getCoinEditBoxNode(this.coinNode).getComponent(cc.EditBox);

        let hlNum: number = this.getUSDT2CoinExRate();

        const usdt = this._walletService.getWallet().usdt;
        srcEditBox.string = cr.CurrencyUtil.serverAmountToDisplayString(usdt);

        this.updateTabTestExNum(srcEditBox, dstEditBox, hlNum);
    }

    onCoin2USDTAll(evt: cc.Event) {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        let srcEditBox: cc.EditBox = this.getCoinEditBoxNode(this.usdtNode).getComponent(cc.EditBox);

        let dstEditBox: cc.EditBox = this.getUSDTEditBoxNode(this.usdtNode).getComponent(cc.EditBox);

        let hlNum: number = this.getCoin2USDTExRate();

        srcEditBox.string = cr.CurrencyUtil.serverAmountToDisplayString(this._walletService.getWallet().goldNum);

        this.updateTabTestExNum(srcEditBox, dstEditBox, hlNum);
    }

    requestTabHuilv(index: number) {
        // 0-获取rmb到usdt汇率 1-获取usdt到rmb的汇率
        let hlVal: string = index === 0 ? this.usdt_2_coin_ex : this.coin_2_usdt_ex;

        if (hlVal === '') {
            let hlType: number = index === 0 ? 1 : 0;

            this._exchangeCurrencyService
                .getScalerQuote(hlType)
                .then((resp) => {
                    this.requestExRateResponse(resp);
                })
                .catch((error: Error) => {
                    if (error instanceof pf.ServerError) {
                        cc.log('getScalerQuote error', error.errorCode);
                    } else {
                        cc.log('getScalerQuote error', error.message);
                    }
                });
        } else {
            this.updateTabExHuilv(index);

            this.updateTabTestExNumPositive(index);
        }
    }

    onTabHuilvResponse(msg: any) {
        if (msg.op_type === 0) {
            this.updateTabExHuilv(1);

            this.updateTabTestExNumPositive(1);
        } else if (msg.op_type === 1) {
            this.updateTabExHuilv(0);

            this.updateTabTestExNumPositive(0);
        }
    }

    requestExRateResponse(msg: any): void {
        let rateNum = Number(msg.rate);

        if (isNaN(rateNum) || rateNum <= 0 || (msg.op_type !== 1 && msg.op_type !== 0)) {
            return;
        }

        if (msg.op_type === 0) {
            this.coin_2_usdt_ex_val = rateNum;

            this.coin_2_usdt_ex = this.formatExRate(rateNum);
        } else {
            this.usdt_2_coin_ex_val = rateNum;

            this.usdt_2_coin_ex = this.formatExRate(rateNum);
        }

        this.onTabHuilvResponse(msg);
    }

    formatExRate(rateNum: number): string {
        if (isNaN(rateNum) || rateNum <= 0) {
            return '';
        }

        return pf.MathUtil.floorToDecimalPlaces(rateNum, 2).toString();
    }
}
