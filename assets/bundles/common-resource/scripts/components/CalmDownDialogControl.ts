import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
import type { CalmDownParams } from '../../../../poker-framework/scripts/services/services-index';

const { ccclass, property } = cc._decorator;

@ccclass
export class CalmDownDialogControl extends cc.Component {
    @property(cc.Label) title: cc.Label = null; // 标题

    @property(cc.Label) desc1: cc.Label = null; // 描述1
    @property(cc.Label) desc2: cc.Label = null; // 描述2
    @property(cc.Node) icon: cc.Node = null; // 图标
    @property(cc.Label) txtTimeDown: cc.Label = null; // 描述2
    @property(cc.Node) btnCancel: cc.Node = null; // 继续游戏按钮
    @property(cc.Node) btnQuit: cc.Node = null; // 退出游戏按钮

    private CalmDownDeadLineTimeStamp: number = 0; // 冷静到期时间戳
    private calmDownLeftSeconds: number = 0; // 游戏内冷静剩余倒计时

    private _boundAppEnterBackground = this.OnAppEnterBackground.bind(this);
    private _boundAppEnterForeground = this.OnAppEnterForeground.bind(this);

    private _calmDownService: pf.services.CalmDownService = null;

    autoShow(data: CalmDownParams): void {
        this.adjustScreen();

        this.calmDownLeftSeconds = data.calmDownLeftSeconds;
        this.CalmDownDeadLineTimeStamp = data.calmDownDeadLineTimeStamp;

        this.updateCalmDownTime();
        // 倒计时
        this.unscheduleAllCallbacks();
        this.schedule(() => {
            // 这里的 this 指向 component
            this.updateCalmDownTime();
        }, 1);

        this.node.active = true;
    }

    onLoad() {
        this.initLanguage();
        pf.app.events().addListener('appEnterBackground', this._boundAppEnterBackground);
        pf.app.events().addListener('appEnterForeground', this._boundAppEnterForeground);

        this._calmDownService = pf.serviceManager.get(pf.services.CalmDownService);

        this.node.active = false;
    }

    onDestroy() {
        pf.app.events().removeListener('appEnterBackground', this._boundAppEnterBackground);
        pf.app.events().removeListener('appEnterForeground', this._boundAppEnterForeground);
    }

    /**
     * 游戏进入后台时触发的事件
     */
    OnAppEnterBackground(): void {
        console.log('OnAppEnterBackground popSilence.');
    }

    /**
     * 游戏进入前台运行时触发的事件
     */
    OnAppEnterForeground(): void {
        console.log('OnAppEnterForeground popSilence.');
        // 从后台切回来，定时器暂停，倒计时需要校正一下
        let time = Date.now();
        let diffTime = this.CalmDownDeadLineTimeStamp - time / 1000;
        if (diffTime > 0) {
            this.calmDownLeftSeconds = diffTime;
        } else {
            this.closeDlg();
        }
    }

    // start() {}

    private initLanguage() {
        this.title.string = pf.languageManager.getString('pop_silence_title');
        this.btnCancel.getChildByName('Background').getChildByName('Label').getComponent(cc.Label).string =
            pf.languageManager.getString('pop_silence_btn_continue');
        this.btnQuit.getChildByName('Background').getChildByName('Label').getComponent(cc.Label).string =
            pf.languageManager.getString('pop_silence_btn_quit');
        this.desc1.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('pop_silence_tips1')
        );
        this.desc2.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('pop_silence_tips2')
        );

        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            this.desc1.fontSize = 48;
        } else {
            this.desc1.fontSize = 44;
        }
    }

    private updateCalmDownTime() {
        this.txtTimeDown.getComponent(cc.Label).string =
            '（' + pf.TimeUtil.formatCountDownTime(this.calmDownLeftSeconds, pf.eTimeType.Hour_Min_Sec) + '）';
        if (this.calmDownLeftSeconds <= 0) {
            // 倒计时为0的时候 关闭窗口
            this.closeDlg();
        }

        this.calmDownLeftSeconds--;
    }

    private adjustScreen() {
        // TODO: testing to adjust screen
        if (pf.system.view.isScreenLandscape()) {
            // 如果是横屏
            this.node.setContentSize(cc.size(pf.system.view.designHeight, pf.system.view.designWidth));
        } else {
            this.node.setContentSize(cc.size(pf.system.view.designWidth, pf.system.view.designHeight));
        }
    }

    // 继续游戏 取消冷静状态
    private onBtnCancel() {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this._calmDownService.getCalmDownConfirm(false).catch((error: pf.ServerError) => {
            cc.log('get calm down confirm error:' + error.errorCode);
        });
        this.closeDlg();
    }

    // 退出游戏
    private onBtnQuit() {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this.closeDlg();

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        if (context?.exitCallback) {
            context.exitCallback(pf.client.ExitType.Standard);
        }
    }

    private closeDlg() {
        this.unscheduleAllCallbacks();
        this.node.active = false;
    }
}
