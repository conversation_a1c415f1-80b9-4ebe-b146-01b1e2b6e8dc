import * as pf from '../../../../poker-framework/scripts/pf';
import * as cr from '../../../common-resource/scripts/common-resource';
import { macros } from '../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

class MessageData {
    text: string;
    isLaba: boolean;
}

@ccclass
export class ToastMessageControl extends cc.Component {
    @property(cc.Sprite)
    bgSprite: cc.Sprite = null;

    @property(cc.Label)
    textLabel: cc.Label = null;

    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.Node)
    closeButton: cc.Node = null;

    private _messageQueue: MessageData[] = [];

    private _isBusy: boolean = false;

    private _initPos: cc.Vec2 = cc.v2(0, -45);

    private _bgSprite: cc.Sprite;

    onLoad() {
        this._bgSprite = this.node.getComponent(cc.Sprite);

        this.node.zIndex = macros.ZORDER_TT;

        this.node.active = false;
    }

    showMsg(text: string, isLaba: boolean = false) {
        if (text.length === 0) {
            return;
        }

        const data = new MessageData();
        data.text = text;
        data.isLaba = isLaba;
        this._messageQueue.push(data);

        this.checkQueue();
    }

    private checkQueue() {
        if (this._isBusy) {
            return;
        }

        if (this._messageQueue.length === 0) {
            return;
        }

        const msg = this._messageQueue.shift();

        this._isBusy = true;

        if (pf.system.view.designWidth < pf.system.view.designHeight) {
            pf.addressableAssetManager
                .loadAsset<cc.SpriteFrame>(macros.DynamicAsset.TOAST_BG_PORTRAIT)
                .then((spriteFrame) => {
                    this._bgSprite.spriteFrame = spriteFrame;
                });
            this.node.setContentSize(pf.system.view.width, this.node.height);
        } else {
            pf.addressableAssetManager
                .loadAsset<cc.SpriteFrame>(macros.DynamicAsset.TOAST_BG_LANDSCAPE)
                .then((spriteFrame) => {
                    this._bgSprite.spriteFrame = spriteFrame;
                });
            this.node.setContentSize(pf.system.view.width * 0.5, this.node.height);
        }

        // TODO: deal with safe area
        // const offsetY = cv.SafeAreaWithDifferentDevices.getSafeArea();
        const offsetY = 0;
        const contentSize = cc.Canvas.instance.node.getContentSize();
        this.node.setPosition(contentSize.width * 0.5, contentSize.height - offsetY);

        this.node.active = true;

        this.textLabel.string = msg.text;
        this.textLabel.node.setPosition(this._initPos);

        this.closeButton.active = msg.isLaba;
        this.timeLabel.node.active = msg.isLaba;
        this.timeLabel.fontSize = msg.isLaba ? 30 : 42;
        this.textLabel.fontSize = msg.isLaba ? 30 : 42;

        const textSize = cr.UIUtil.updateAndMeasureLabel(this.textLabel);

        const moveWidth = pf.system.view.designWidth;

        let enterAction = cc.fadeIn(0.3);

        let pauseAction = cc.delayTime(0.7);

        let outAction = cc.fadeOut(1.0);

        let hideAction = cc.callFunc(() => {
            this.node.active = false;

            this._isBusy = false;

            this.checkQueue();
        }, this);

        if (msg.isLaba) {
            let timeLeft = 5;

            // let _timeStr = time_text.getComponent(cc.Label)

            this.timeLabel.string = pf.StringUtil.formatC('(%d)', timeLeft);

            let showTime = cc.delayTime(1);

            let pkCall = cc.callFunc(() => {
                timeLeft--;

                if (timeLeft > 0) {
                    this.timeLabel.string = pf.StringUtil.formatC('(%d)', timeLeft);
                } else {
                    this.endDisplay();
                    this.checkQueue();
                }
            }, this);

            this.timeLabel.node.runAction(cc.repeat(cc.sequence(showTime, pkCall), 6));

            this.closeButton.targetOff(this);

            this.closeButton.on(
                'click',
                (event: cc.Event): void => {
                    this.endDisplay();
                    this.checkQueue();
                },
                this
            );
        }

        this.textLabel.node.runAction(cc.fadeIn(0));

        this.timeLabel.node.setPosition(
            cc.v2(
                this.textLabel.node.getPosition().x +
                    this.textLabel.node.getContentSize().width / 2 +
                    this.timeLabel.node.getContentSize().width / 2,
                this.timeLabel.node.getPosition().y
            )
        );

        this.closeButton.setPosition(
            cc.v2(
                this.node.getBoundingBox().size.width / 2 - this.closeButton.getContentSize().width / 2 - 50,
                this.closeButton.getPosition().y
            )
        );

        if (moveWidth < textSize.width) {
            // let text = content;

            let offsetX = textSize.width - moveWidth + 50;

            let moveTime = msg.isLaba ? 4 : offsetX / 100; // 每分钟移动100像素

            let pos = cc.v2(
                this.textLabel.node.getPosition().x + offsetX / 2 + 10,
                this.textLabel.node.getPosition().y
            );

            this.textLabel.node.setPosition(pos);

            let moveAction = cc.moveTo(moveTime, cc.v2(pos.x - offsetX, pos.y));

            if (!msg.isLaba) {
                const beginTime = enterAction.getDuration() + pauseAction.getDuration();

                this.textLabel.node.runAction(cc.sequence(cc.delayTime(beginTime), moveAction));

                this.node.runAction(
                    cc.sequence(
                        enterAction,
                        pauseAction,
                        cc.delayTime(moveTime),
                        cc.delayTime(0.3),
                        outAction,
                        hideAction
                    )
                );
            }
        } else {
            if (msg.isLaba) {
                this.node.runAction(cc.sequence(enterAction, pauseAction));
            } else {
                this.node.runAction(cc.sequence(enterAction, pauseAction, outAction, hideAction));
            }
        }
    }

    private endDisplay() {
        this.timeLabel.node.stopAllActions();
        this.textLabel.node.stopAllActions();
        this.node.stopAllActions();
        this.node.active = false;

        this._isBusy = false;
    }
}
