/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';

const { ccclass, property, disallowMultiple, menu } = cc._decorator;

export enum i18nFlagType {
    Default,
    Uppercase_First_Letter,
    Uppercase_All,
    Lowercase_All,
    Uppercase_Words_First_Letter
}

@ccclass('i18nConcatItemControl')
export class i18nConcatItemControl {
    @property({ tooltip: 'used to glue text parts translations' }) glue: string = '';
    @property(cc.String) textKey: string = '';
}

@ccclass
@disallowMultiple
@menu('Custom UI/i18n')
export class i18nTextControl extends cc.Component {
    @property({ tooltip: 'i18n key used for translation\n-use $ for currency' }) textKey: string = '';
    @property(cc.Boolean) isCurrency: boolean = false;
    @property({ type: cc.Enum(i18nFlagType) }) flag: i18nFlagType = i18nFlagType.Default;
    @property({ type: i18nConcatItemControl }) concat: i18nConcatItemControl[] = [];

    private m_label: cc.Label = null;
    private m_editBox: cc.EditBox = null;
    private m_richText: cc.RichText = null;
    private m_lastTextKey: string = '';
    private m_lastLanguage: string = '';

    private _boundChangeLanguage = this.initLanguage.bind(this);

    protected onLoad(): void {
        this.m_label = this.node.getComponent(cc.Label);
        this.m_editBox = this.node.getComponent(cc.EditBox);
        this.m_richText = this.node.getComponent(cc.RichText);

        if (this.textKey === '$') this.isCurrency = true;
    }

    protected onEnable(): void {
        this.refresh();
        pf.languageManager.addListener('languageChange', this._boundChangeLanguage);
        // cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
        // TODO: register to currency change event
    }

    protected onDisable(): void {
        pf.languageManager.removeListener('languageChange', this._boundChangeLanguage);
        // cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        // TODO: unregister from currency change event
    }

    setKey(key: string, forceRefresh: boolean = false): void {
        this.enabled = true;
        if (this.textKey !== key) {
            this.textKey = key;
            this.refresh();
        } else if (forceRefresh) {
            this.refresh();
        }
    }

    /**
     * Set new text provided and disable i18n component
     * @param newText new text to be set
     */
    setText(newText: string): void {
        this.enabled = false;
        this.textKey = '';
        this.m_lastTextKey = '';
        this.setTextInternal(newText);
    }

    private refresh(): void {
        this.initLanguage();
        // this.initCurrency();
        // TODO: find a way to enable in a way that it does not break transitions
        // setTimeout(() => { this.updateWidget(); }, 1);
    }

    updateWidget() {
        if (this.node.getComponent(cc.Widget) !== null) {
            this.node.getComponent(cc.Widget).updateAlignment();
        }
    }

    initLanguage(): void {
        // if (this.textKey.length == 0 || this.isCurrency || cv.config == null) return;
        if (this.textKey.length === 0 || this.isCurrency) return;

        if (this.m_lastTextKey === this.textKey && this.m_lastLanguage === pf.languageManager.currentLanguage) return;
        this.m_lastTextKey = this.textKey;
        this.m_lastLanguage = pf.languageManager.currentLanguage;

        let _translation: string = this.applyFlag(pf.languageManager.getString(this.textKey));
        if (_translation.length === 0) return;

        for (const item of this.concat) {
            if (item.textKey.length > 0) {
                let _concatTrans: string = this.applyFlag(pf.languageManager.getString(item.textKey));
                if (_concatTrans.length > 0) _translation = _translation + item.glue + _concatTrans;
            } else {
                _translation = _translation + item.glue;
            }
        }

        this.setTextInternal(_translation);
    }

    private applyFlag(term: string): string {
        return pf.StringUtil.convertTextCase(term, this.flag);
    }

    initCurrency(): void {
        if (!this.isCurrency) return;

        // not implemented in pkw
        // this.setTextInternal(cv.dataHandler.getUserData().getCurrencyCode());
    }

    protected setTextInternal(newText: string): void {
        if (this.m_label === null && this.m_editBox === null && this.m_richText === null) {
            this.onLoad();
        }

        if (this.m_label !== null) this.m_label.string = newText;
        else if (this.m_editBox !== null) this.m_editBox.placeholder = newText;
        else if (this.m_richText !== null) this.m_richText.string = newText;
    }
}
