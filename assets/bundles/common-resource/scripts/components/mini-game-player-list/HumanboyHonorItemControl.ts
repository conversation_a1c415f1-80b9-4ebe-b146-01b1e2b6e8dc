/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/common-resource-macros';

import * as cr from '../../../../common-resource/scripts/common-resource';
import { AvatarControl } from '../../../../common/scripts/components/AvatarControl';
import type { ScrollViewItemControl } from '../../../../common/scripts/components/ScrollViewItemControl';

const { ccclass, property } = cc._decorator;

@ccclass
export class HumanboyHonorItemControl extends cc.Component {
    @property(cc.Node) head_img: cc.Node = null;
    @property(AvatarControl) avatarControl: AvatarControl = null;
    @property(cc.Sprite) rank_img: cc.Sprite = null;
    @property(cc.Sprite) gold_img: cc.Sprite = null;
    @property(cc.Label) name_text: { string: string } = null;
    @property(cc.Label) money_text: { string: string } = null;
    @property(cc.Label) profit_text: { string: string } = null;
    @property(cc.Label) rank_text: { node: { active: boolean }; string: string } = null;
    @property(cc.Label) des_text: { node: { opacity: number; color: cc.Color }; string: string } = null;
    @property(cc.Label) day_text: { string: string; node: { opacity: number; color: cc.Color } } = null;

    @property(cc.SpriteAtlas) chart_PLIST: cc.SpriteAtlas = null;
    @property(cc.SpriteAtlas) game_dznz_PLIST: cc.SpriteAtlas = null;

    protected _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room;
    }

    updateSVReuseData(index: number, dataArray: any[], view: ScrollViewItemControl): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;
        let data = dataArray[index];
        if (!(data instanceof pf.services.RankData)) return;
        // if (!(data instanceof RankData)) return;

        // this.msg = data;

        if (index === 0) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.chart_PLIST.getSpriteFrame('chart_honor_1');
        } else if (index === 1) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.chart_PLIST.getSpriteFrame('chart_honor_2');
        } else if (index === 2) {
            this.rank_img.node.active = true;
            this.rank_text.node.active = false;
            this.rank_img.spriteFrame = this.chart_PLIST.getSpriteFrame('chart_honor_3');
        } else {
            this.rank_img.node.active = false;
            this.rank_text.node.active = true;
            this.rank_text.string = cr.CurrencyUtil.clientAmountToDisplayString(index + 1);
        }

        this.name_text.string = data.name;
        this.money_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(data.coin)
        );

        let xx = parseInt(view.name, macros.RADIX_DECIMAL);
        this.des_text.node.opacity = 153;
        this.des_text.node.color = cc.Color.WHITE;

        if (xx === 1 || xx === 3) {
            this.des_text.string = pf.languageManager.getString('Humanboy_list_frequency_time');
            this.profit_text.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_list_frequency'),
                data.frequency
            );
        } else {
            this.des_text.string = pf.languageManager.getString('Humanboy_list_profit');
            this.profit_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
                cr.CurrencyUtil.convertToClientAmount(data.profit)
            );
        }

        this.day_text.string = pf.TimeUtil.formatTime(data.updateAt, pf.eTimeType.Year_Month_Day);

        this.day_text.node.opacity = 153;
        this.day_text.node.color = cc.Color.WHITE;

        let playerInfo = data.uid === this._miniGameRoom.selfPlayer.uid ? this._miniGameRoom.selfPlayer : data;
        const headPath = cr.CommonUtil.getHeadPath(playerInfo, this._miniGameRoom.selfPlayer.uid);
        this.avatarControl.loadHeadImage(headPath, data.plat);
    }
}
