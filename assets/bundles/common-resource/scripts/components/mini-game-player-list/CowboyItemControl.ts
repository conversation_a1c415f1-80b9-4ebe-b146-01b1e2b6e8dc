/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/common-resource-macros';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { AvatarControl } from '../../../../common/scripts/components/AvatarControl';

const { ccclass, property } = cc._decorator;

@ccclass
export class CowboyItemControl extends cc.Component {
    @property(cc.Node) head_img: cc.Node = null;
    @property(AvatarControl) avatarControl: AvatarControl = null;
    @property(cc.Sprite) rank_img: cc.Sprite = null;
    @property(cc.Label) num_text: cc.Label = null;
    @property(cc.Label) name_text: cc.Label = null;
    @property(cc.Label) money_text: cc.Label = null;
    @property(cc.Label) jushu_text: cc.Label = null;
    @property(cc.Label) bet_text: cc.Label = null;
    @property(cc.Label) win_text: cc.Label = null;

    @property(cc.Node) tz_img: cc.Node = null;
    @property(cc.Node) hs_img: cc.Node = null;
    @property(cc.SpriteAtlas) playerlist_PLIST: cc.SpriteAtlas = null;

    protected _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集

    protected _gameRoom: pf.Nullable<pf.services.IGameRoom> = null;

    protected onLoad(): void {
        this.initAtlas();

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._gameRoom = context.room;
    }

    protected initAtlas(): void {
        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_LANGUAGE_ATLAS);
    }

    protected start(): void {}

    updateSVReuseData(index: number, dataArray: any[]): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;

        const player = dataArray[index] as pf.services.GamePlayer;

        this.tz_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_icon_bet');
        this.hs_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_icon_hs');

        this.num_text.string = pf.StringUtil.formatC(pf.languageManager.getString('Cowboy_fuhao_no_text'), index);
        this.num_text.node.active = true;

        if (index > 8) {
            this.rank_img.node.active = true;
            this.rank_img.spriteFrame = this.playerlist_PLIST.getSpriteFrame('fuwen9');
        } else {
            this.rank_img.node.active = true;
            if (index === 0) {
                this.rank_img.getComponent(cc.Sprite).spriteFrame =
                    this.playerlist_PLIST.getSpriteFrame('playerlist_ssz');
                this.num_text.string = pf.languageManager.getString('Cowboy_shensuanzi_text');
            } else {
                this.rank_img.getComponent(cc.Sprite).spriteFrame = this.playerlist_PLIST.getSpriteFrame('fuwen');
            }
        }

        this.jushu_text.node.opacity = 127;
        this.jushu_text.node.color = cc.color(255, 255, 255, 127);

        this.jushu_text.node.getComponent(cc.Label).string = pf.languageManager.getString('Cowboy_last20_text');

        this.name_text.string = player.name;
        this.bet_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(player.totalBetAmount)
        );
        this.win_text.string = cr.CurrencyUtil.clientAmountToDisplayString(player.winCount);
        this.money_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(player.curCoin)
        );

        const playerInfo = player.uid === this._gameRoom.selfPlayer.uid ? this._gameRoom.selfPlayer : player;
        const path = cr.CommonUtil.getHeadPath(playerInfo, this._gameRoom.selfPlayer.uid);
        this.avatarControl?.loadHeadImage(path, player.plat);
    }
}
