/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
/**
 * 声音设置面板
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameAudioSettingControl extends cc.Component {
    @property(cc.Sprite) effect_sp: cc.Sprite = null;
    @property(cc.Sprite) music_sp: cc.Sprite = null;
    @property(cc.Sprite) setting_img: cc.Sprite = null;

    @property(cc.Button) btn_confirm: cc.Button = null;
    @property(cc.Button) btn_close: cc.Button = null;
    @property(cc.Button) btn_effect: cc.Button = null; // 音效
    @property(cc.Button) btn_music: cc.Button = null; // 音乐
    @property(cc.SpriteAtlas) atlas_setting: cc.SpriteAtlas = null;
    @property(cc.<PERSON>) isMultiLanguageSupported: boolean = true;

    private isEffect_open: boolean = false;
    private isMusic_open: boolean = false;
    protected _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集

    protected onLoad(): void {
        if (this.isMultiLanguageSupported) {
            this.initAtlas();

            this.setting_img.getComponent(cc.Sprite).spriteFrame =
                this._atlas_cb_language.getSpriteFrame('cowboy_sound_setting');
            this.btn_confirm.getComponent(cc.Sprite).spriteFrame = this._atlas_cb_language.getSpriteFrame('tips_ok');
        }

        this.initSwitch();

        this.btn_confirm.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                this.confirm();
            },
            this
        );
        this.btn_close.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.COMMON_CLOSE);
                this.close();
            },
            this
        );
        this.btn_effect.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.Tab);
                this.effect();
            },
            this
        );
        this.btn_music.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.Tab);
                this.sound();
            },
            this
        );
    }

    protected initAtlas(): void {
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
    }

    sound(): void {
        this.isMusic_open = !this.isMusic_open;
        this._displayMusicSprite();
    }

    effect(): void {
        this.isEffect_open = !this.isEffect_open;
        this._displaySoundSprite();
    }

    private _displayMusicSprite() {
        if (this.isMusic_open) {
            this.music_sp.spriteFrame = this.atlas_setting.getSpriteFrame('sound_open');
        } else {
            this.music_sp.spriteFrame = this.atlas_setting.getSpriteFrame('sound_close');
        }
    }

    private _displaySoundSprite() {
        if (this.isEffect_open) {
            // this.effect_sp.spriteFrame = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_AUDIO_ON_SPRITE);
            this.effect_sp.spriteFrame = this.atlas_setting.getSpriteFrame('sound_open');
        } else {
            // this.effect_sp.spriteFrame = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_AUDIO_OFF_SPRITE);
            this.effect_sp.spriteFrame = this.atlas_setting.getSpriteFrame('sound_close');
        }
    }

    close(): void {
        this.node.active = false;
    }

    initSwitch(): void {
        this.isMusic_open = pf.audioManager.enableMusic;
        this.isEffect_open = pf.audioManager.enalbeSoundEffect;

        this._displayMusicSprite();
        this._displaySoundSprite();
    }

    confirm(): void {
        pf.audioManager.enableMusic = this.isMusic_open;
        pf.audioManager.enalbeSoundEffect = this.isEffect_open;

        pf.localStorage.setItem(macros.AudioSettingKeys.MUSIC, this.isMusic_open);
        pf.localStorage.setItem(macros.AudioSettingKeys.SOUND_EFFECT, this.isEffect_open);

        this.close();
    }
}
