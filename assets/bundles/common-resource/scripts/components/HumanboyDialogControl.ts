/* eslint-disable camelcase */
import type { IMiniGameDialog } from './mini-game-dialog/HumanboyIMiniGameDialog';
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
import * as cr from '../../../common-resource/scripts/common-resource';

/**
 * 小游戏通用简易对话框
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class HumanboyDialogControl extends cc.Component implements IMiniGameDialog {
    @property(cc.Sprite) img_title: cc.Sprite = null;
    @property(cc.Label) txt_content: cc.Label = null;
    @property(cc.Button) btn_left: cc.Button = null;
    @property(cc.Button) btn_right: cc.Button = null;
    @property(cc.Button) btn_close: cc.Button = null;

    protected _cb_left: (dialog: IMiniGameDialog) => void = null;
    protected _cb_right: (dialog: IMiniGameDialog) => void = null;
    protected _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集

    protected onLoad(): void {
        this.initAtlas();
        this.img_title.spriteFrame = this._atlas_cb_language.getSpriteFrame('tips_img');

        this.btn_left.node.on('click', (sender: cc.Node): void => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

            if (this._cb_left) {
                this._cb_left(this);
                this.close();
            }
        });

        this.btn_right.node.on('click', (sender: cc.Node): void => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            if (this._cb_right) {
                this._cb_right(this);
                this.close();
            }
        });

        this.btn_close.node.on('click', (sender: cc.Node): void => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            this.close();
        });

        // 隐藏关闭按钮(暂不开放)
        this.btn_close.node.active = false;
    }

    protected initAtlas() {
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
    }

    protected start(): void {}

    /**
     * 显示对话框
     * @param str_content       - 内容文本
     * @param str_btn_left      - 左按钮文本
     * @param str_btn_right     - 右按钮文本
     * @param cb_left           - 左按钮回调函数
     * @param cb_right          - 右按钮回调函数
     */
    show(
        str_content: string,
        str_btn_left: string,
        str_btn_right: string,
        cb_left: (dialog: IMiniGameDialog) => void,
        cb_right: (dialog: IMiniGameDialog) => void
    ): void {
        this.txt_content.string = cr.UIUtil.calculateAutoWrapString(this.txt_content.node, str_content);
        this._cb_left = cb_left;
        this._cb_right = cb_right;
        this.node.active = true;

        let txt_btn_left: cc.Label = this.btn_left.node.getChildByName('txt').getComponent(cc.Label);
        txt_btn_left.string = str_btn_left;

        let txt_btn_right: cc.Label = this.btn_right.node.getChildByName('txt').getComponent(cc.Label);
        txt_btn_right.string = str_btn_right;
    }

    /**
     * 关闭对话框(直接移除)
     */
    close(): void {
        this.node.removeFromParent(true);
        cr.UIUtil.destroyNode(this.node);
    }

    setLegacyDialog(dialog: IMiniGameDialog): void {}
}
