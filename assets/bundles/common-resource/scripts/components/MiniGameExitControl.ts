/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';

/**
 * 退出面板
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameExitControl extends cc.Component {
    @property(cc.Button) btn_cancel: cc.Button = null;
    @property(cc.Button) btn_ok: cc.Button = null;
    @property(cc.Button) btn_close: cc.Button = null;

    @property(cc.Sprite) setting_img: cc.Sprite = null;
    @property(cc.Node) exit_text: cc.Node = null;
    @property(cc.Boolean) isMultiLanguageSupported = true;

    protected _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集

    protected onLoad(): void {
        if (this.isMultiLanguageSupported) {
            this.initAtlas();

            this.setting_img.getComponent(cc.Sprite).spriteFrame = this._atlas_cb_language.getSpriteFrame('tips_img');
            this.btn_ok.getComponent(cc.Sprite).spriteFrame = this._atlas_cb_language.getSpriteFrame('tips_ok');
            this.btn_cancel.getComponent(cc.Sprite).spriteFrame = this._atlas_cb_language.getSpriteFrame('tips_cancel');

            this.exit_text.getComponent(cc.Label).string = pf.languageManager.getString('CowboyExit_bg_exit_text');
        } else {
            // TODO: implement it? actually not needed in wpk cowboy now...
            // this.exit_text.getComponent(cc.Label).string = pf.languageManager.getString(
            //     'CowboyExit_bg_exit_text',
            //     pf.LANGUAGE_GROUPS.zh_CN
            // );
        }

        this.btn_cancel.node.on(
            'click',
            (event: cc.Event): void => {
                this.onCancel();
            },
            this
        );
        this.btn_ok.node.on(
            'click',
            (event: cc.Event): void => {
                this.onOk();
            },
            this
        );
        this.btn_close.node.on(
            'click',
            (event: cc.Event): void => {
                this.onClose();
            },
            this
        );
    }

    protected initAtlas(): void {
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
    }

    onOk(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        if (context?.exitCallback) {
            context.exitCallback(pf.client.ExitType.Standard);
        }
        this.node.active = false;
    }

    onCancel(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this.node.active = false;
    }

    onClose(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this.node.active = false;
    }
}
