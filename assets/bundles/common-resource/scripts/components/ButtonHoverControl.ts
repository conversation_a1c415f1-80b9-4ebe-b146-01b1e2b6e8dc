// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

@ccclass
export default class ButtonHoverControl extends cc.Component {
    @property(cc.Label)
    label: cc.Label = null;
    @property(cc.LabelOutline)
    outline: cc.LabelOutline = null;

    @property hexNormalColor: string = '#BB7333';
    @property hexNormalOutlineColor: string = '#4D2E1C';

    @property hexHoverColor: string = '#FFF4D0';
    @property hexHoverOutlineColor: string = '#66412A';

    // LIFE-CYCLE CALLBACKS:
    private hoverOutlineColor: cc.Color;
    private normalOutlineColor: cc.Color;
    private hoverColor: cc.Color;
    private normalColor: cc.Color;

    start() {
        this.hoverOutlineColor = new cc.Color().fromHEX(this.hexHoverOutlineColor);
        this.normalOutlineColor = new cc.Color().fromHEX(this.hexNormalOutlineColor);
        this.hoverColor = new cc.Color().fromHEX(this.hexHoverColor);
        this.normalColor = new cc.Color().fromHEX(this.hexNormalColor);
        this._onHoverOut();
    }

    protected onEnable(): void {
        this.node.on(cc.Node.EventType.MOUSE_ENTER, this._onHoverIn, this);
        this.node.on(cc.Node.EventType.MOUSE_LEAVE, this._onHoverOut, this);
        this.node.on(cc.Node.EventType.TOUCH_START, this._onHoverIn, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this._onHoverOut, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this._onHoverOut, this);
    }

    protected onDisable(): void {
        this.node.off(cc.Node.EventType.MOUSE_ENTER, this._onHoverIn, this);
        this.node.off(cc.Node.EventType.MOUSE_LEAVE, this._onHoverOut, this);
        this.node.off(cc.Node.EventType.TOUCH_START, this._onHoverIn, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this._onHoverOut, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this._onHoverOut, this);
        this._onHoverOut();
    }

    private _onHoverIn(): void {
        if (this.outline) {
            this.outline.color = this.hoverOutlineColor;
        }
        if (this.label) {
            this.label.node.color = this.hoverColor;
        }
    }

    private _onHoverOut(): void {
        if (this.outline) {
            this.outline.color = this.normalOutlineColor;
        }
        if (this.label) {
            this.label.node.color = this.normalColor;
        }
    }

    // update (dt) {}
}
