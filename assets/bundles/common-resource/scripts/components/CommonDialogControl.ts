import { macros } from '../common/common-resource-macros';
import * as pf from '../../../../poker-framework/scripts/pf';

const { ccclass, property } = cc._decorator;

@ccclass
export class CommonDialogControl extends cc.Component {
    @property(cc.Label)
    messageLabel: cc.Label = null;

    @property(cc.Node)
    singleButton: cc.Node = null;

    @property(cc.Node)
    doubleButtons: cc.Node[] = [];
    @property(cc.Sprite) imgTitle: cc.Sprite = null;

    private _singleButtonLabel: cc.Label;

    private _doubleButtonsLabels: cc.Label[] = [];

    private _buttonClickFunctions: Function[] = [];

    private _callbacks: Function[] = [];

    private _atlasCbLanguage: cc.SpriteAtlas = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        if (this.imgTitle !== null) {
            this._atlasCbLanguage = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
            this.imgTitle.spriteFrame = this._atlasCbLanguage.getSpriteFrame('tips_img');
        }

        this._buttonClickFunctions.push(this.onFirstButtonClick);
        this._buttonClickFunctions.push(this.onSecondButtonClick);

        this._singleButtonLabel = this.singleButton.getChildByName('Label').getComponent(cc.Label);
        this.singleButton.on('click', this._buttonClickFunctions[0], this);

        for (let i = 0; i < this.doubleButtons.length; i++) {
            this._doubleButtonsLabels[i] = this.doubleButtons[i].getChildByName('Label').getComponent(cc.Label);
            this.doubleButtons[i].on('click', this._buttonClickFunctions[i], this);
        }

        // TODO: need new order solution
        this.setZOrder(macros.ZORDER_TT);

        this.node.active = false;
    }

    setZOrder(index: number) {
        this.node.zIndex = index;
    }

    showMsg(message: string, buttonTexts: string[], firstCallback: Function, secondCallback?: Function) {
        this.messageLabel.string = message;

        if (buttonTexts.length === 1) {
            this.singleButton.active = true;
            this._singleButtonLabel.string = buttonTexts[0];
            this._callbacks[0] = firstCallback;

            for (const button of this.doubleButtons) {
                button.active = false;
            }
            this._callbacks[1] = null;
        } else if (buttonTexts.length === 2) {
            this.singleButton.active = false;

            for (let i = 0; i < this.doubleButtons.length; i++) {
                this.doubleButtons[i].active = true;
                this._doubleButtonsLabels[i].string = buttonTexts[i];
            }
            this._callbacks[0] = firstCallback;
            this._callbacks[1] = secondCallback;
        } else {
            return;
        }

        this.node.active = true;
    }

    private onFirstButtonClick() {
        if (this._callbacks[0]) {
            this._callbacks[0]();
        }
        this.node.active = false;
    }

    private onSecondButtonClick() {
        if (this._callbacks[1]) {
            this._callbacks[1]();
        }
        this.node.active = false;
    }
}
