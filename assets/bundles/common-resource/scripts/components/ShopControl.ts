// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

@ccclass
export class ShopControl extends cc.Component {
    _shopService: pf.services.ShopService = null;
    _authService: pf.services.AuthService = null;
    _domainService: pf.services.DomainService = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        cc.log('ShopControl onLoad');
        this._shopService = pf.serviceManager.get(pf.services.ShopService);
        this._authService = pf.serviceManager.get(pf.services.AuthService);
        this._domainService = pf.serviceManager.get(pf.services.DomainService);

        this.node.active = false;
    }

    open() {
        const payType = this._authService.currentUser.payType;
        if (payType !== 1) {
            this._shopService.addCoinOrder(payType).then((response) => {
                this.onMsgAddCoinOrderResponse(response);
            });
        }
    }

    onMsgAddCoinOrderResponse(msg: pf.client.IAddCoinOrderResponse) {
        cc.log(msg);

        const productId = 2;
        const userId = this._authService.currentUser.userId;
        let account = '';
        if (userId === 0) {
            account = pf.localStorage.getItem('user_id');
            if (pf.DataUtil.getArrayLength(account) <= 0) {
                cc.error('user id not found!');
                return;
            }
        } else {
            account = userId.toString();
        }
        // TODO: get uuid on native platforms
        // check ticket AT-3581 for final version of the feature
        const uuid = macros.UUID_WEB;
        const clubId = this._authService.currentUser.firstClubId;
        const unionId = this._authService.currentUser.firstAlliId;
        const nickname = encodeURI(this._authService.currentUser.nickname);
        const token = msg.token;
        let language = '';
        switch (pf.languageManager.currentLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                language = 'zh';
                break;
            case pf.LANGUAGE_GROUPS.yn_TH:
                language = 'vn';
                break;
            case pf.LANGUAGE_GROUPS.hi_IN:
                language = 'inr';
                break;
            default:
                language = 'en';
                break;
        }
        const areaCode = this._authService.currentUser.areaCode;
        const timestamp = Math.floor(new Date().getTime() / 1000);
        const extraParam1 = '';
        const isTouristPlayer = this._authService.currentUser.isTouristPlayer ? 1 : 0;
        const key: string =
            pf.StringUtil.formatC('%d', productId) +
            account +
            account +
            pf.StringUtil.formatC('%d', clubId) +
            pf.StringUtil.formatC('%d', unionId) +
            nickname +
            token +
            language +
            pf.StringUtil.formatC('%lld', timestamp) +
            pf.StringUtil.formatC('%d', isTouristPlayer) +
            macros.SHOP_SIGN;
        cc.log('shop key:' + key);
        const imToken = this._authService.currentUser.imToken;
        // const isWebview = (cc.sys.isBrowser || cv.native.isAndroidSimulator()) ? 0 : 1;
        const isWebView = cc.sys.isBrowser ? 0 : 1;

        const params: string =
            'product_id=' +
            pf.StringUtil.formatC('%d', productId) +
            '&user_id=' +
            account +
            '&login_name=' +
            account +
            '&uuid=' +
            uuid +
            '&club_id=' +
            pf.StringUtil.formatC('%d', clubId) +
            '&union_id=' +
            pf.StringUtil.formatC('%d', unionId) +
            '&nickname=' +
            nickname +
            '&token=' +
            token +
            '&language_type=' +
            language +
            '&areacode=' +
            areaCode +
            '&time=' +
            pf.StringUtil.formatC('%lld', timestamp) +
            '&extra_param1=' +
            extraParam1 +
            '&guest=' +
            isTouristPlayer +
            '&key_code=' +
            pf.Crypto.md5(key) +
            '&imToken=' +
            imToken +
            '&isWebview=' +
            isWebView;
        // const webUrl = this._authService.currentUser.shopURL + cv.config.getStringData('WEB_API_SHOP', true);
        // WEB_API_SHOP is an empty string...ignore now
        const webUrl = this._domainService.getShopUrl();
        const url = webUrl + '?' + params;

        this.openWindow(url);
    }

    openWindow(url: string) {
        const newWindow = window.open('', '_blank');
        newWindow.location.href = url;
    }
}
