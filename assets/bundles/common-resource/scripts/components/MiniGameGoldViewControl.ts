/* eslint-disable camelcase */

import * as pf from '../../../../poker-framework/scripts/pf';
import * as cr from '../common-resource';
import { macros } from '../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

@ccclass
export class MiniGameGoldViewControl extends cc.Component {
    @property(cc.Node) gold_text: cc.Node = null;
    @property(cc.Node) usdt_text: cc.Node = null;

    protected addCallback: () => void = null;

    private _noticeViewNode: cc.Node = null;

    private _walletService: pf.Nullable<pf.services.WalletService> = null;

    private _boundUpdateUserInfo = this.UpdateUserInfo.bind(this);

    private _platform: string;

    onLoad() {
        this._walletService = pf.serviceManager.get(pf.services.WalletService);
        this._walletService.addListener('userGoldNum', this._boundUpdateUserInfo);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._platform = context.platform || 'pkw';

        this.UpdateUserInfo();
    }

    onDestroy() {
        this._walletService.removeListener('userGoldNum', this._boundUpdateUserInfo);
    }

    setNoticeViewNode(node: cc.Node) {
        this._noticeViewNode = node;
    }

    setGoldNumber(num: number) {
        if (this._platform === 'pkw') {
            this.gold_text.getComponent(cc.Label).string = cr.CurrencyUtil.serverAmountToDisplayString(num);
        } else {
            this.gold_text.getComponent(cc.Label).string = cr.CommonUtil.transGoldShortString(num);
        }
    }

    setUSDTNumber(num: number) {
        this.usdt_text.getComponent(cc.Label).string = cr.CurrencyUtil.serverAmountToDisplayString(num);
    }

    setViewStyle(style: number) {}

    UpdateUserInfo() {
        const wallet = this._walletService.getWallet();
        console.log('[3in1] GoldViewControl::UpdateUserInfo ' + wallet.totalAmount + ':' + wallet.usdt);
        this.setGoldNumber(wallet.totalAmount);
        this.setUSDTNumber(wallet.usdt);
    }

    setAddCallback(cb: () => void) {
        this.addCallback = cb;
    }

    _rechargeEvent() {
        // TODO: segment event
        // if (cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE) {
        //     const properties = { item: 'headerDepositButton' };
        //     cv.segmentTool.track(
        //         cv.Enum.CurrentScreen.deposit,
        //         cv.Enum.segmentEvent.DepositInitiated,
        //         cv.Enum.Functionality.payments,
        //         properties
        //     );
        //     return;
        // }
        // const properties = { item: 'leftMenuDepositButton' };
        // cv.segmentTool.track(
        //     cv.Enum.CurrentScreen.room,
        //     cv.Enum.segmentEvent.DepositInitiated,
        //     cv.Enum.Functionality.payments,
        //     properties
        // );
    }

    onBtnAddClick(event: cc.Event) {
        if (this.addCallback) {
            this.addCallback();
        } else {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            const noticeView = this._noticeViewNode;
            if (!cc.sys.isBrowser && cc.sys.os === cc.sys.OS_IOS) {
                if (noticeView && noticeView.active) {
                    noticeView.active = false;
                    // TODO: shop
                    // cv.SHOP.setExitCallFunc(() => {
                    //     noticeView.active = true;
                    // });
                } else {
                    // cv.SHOP.setExitCallFunc(() => {});
                }
            }
            this._rechargeEvent();
            // cv.SHOP.RechargeClick();
        }
    }
}
