/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
import type { AppEvents } from 'main';

/**
 * 退出面板
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameRuleControl extends cc.Component {
    @property(cc.WebView) rule: cc.WebView = null;
    @property(cc.Button) close_btn: cc.Button = null;
    @property(cc.Sprite) title_img: cc.Sprite = null;
    @property(cc.Node) bg2_img: cc.Node = null;
    @property(cc.ScrollView) scr: cc.ScrollView = null;
    @property(cc.Node) ic_Rule: cc.Node = null; // 游戏规则
    @property(cc.Node) ruleBox: cc.Node = null;

    protected _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集
    private _isAddSpr: boolean = false;

    private _domainService: pf.Nullable<pf.services.DomainService> = null;
    protected _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;

    private _boundClose = this.onClose.bind(this);

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room;

        this._domainService = pf.serviceManager.get(pf.services.DomainService);

        this.initAtlas();
        this.title_img.getComponent(cc.Sprite).spriteFrame = this._atlas_cb_language.getSpriteFrame('cowboy_yxgz');

        pf.app.events<AppEvents>().addListener('hideWebview', this._boundClose);

        if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
            this.rule.node.removeFromParent(true);
            this.rule.node.destroy();
        } else {
            this.scr.node.removeFromParent(true);
            this.scr.node.destroy();
        }

        // 非私语平台，设置通透。\
        let spMask: any = this.bg2_img.getComponent(cc.Sprite);
        spMask.srcBlendFactor = cc.macro.BlendFactor.SRC_ALPHA;
    }

    protected initAtlas(): void {
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
    }

    protected start(): void {
        this.close_btn.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.BACK_BUTTON);
                this.onClose();
            },
            this
        );
    }

    onDestroy(): void {
        pf.app.events<AppEvents>().removeListener('hideWebview', this._boundClose);
    }

    openView(ruleUrl: string): void {
        this.node.active = true;
        if (pf.app.clientType !== pf.client.ClientType.CowboyWeb) {
            // this.rule.node.active = true;
        } else {
            if (this._isAddSpr === true) return;
        }

        const url =
            /* this._domainService.getDomainInfo().webServer + ruleUrl + */ this._miniGameRoom.roomParams.rulePic;
        // this.rule.url = url;
        if (url !== '') {
            cc.assetManager.loadRemote<cc.Texture2D>(url /* , {ext: '.png'} */, (err, texture) => {
                if (err) {
                    console.error('[RULE] loadRemote fail! err : ' + err);
                    this.ic_Rule.getComponent(cc.Sprite).spriteFrame = null;
                } else {
                    this.ic_Rule.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture);
                    // wpk pokermaster規則會超出左右範圍。
                    this.ic_Rule.width = this.ic_Rule.parent.parent.width;
                }
            });
        }
    }

    // openViewWithSprite(ruleSprite: cc.SpriteFrame) {
    //     this.node.active = true;
    //     this.ic_Rule.getComponent(cc.Sprite).spriteFrame = ruleSprite;
    // }

    onClose(): void {
        this.node.active = false;
        if (pf.app.clientType !== pf.client.ClientType.CowboyWeb) {
            this.rule.node.active = false;
        }
    }
}
