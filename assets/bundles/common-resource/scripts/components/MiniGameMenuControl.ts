/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
import * as cr from '../common-resource';
import { MiniGameGoldViewControl } from './common-resource-components-index';

/**
 * 小游戏通用菜单
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameMenuControl extends cc.Component {
    @property(cc.Sprite) img_shield: cc.Sprite = null; // 遮罩底图
    @property(cc.Node) img_menu_bg: cc.Node = null;
    @property(cc.Prefab) goldview: cc.Prefab = null;
    @property(cc.Button) btn_exchange: cc.Button = null; // 菜单底图
    @property(cc.Button) btn_rule: cc.Button = null; // 游戏规则 按钮
    @property(cc.Button) btn_sound_setting: cc.Button = null; // 音效设置 按钮
    @property(cc.Button) btn_advanced_setting: cc.Button = null; // 高级设置 按钮
    @property(cc.Button) btn_exit: cc.Button = null; // 退出游戏 按钮

    protected _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集
    protected _atlas_hb_exchange: cc.SpriteAtlas = null;

    protected _goldViewControl: MiniGameGoldViewControl = null;

    private _platform: string = '';

    show(bAnim: boolean): void {
        this._autoAnimFunc(true, bAnim);

        if (this._platform === 'wpk') {
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            if (context.updateWallet) {
                context.updateWallet();
            }
            this._goldViewControl.UpdateUserInfo();
        }
    }

    hide(bAnim: boolean): void {
        this._autoAnimFunc(false, bAnim);
    }

    getBtnExchange(): cc.Button {
        return this.btn_exchange;
    }

    getBtnRule(): cc.Button {
        return this.btn_rule;
    }

    getBtnSoundSetting(): cc.Button {
        return this.btn_sound_setting;
    }

    getBtnAdvancedSetting(): cc.Button {
        return this.btn_advanced_setting;
    }

    getBtnExit(): cc.Button {
        return this.btn_exit;
    }

    setMenuPosition(worldPos: cc.Vec2): void {
        this.img_menu_bg.setPosition(worldPos);
    }

    getMenuPosition(): cc.Vec3 {
        return this.img_menu_bg.position;
    }

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._platform = context.platform || 'pkw';

        let goldView: cc.Node = cc.instantiate(this.goldview);
        this._goldViewControl = goldView.getComponent(MiniGameGoldViewControl);
        if (this._platform === 'pkw') {
            this._goldViewControl.setAddCallback(() => {
                pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                if (cc.sys.isBrowser) {
                    cr.commonResourceAgent.commonDialog.showMsg(
                        pf.languageManager.getString('UIOpenNewWindow'),
                        [
                            pf.languageManager.getString('TipsPanel_sure_button'),
                            pf.languageManager.getString('TipsPanel_cancel_button')
                        ],
                        () => {
                            cr.commonResourceAgent.shop?.open();
                        }
                    );
                } else {
                    const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                    context.isSelfRecharge = true;
                    context.exitCallback(pf.client.ExitType.Standard);
                }
            });
        } else if (this._platform === 'wpk') {
            this._goldViewControl.setAddCallback(this.onClickAddForWPK.bind(this));
        }
        cc.find('img_menu_bg/gold_Panel', this.node).addChild(goldView);
        this.initAtlas();

        this.btn_exchange.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_exchange.getSpriteFrame('exchange_coin');

        this.btn_rule.getComponent(cc.Sprite).spriteFrame =
            this._atlas_hb_language.getSpriteFrame('humanboy_menu_rule');

        this.btn_sound_setting.getComponent(cc.Sprite).spriteFrame =
            this._atlas_hb_language.getSpriteFrame('humanboy_menu_sound_setting');

        this.btn_advanced_setting.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame(
            'humanboy_menu_advanced_setting'
        );

        this.btn_exit.getComponent(cc.Sprite).spriteFrame =
            this._atlas_hb_language.getSpriteFrame('humanboy_menu_exit');

        this.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
            this.hide(true);
        });
    }

    protected onClickAddForWPK(): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        cr.commonResourceAgent.commonDialog.showMsg(
            pf.languageManager.getString('PokerMaster_dialog_recharge'),
            [
                pf.languageManager.getString('TipsPanel_sure_button'),
                pf.languageManager.getString('TipsPanel_cancel_button')
            ],
            () => {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                context.exitCallback(pf.client.ExitType.Standard);
            },
            () => {
                // do nothing here
            }
        );
    }

    protected initAtlas(): void {
        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_LANGUAGE_ATLAS);
        this._atlas_hb_exchange = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_EXCHANGE_ATLAS);
    }

    protected start(): void {}

    protected onDestroy(): void {
        if (cc.isValid(this.img_menu_bg)) {
            this.img_menu_bg.stopAllActions();
        }
    }

    protected _autoAnimFunc(bOpen: boolean, bAnim: boolean): void {
        this.node.active = true;
        this.img_menu_bg.active = true;
        this.img_menu_bg.stopAllActions();

        let duration = 0.5;
        let seq: cc.Action = null;

        if (bOpen) {
            this.img_menu_bg.setScale(0);
            let cb: cc.ActionInstant = cc.callFunc((): void => {
                this.img_menu_bg.setScale(1.0);
                this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
            });

            if (bAnim) {
                let st: cc.ActionInterval = cc.scaleTo(duration, 1.0);
                let ebo: cc.ActionInterval = st.easing(cc.easeBackOut());
                seq = cc.sequence(ebo, cb);
            } else {
                seq = cb;
            }
        } else {
            this.img_menu_bg.setScale(1);
            let cb: cc.ActionInstant = cc.callFunc((): void => {
                this.img_menu_bg.setScale(0);
                this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
                this.node.active = false;
            });

            if (bAnim) {
                let st: cc.ActionInterval = cc.scaleTo(duration, 0.0);
                let ebi: cc.ActionInterval = st.easing(cc.easeBackIn());
                seq = cc.sequence(ebi, cb);
            } else {
                seq = cb;
            }
        }

        if (seq) {
            this.img_menu_bg.runAction(seq);
            this.img_shield.getComponent(cc.BlockInputEvents).enabled = true;
        }
    }
}
