import * as pf from '../../../../../poker-framework/scripts/pf';

const { ccclass, property } = cc._decorator;

@ccclass
export default class RebateInfoControl extends cc.Component {
    @property(cc.Node) private backgroundNode: cc.Node = null;
    @property(cc.Node) private guideInfoNode: cc.Node = null;
    @property(cc.Label) private guideInfoContent: cc.Label = null;
    @property(cc.Node) private questionNode: cc.Node = null;
    @property(cc.Node) private arrowNode: cc.Node = null;
    @property(cc.Node) private guideBgNode: cc.Node = null;

    private originalGuideWidth: number = 0;

    protected onLoad(): void {
        // get screen size
        const screenSize = cc.view.getVisibleSize();
        this.backgroundNode.setContentSize(screenSize);
        this.originalGuideWidth = this.guideInfoContent.node.width;
        this.node.active = false;
    }

    showGuideInfo() {
        this.node.active = true;
        this.guideInfoNode.active = true;
        this.guideInfoNode.opacity = 0;
        cc.Tween.stopAllByTarget(this.guideInfoNode);
        cc.tween(this.guideInfoNode).to(0.2, { opacity: 255 }).start();
        this.arrowNode.setPosition(new cc.Vec2(this.questionNode.position.x - 762, this.arrowNode.position.y));
    }

    hideGuideInfo() {
        this.guideInfoNode.opacity = 255;
        cc.Tween.stopAllByTarget(this.guideInfoNode);
        cc.tween(this.guideInfoNode)
            .to(0.2, { opacity: 0 })
            .call(() => {
                this.guideInfoNode.active = false;
            })
            .start();
    }

    setGuideInfo(data: pf.client.IEventStatusClient, minWidth: number): void {
        this.guideInfoContent.string = this._getEventContent(data);
        this.adjustGuideInfoSize(minWidth);
    }

    private adjustGuideInfoSize(minWidth: number): void {
        const maxWidth = this.originalGuideWidth;

        // Get actual text size by calculation
        const label = this.guideInfoContent;
        const originalOverflow = label.overflow;
        label.overflow = cc.Label.Overflow.NONE; // temporarily remove overflow limit
        label.node.setContentSize(cc.size(0, 0)); // reset size to calculate again

        // Update and get actual text size
        this.scheduleOnce(() => {
            const textSize = label.node.getContentSize();
            const paddingLeft = 40;
            const paddingRight = 20;
            const newWidth = Math.max(minWidth, Math.min(textSize.width, maxWidth));
            this.guideInfoContent.node.width = newWidth;
            this.guideInfoContent.node.x = paddingLeft;
            this.guideBgNode.width = newWidth + paddingLeft + paddingRight;
            label.overflow = originalOverflow;
        });
    }

    private _getEventContent(data: pf.client.IEventStatusClient): string {
        const content = data.content;
        if (!content) {
            return '';
        }
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        if (context?.platform === 'wpk' && context.gameId === pf.client.GameId.CowBoy) {
            return content[pf.LANGUAGE_GROUPS.zh_CN]?.desc;
        }
        return data.content[pf.languageManager.currentLanguage]?.desc;
    }
}
