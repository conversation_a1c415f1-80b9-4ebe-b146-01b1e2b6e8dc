/* eslint-disable camelcase */
import * as cr from 'common-resource';
import { MiniGameCurrencyIconSetting } from '../../common/rebate-defines';

const { ccclass, property } = cc._decorator;
@ccclass
export class MultipleCurrenciesControl extends cc.Component {
    @property(MiniGameCurrencyIconSetting) prizeType: MiniGameCurrencyIconSetting[] = [];
    @property(cc.Node) private groups: cc.Node[] = [];
    @property private alignCenter: boolean = false;
    @property private content_with_1: number = 205;
    @property private content_with_2: number = 264;
    setData(data: { [k: number]: number }) {
        const list = Object.entries(data); // .filter(([key, value]) => value > 0);
        for (let i = 0; i < this.groups.length; i++) {
            this.groups[i].active = i < list.length;
        }
        const firstNode = this.groups[0];
        const firstLabel = firstNode.getComponentInChildren(cc.Label);
        const firstIcon = firstNode.getComponentInChildren(cc.Sprite);
        const layout = firstNode.parent.getComponent(cc.Layout);
        if (this.alignCenter) {
            if (list.length >= 2) {
                layout.enabled = true;
                layout.node.width = this.content_with_2;
                firstLabel.overflow = cc.Label.Overflow.SHRINK;
                firstLabel.node.width =
                    this.content_with_2 / 2 - firstIcon.node.width - layout.paddingLeft - layout.spacingX / 2;
                firstLabel.enableWrapText = false;
            } else {
                firstNode.setPosition(0, 0);
                layout.node.width = this.content_with_1;
                layout.enabled = false;
                firstLabel.overflow = cc.Label.Overflow.NONE;
            }
        }
        for (let i = 0; i < list.length; i++) {
            if (i >= this.groups.length) {
                const newNode = cc.instantiate(firstNode);
                layout.node.addChild(newNode);
                this.groups.push(newNode);
            }
            const node = this.groups[i];
            const [key, value] = list[i];
            const currency = this.prizeType.find((o) => o.currencyType.toString() === key);
            node.getComponentInChildren(cc.Sprite).spriteFrame = currency?.coinFrame;
            node.getComponentInChildren(cc.Label).string = cr.CommonUtil.getShortOwnCoinString(value);
            node.active = true;
        }
    }
}
