/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from 'common-resource';
import { CalendarDailyControl } from './CalendarDailyControl';

const { ccclass, property } = cc._decorator;

@ccclass
export class BettingBonusControl extends cc.Component {
    @property(CalendarDailyControl) private calendar: CalendarDailyControl = null;

    @property(cc.Label) protected lblClaimed: cc.Label = null;
    @property(cc.Label) protected lblTotalBet: cc.Label = null;
    @property(cc.Label) protected lblNextLvl: cc.Label = null;
    protected selectedIdx: number = 0;
    private eventData: pf.client.Rebate.IEventData = null;
    protected sysTime: number = 0;

    updateData(data: pf.client.IEventStatusClient) {
        this.eventData = data.setting;
        const betTime = data.setting.bet_time[0];
        const isActive = data.system_time >= betTime.event_start_time && data.system_time <= betTime.event_end_time;
        const nextLvlBet = this.getNextLevelBet();
        this.lblClaimed.string = this.getAllClaimed();
        this.lblTotalBet.string = cr.CommonUtil.ThousandPointFormat(betTime.betting_amount);
        this.lblNextLvl.string = cr.CommonUtil.ThousandPointFormat(nextLvlBet);
        this.lblNextLvl.node.parent.active = isActive && nextLvlBet > 0;
        this.calendar.updateData(data.setting, 1, data.system_time);
    }

    private getAllClaimed(): string {
        if (this.eventData === null) {
            return '0';
        }
        const sumFunc = (reward_progress: pf.client.Rebate.IRewardProgress[], currencyType?: number) => {
            return reward_progress
                .filter((o) => o.got && (o.currency_type === currencyType || currencyType === undefined))
                .reduce((accumulator, currentValue) => {
                    return Number(accumulator) + Number(currentValue.reward || 0);
                }, 0);
        };

        const claimedList: { [k: number]: number } = {};
        const uniqueCurrencyTypeList = cr.RebateUtil.getUniqueCurrencyTypeList(this.eventData);
        uniqueCurrencyTypeList.forEach((currencyType) => {
            const amount = this.eventData.bet_time.reduce((accumulator, currentValue) => {
                return Number(accumulator) + Number(sumFunc(currentValue.reward_progress, currencyType));
            }, 0);
            if (amount > 0) {
                claimedList[currencyType] = amount;
            }
        });

        const list = Object.entries(claimedList);
        if (list.length === 0) {
            return '0';
        }
        if (list.length === 1) {
            const [key, value] = list[0];
            return `${cr.CommonUtil.ThousandPointFormat(value)}`;
        }

        let [key, value] = list[0];
        const comma = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? '' : ' ';
        const space = '  ';
        let str = `${pf.languageManager.getString(
            'minigame_currency_type_' + key
        )}${comma}${cr.CommonUtil.ThousandPointFormat(value)}`;
        for (let i = 1; i < list.length; i++) {
            [key, value] = list[i];
            str += `${space}${pf.languageManager.getString(
                'minigame_currency_type_' + key
            )}${comma}${cr.CommonUtil.ThousandPointFormat(value)}`;
        }
        return str;
    }

    private getNextLevelBet(): number {
        if (this.eventData === null || this.eventData.bet_time.length === 0) {
            return 0;
        }
        const betData = this.eventData.bet_time[0];
        if (betData.reward_progress.length > 0) {
            // find the nearst reward can claim
            const rewardProgress = betData.reward_progress.filter((o) => !o.can_get);
            if (rewardProgress.length > 0) {
                const reward = rewardProgress[0];
                return reward.amount_gte - betData.betting_amount;
            }
        }
        return 0;
    }
}
