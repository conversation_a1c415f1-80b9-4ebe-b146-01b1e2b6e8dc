import * as pf from '../../../../../poker-framework/scripts/pf';

const { ccclass, property } = cc._decorator;

@ccclass
export class CountdownControl extends cc.Component {
    @property(cc.Label) lblTitle: cc.Label = null;
    @property(cc.Label) lblCountDown: cc.Label = null;

    start() {}

    startCountdown(time: number, onFinish?: Function, displayTime: boolean = true) {
        let duration = time;
        this.unscheduleAllCallbacks();
        this.lblCountDown.string = '';
        this.lblCountDown.node.active = displayTime;
        if (duration <= 0) {
            onFinish?.();
            return;
        }
        this.countdown(duration);

        this.schedule(() => {
            duration -= 1;
            if (duration > 0) {
                this.countdown(duration);
            } else {
                this.lblCountDown.string = '';
                this.unscheduleAllCallbacks();
                onFinish?.();
            }
        }, 1);
    }

    countdown(time: number) {
        let days = Math.floor(time / 86400);
        let hours = Math.floor((time % 86400) / 3600);
        let minutes = Math.floor(((time % 86400) % 3600) / 60);
        let seconds = Math.floor(((time % 86400) % 3600) % 60);
        let result = '';
        const space = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? '' : ' ';
        if (days > 0) {
            result += days + this.getStringDataNumber('day', days) + space;
        }
        if (days > 0 || hours > 0) {
            result += hours + this.getStringDataNumber('hour', hours) + space;
        }
        result += minutes + this.getStringDataNumber('minute', minutes);

        if (days <= 0 && hours <= 0) {
            result += space + seconds + this.getStringDataNumber('seconds', seconds);
        }
        this.lblCountDown.string = result;
    }

    private getStringDataNumber(key: string, time: number) {
        return pf.languageManager.getString(key).replace('(s)', time > 1 ? 's ' : ' ');
    }

    setTitle(title: string) {
        this.lblTitle.string = title;
    }

    stopCountdown() {
        this.unscheduleAllCallbacks();
        this.lblCountDown.string = '';
    }

    protected onDestroy(): void {
        this.unscheduleAllCallbacks();
    }
}
