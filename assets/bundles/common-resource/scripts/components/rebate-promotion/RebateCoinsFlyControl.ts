const { ccclass, property } = cc._decorator;

@ccclass
export default class RebateCoinsFlyControl extends cc.Component {
    @property(cc.Node)
    coinFlyDemo1: cc.Node = null;

    @property(cc.Node)
    coinFlyDemo2: cc.Node = null;

    @property(cc.Node)
    coinFlyDemo3: cc.Node = null;

    @property(cc.Node)
    coinFlyDemo4: cc.Node = null;

    @property(cc.Node)
    nodeToastDemo: cc.Node = null;

    @property(cc.RichText)
    txtToast: cc.RichText = null;

    private readonly ANIMATION_DURATION = 1.2;
    private readonly TOAST_TEXT_TEMPLATE =
        '<color=#FFFFFF>成功领取 <color=#FFDE58><bold>{0}</bold></color> 金币，请前往【我的钱包】查看</color>';

    onLoad() {
        this.initializeCoins();
    }

    private initializeCoins() {
        [this.coinFlyDemo1, this.coinFlyDemo2, this.coinFlyDemo3, this.coinFlyDemo4].forEach((coin) => {
            if (coin) {
                coin.opacity = 0;
                coin.scale = 0.25;
                coin.position = cc.v3(0, 0, 0);
            }
        });

        if (this.nodeToastDemo) {
            this.nodeToastDemo.opacity = 0;
            this.nodeToastDemo.scale = 0.95;
        }
    }

    private updateToastText(amount: number) {
        if (this.txtToast) {
            this.txtToast.string = this.TOAST_TEXT_TEMPLATE.replace('{0}', amount.toString());
        }
    }

    playCoinsFlyAnimation(targetNode: cc.Node, startNode: cc.Node, amount: number = 198): Promise<void> {
        return new Promise((resolve) => {
            // this.updateToastText(amount);
            const target = targetNode;
            const start = startNode;

            if (!target) {
                console.warn('No target node specified for coin animation');
                resolve();
                return;
            }

            const worldPos = target.convertToWorldSpaceAR(cc.v2(0, 0));
            const localPos = this.node.convertToNodeSpaceAR(worldPos);

            let startPos = cc.v2(0, 0);
            if (start) {
                const startWorldPos = start.convertToWorldSpaceAR(cc.v2(0, 0));
                startPos = this.node.convertToNodeSpaceAR(startWorldPos);
            }

            let completedAnimations = 0;
            const totalAnimations = 4; // 4 coins + 1 toast

            const checkCompletion = () => {
                completedAnimations++;
                if (completedAnimations >= totalAnimations) {
                    resolve();
                }
            };

            this.playCoinAnimation(this.coinFlyDemo1, 0, startPos, localPos, checkCompletion);
            this.playCoinAnimation(this.coinFlyDemo2, 0.016, startPos, localPos, checkCompletion);
            this.playCoinAnimation(this.coinFlyDemo3, 0.033, startPos, localPos, checkCompletion);
            this.playCoinAnimation(this.coinFlyDemo4, 0.05, startPos, localPos, checkCompletion);
            // this.playToastAnimation(checkCompletion);
        });
    }

    private playCoinAnimation(
        coin: cc.Node,
        delay: number,
        startPosition: cc.Vec2,
        targetPosition: cc.Vec2,
        onComplete: () => void
    ) {
        if (!coin) {
            onComplete?.();
            return;
        }

        // Reset coin state
        coin.opacity = 0;
        coin.scale = 0.25;
        coin.position = cc.v3(startPosition.x, startPosition.y, 0);

        // Fade in
        cc.tween(coin).delay(delay).to(0.08, { opacity: 255 }).start();

        // Scale animation
        cc.tween(coin).delay(delay).to(0.16, { scale: 1 }, { easing: 'cubicOut' }).start();

        // Position animation
        cc.tween(coin)
            .delay(delay)
            .to(
                this.ANIMATION_DURATION,
                {
                    position: cc.v3(targetPosition.x, targetPosition.y, 0)
                },
                { easing: 'cubicInOut' }
            )
            .call(() => {
                // Fade out at the end
                cc.tween(coin)
                    .to(0.08, { opacity: 0 })
                    .call(() => {
                        onComplete?.();
                    })
                    .start();
            })
            .start();
    }

    private playToastAnimation(onComplete: () => void) {
        if (!this.nodeToastDemo) {
            onComplete?.();
            return;
        }

        cc.tween(this.nodeToastDemo)
            .delay(1.1)
            .to(0.16, { opacity: 255 })
            .to(0.33, { scale: 1 }, { easing: 'cubicOut' })
            .delay(1)
            .to(0.33, { opacity: 0 })
            .call(() => {
                onComplete?.();
            })
            .start();
    }
}
