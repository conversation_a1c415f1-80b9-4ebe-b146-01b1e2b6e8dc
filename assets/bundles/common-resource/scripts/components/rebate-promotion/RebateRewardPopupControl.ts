import { GameDialogBaseControl } from './GameDialogBaseControl';

const { ccclass, property } = cc._decorator;

@ccclass
export class RebateRewardPopupControl extends GameDialogBaseControl {
    @property(cc.Node)
    private popUpNode: cc.Node = undefined;

    @property(cc.Animation)
    private rewardAnim: cc.Animation = undefined;

    init(): void {}

    collect(): void {
        // emit event here

        this._stop();
    }

    _playPopup(): void {
        this.unscheduleAllCallbacks();
        this.popUpNode.active = true;

        this.rewardAnim.play('RewardPopup');
        this.rewardAnim.playAdditive('RewardPopup_Loop');
    }

    private _stop(): void {
        this.rewardAnim.stop();
        this.popUpNode.active = false;
    }
}
