const { ccclass } = cc._decorator;

@ccclass
export class NumberRollerControl extends cc.Component {
    private _start: number = 0;
    private _end: number = 0;
    private _duration: number = 0;
    private _speed: number = 0;
    private _time: number = 0;
    private _isReverse: boolean = false;
    private _update: (value: number) => void = undefined;
    private _completed: (value: number) => void = undefined;

    onLoad() {
        this.enabled = false;
    }

    playRollByTime(
        duration: number,
        startValue: number,
        endValue: number,
        callback: (value: number) => void,
        onComplete?: (value: number) => void
    ) {
        if (startValue === endValue) return;
        if (endValue < 0 || startValue < 0) {
            cc.warn('Attempt to run with negative value');
            return;
        }

        this._start = startValue;
        this._end = endValue;
        this._duration = duration;
        this._speed = undefined;
        this._update = callback;
        this._completed = onComplete;
        this._time = 0;

        callback(startValue);
        this.enabled = true;
    }

    playRollBySpeed(
        speed: number,
        startValue: number,
        endValue: number,
        callback: (value: number) => void,
        onComplete?: (value: number) => void
    ) {
        if (startValue === endValue) return;
        if (endValue < 0 || startValue < 0) {
            cc.warn('Attempt to run with negative value');
            return;
        }

        this._start = startValue;
        this._end = endValue;
        this._duration = undefined;
        this._update = callback;
        this._completed = onComplete;
        this._time = 0;
        this._isReverse = this._start > this._end;
        this._speed = this._isReverse ? -speed : speed;

        callback(startValue);
        this.enabled = true;
    }

    skip() {
        if (this.enabled) {
            this.stop();
            this._update(this._end);
            const callback = this._completed;
            this._completed = undefined;
            if (callback) {
                callback(this._end);
            }
        }
    }

    stop() {
        this.enabled = false;
    }

    update(dt: number) {
        if (this._speed !== undefined) {
            this._rollBySpeed(dt);
        } else {
            this._rollByTime(dt);
        }
    }

    private _rollByTime(dt: number): void {
        this._time += dt;
        let ratio = this._time / this._duration;
        if (ratio >= 1) {
            this._update(this._end);
            const callback = this._completed;
            this._completed = undefined;
            if (callback) {
                callback(this._end);
            }
            this.stop();
        } else {
            ratio = 1 - Math.cos(ratio * Math.PI * 0.5);
            const currentCount = cc.misc.lerp(this._start, this._end, ratio);
            this._update(currentCount);
        }
    }

    private _rollBySpeed(dt: number): void {
        this._start += this._speed * dt;
        const max = this._isReverse ? this._start <= this._end : this._start >= this._end;
        if (max) {
            this._update(this._end);
            const callback = this._completed;
            this._completed = undefined;
            if (callback) {
                callback(this._end);
            }
            this.stop();
        } else {
            this._update(this._start);
        }
    }
}
