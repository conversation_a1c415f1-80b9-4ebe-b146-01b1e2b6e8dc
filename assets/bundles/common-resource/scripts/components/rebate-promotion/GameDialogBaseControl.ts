import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

@ccclass
export class GameDialogBaseControl extends cc.Component {
    @property(cc.Button) sureButton: cc.Button = null;
    @property(cc.Node) cancelButton: cc.Node = null;
    @property(cc.Label) titleLabel: cc.Label = null;
    @property(cc.RichText) content: cc.RichText = null;

    private sureCallback: Function = null;
    private cancelCallback: Function = null;
    private extraActionCallback: Function = null;
    private closeCallback: Function = null;

    protected rebateService: pf.services.RebateService = null;

    onLoad() {
        this.sureButton?.node.on('click', this.onBtnSureClick, this);
        this.cancelButton?.on('click', this.onBtnCancelClick, this);
        this.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
            event.stopPropagation();
        });
        this.node.active = false;

        this.rebateService = pf.serviceManager.get(pf.services.RebateService);
    }

    // sendMsgToManager(msg: string) {
    //     cv.MessageCenter.send(DialogManager.preFix + msg);
    // }

    setCloseCallback(closeCallback: Function) {
        this.closeCallback = closeCallback;
    }

    reset() {
        if (!this.node) return;
        // cv.resMgr.adaptWidget(this.node, true);
        cr.UIUtil.adaptWidget(this.node, true);
    }

    showMsg(
        content: string,
        titleString: string = '',
        sureCallback: Function,
        cancelCallback?: Function,
        actionCallback?: Function,
        horizontalAlign: number = cc.Label.HorizontalAlign.CENTER
    ) {
        if (!this.node) return;

        this.node.active = true;
        this.sureCallback = sureCallback;
        this.cancelCallback = cancelCallback;
        this.extraActionCallback = actionCallback;
        if (this.titleLabel) {
            this.titleLabel.string = titleString;
        }

        if (this.content) {
            this.content.horizontalAlign = horizontalAlign;
            this.content.string = content;
        }
        this.onShowedpopup();
    }

    hideTipsPanel() {
        if (this && this.node && cc.isValid(this.node, true)) {
            this.node.active = false;
        }
    }

    getVisible(): boolean {
        if (this && this.node && cc.isValid(this.node, true)) {
            return this.node.active;
        }

        return false;
    }

    private onBtnSureClick() {
        // cv.AudioMgr.playButtonSound('button_click');
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this.sureCallback?.();
    }

    private onBtnCancelClick() {
        // cv.AudioMgr.playButtonSound('button_click');
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        // this.sendMsgToManager('onPopupClose');
        this.closeCallback?.();
        this.cancelCallback?.();
    }

    protected onShowedpopup() {}
}
