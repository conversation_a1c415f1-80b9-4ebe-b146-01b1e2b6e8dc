import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from 'common-resource';
import * as common from 'common';

import AvatarControl = common.components.AvatarControl;
import ScrollViewItemControl = common.components.ScrollViewItemControl;

const { ccclass, property } = cc._decorator;

const NOT_RANK_FONT_SIZE = 36;
const TOP_RANK_FONT_SIZE = 44;
@ccclass
export class BettingRankingItemControlV2 extends cc.Component {
    @property(cc.Sprite) iconRank: cc.Sprite = null;
    @property(cc.Node) iconAvatar: cc.Node = null;
    @property(cc.Label) lblRank: cc.Label = null;
    @property(cc.Label) lblName: cc.Label = null;
    @property(cc.Label) lblAmount: cc.Label = null;
    @property(cc.Label) lblRewad: cc.Label = null;
    @property(cc.Node) iconReward: cc.Node = null;
    @property(cc.Node) highLight: cc.Node = null;
    @property(cc.Integer) private maxLength = 100;
    @property(cc.SpriteFrame) medalFrames: cc.SpriteFrame[] = [];

    private originName: string = '';

    protected onLoad(): void {
        this.lblName.string = '';
    }

    updateSVReuseData(index: number, dataArray: any[], view: ScrollViewItemControl): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;

        const data = dataArray[index];
        if (!data) return;

        this.updateUI(data);
    }

    updateUI(data: pf.client.Rebate.ILeaderboardInfo & { highLight: boolean }): void {
        if (!data) return;

        const idx = data.rank || -1;
        this.lblRank.string = idx === -1 ? pf.languageManager.getString('Rebate_not_rank') : `${idx}.`;
        this.lblRank.enableBold = idx !== -1;
        this.lblRank.fontSize = idx !== -1 ? TOP_RANK_FONT_SIZE : NOT_RANK_FONT_SIZE;
        this.lblAmount.string = data.bet_amount > 0 ? cr.CommonUtil.getShortOwnCoinString(data.bet_amount) : '-';
        this.lblRewad.string = data.reward > 0 ? cr.CommonUtil.getShortOwnCoinString(data.reward) : '-';

        this.iconAvatar.getComponent(AvatarControl).loadHeadImage(data.head, data.plat || 0);
        if (idx > 0 && idx <= 3) {
            this.iconRank.spriteFrame = this.medalFrames[idx - 1];
            this.lblRank.node.active = false;
            this.iconRank.node.active = true;
        } else {
            this.iconRank.node.active = false;
            this.lblRank.node.active = true;
        }

        if (this.highLight) {
            this.highLight.active = data.highLight;
        }

        this.iconReward.active = data.reward > 0;

        if (this.originName !== data.name || this.lblName.string === '') {
            this.lblName.node.opacity = 1;
            this.lblName.string = this.originName = data.name;
            this.scheduleOnce(() => {
                if (this.lblName.node.width > this.maxLength) {
                    let ratio = 1 - (this.lblName.node.width - this.maxLength) / this.lblName.node.width;
                    let newString = data.name.substring(0, Math.floor(data.name.length * ratio));
                    this.lblName.string = newString;
                    this.adaptNameLabel(newString, this.lblName);
                } else {
                    this.lblName.node.opacity = 255;
                }
            }, 0);
        }
    }

    private adaptNameLabel(originString: string, label: cc.Label) {
        this.scheduleOnce(() => {
            if (label.node.width > this.maxLength) {
                let newString = originString;
                label.string = newString.substring(0, newString.length - 1);
                this.adaptNameLabel(label.string, label);
            } else {
                this.showName(label, label.string, '...');
            }
        }, 0);
    }

    private showName(label: cc.Label, content: string, surfix: string) {
        label.string = content + surfix;
        this.scheduleOnce(() => {
            if (label.node.width > this.maxLength) {
                let newString = content;
                label.string = newString.substring(0, newString.length - 1) + surfix;
            }
        }, 0);
        label.node.opacity = 255;
    }
}
