/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { NumberRollerControl } from './NumberRollerControl';
import type { IEventStatusClient } from '../../../../../poker-framework/scripts/poker-client/poker-client-index';
import Particle3DControl from '../../../../common/scripts/components/Particle3DControl';
import PlayAnimControl from '../../../../common/scripts/components/PlayAnimControl';

const { ccclass, property } = cc._decorator;

interface IFloatingButtonStatus {
    minimum: number;
    maximum: number;
    value: number;
    isReward: boolean;
    leaderboard?: ILeaderboardInfo;
}

interface ILeaderboardInfo {
    rank?: number;
    surpass?: number;
    nickName?: string;
    unClaimReward?: number;
    isTop?: boolean;
    topNCanGet?: number;
}

@ccclass
export class RebateFloatingButtonControl extends cc.Component {
    @property(cc.Sprite)
    private spB1Radial: cc.Sprite = undefined;
    @property(PlayAnimControl) animShow: PlayAnimControl = null;
    @property(Particle3DControl) p3dShow: Particle3DControl = null;
    @property(PlayAnimControl) animBarMax: PlayAnimControl = null;
    @property(Particle3DControl) p3dBarMax: Particle3DControl = null;
    @property(cc.Label)
    private lbStatus: cc.Label = undefined;

    private _isFirstSpawn: boolean = true;
    private _clickButton: () => void = undefined;
    private _eventid: number = undefined;

    private _rebateService: pf.services.RebateService = null;

    private _lastMaximum: number = 0;
    private _hasAllClaimedAnimationPlayed: boolean = false;

    private _currentEvent: IEventStatusClient = null;

    @property(cc.Node)
    private ndParticlePivot: cc.Node = null;

    @property(cc.Node)
    private ndParticle: cc.Node = null;

    @property(cc.Node)
    private ndBarGlow: cc.Node = null;

    @property(cc.Node)
    private ndBox: cc.Node = null;

    @property(cc.Node)
    private ndBar: cc.Node = null;

    // used to setup which activity will be trigger whenever button is click. Instantiate activity prefab in here
    setup(callback: () => void): void {
        this._clickButton = callback;
    }

    get eventId(): number {
        return this._eventid;
    }

    init(eventid: number, isNewTheme?: boolean): void {
        this._rebateService = pf.serviceManager.get(pf.services.RebateService);
        this._eventid = eventid;
        this.lbStatus.fontSize = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? 22 : 20;
        this.lbStatus.string = pf.languageManager.getString('Rebate_betting_bonus');
    }

    play(data: pf.client.IEventStatusClient): void {
        this._currentEvent = data;
        const betTime = this._currentEvent.setting.bet_time[0];
        if (!data || !betTime || data.system_time >= betTime.end_time) {
            this.hide();
            return;
        }
        this.node.active = true;

        if (data.system_time < betTime.start_time) {
            this._active(false);
            const duration = betTime.start_time - data.system_time;
            this._countdownToDisplay(duration, () => {
                this._active(true);
            });
            return;
        }

        // Process data
        const { minimum, maximum, value, isReward } = this._processData(betTime);

        if (this._isFirstSpawn) {
            this.show(minimum, maximum, value);
            this._isFirstSpawn = false;
        } else {
            this.progress(minimum, maximum, value);
        }

        this._countdownToDisplay(betTime.end_time - data.system_time, () => {
            this.stop();
        });
    }

    private _countdownToDisplay(duration: number, callback: Function): void {
        this.unscheduleAllCallbacks();
        this.schedule(() => {
            if (!cc.isValid(this)) {
                this.unscheduleAllCallbacks();
                return;
            }
            duration -= 1;
            if (duration <= 0) {
                this.unscheduleAllCallbacks();
                callback();
            }
        }, 1);
    }

    show(minimum: number, maximum: number, value: number): void {
        this.node.active = true;
        // this.anim.play('Normal_Show');
        this.playShowAnimation();
        // this.lbProgress.node.active = true;
        // this.lbProgress.string = util.CommonUtil.getShortOwnCoinString(value);
        const end = (value - minimum) / (maximum - minimum);
        this.spB1Radial.fillRange = -0.5 - end / 2;
        // this.spB2Radial.fillRange = -end;

        this._lastMaximum = maximum;
        if (this._isAllClaimed()) {
            this.lbStatus.string = pf.languageManager.getString('Rebate_all_claimed');
        } else {
            this.lbStatus.string = pf.languageManager.getString('Rebate_betting_bonus');
        }
    }

    private _active(b: boolean): void {
        if (b) {
            this.node.opacity = 255;
            this.node.setPosition(0, 0);
        } else {
            this.node.opacity = 0;
            this.node.setPosition(10000, 0);
        }
    }

    progress(minimum: number, maximum: number, value: number): void {
        const isReachedNextReward = this._lastMaximum > 0 && value >= this._lastMaximum;

        if (this._isAllClaimed()) {
            if (!this._hasAllClaimedAnimationPlayed) {
                this._hasAllClaimedAnimationPlayed = true;
                const start = (this.spB1Radial.fillRange + 0.5) * -2;
                const end = 1;

                const radialB1Roll = this.spB1Radial.node.getComponent(NumberRollerControl);
                radialB1Roll.playRollByTime(
                    1,
                    start,
                    end,
                    (amount) => {
                        this.spB1Radial.fillRange = -0.5 - amount / 2;
                    },
                    () => {
                        this.playBarMaxAnimationWithoutProgress();
                        this.lbStatus.string = pf.languageManager.getString('Rebate_all_claimed');
                        this.spB1Radial.fillRange = -1;
                    }
                );
            }
        } else if (isReachedNextReward) {
            // If reached milestone, run progress to 100% of the old milestone
            const start = (this.spB1Radial.fillRange + 0.5) * -2;
            const end = 1; // 100% of the old milestone
            // this.anim.play('Normal_ProgressBar_Grow');
            // this.lbProgress.node.active = true;
            // this.ndProgressBar.active = true;
            // const numberRoll = this.lbProgress.node.getComponent(NumberRollerControl);
            const end_ratio = maximum - minimum;
            const start_ratio = value - minimum;
            // numberRoll.playRollByTime(1, this._progress, value, (amount) => {
            //     this.lbProgress.string = util.CommonUtil.getShortOwnCoinString(amount);
            //     this._progress = value;
            // });

            const radialB1Roll = this.spB1Radial.node.getComponent(NumberRollerControl);
            radialB1Roll.playRollByTime(
                1,
                start,
                end,
                (amount) => {
                    this.spB1Radial.fillRange = -0.5 - amount / 2;
                },
                () => {
                    this.spB1Radial.fillRange = -0.5;
                    // After running to 100%, play milestone animation
                    this.playBarMaxAnimation();
                    this.lbStatus.string = pf.languageManager.getString('Rebate_betting_bonus');
                }
            );
        } else {
            // If not reached milestone, run normally
            const end_ratio = maximum - minimum;
            const start_ratio = value - minimum;
            const start = (this.spB1Radial.fillRange + 0.5) * -2;
            const end = start_ratio / end_ratio;

            const radialB1Roll = this.spB1Radial.node.getComponent(NumberRollerControl);
            radialB1Roll.playRollByTime(1, start, end, (amount) => {
                this.spB1Radial.fillRange = -0.5 - amount / 2;
            });
            this.lbStatus.string = pf.languageManager.getString('Rebate_betting_bonus');
        }

        this._lastMaximum = maximum;
    }

    hide(): void {
        // this.ndToast.active = false;
        this.node.active = false;
        this.unschedule(this.playToast);
    }

    stop(): void {
        this._rebateService.emit('eventStatusStop');
    }

    clickFloatButton(): void {
        if (this._currentEvent) {
            // Process click with current event
            if (this._clickButton) {
                this._clickButton();
            }
        }
    }

    playToast(): void {
        // use pkw system message toast
        // this.ndToast.active = true;
        // this.lbToastMessage.string = this._toastMessage;
        // this.anim.play('Leaderboard_InList');
        // this.scheduleOnce(() => {
        //     this.ndToast.active = false;
        // }, 3);
        // TODO: find a way to send message
        // cv.MessageCenter.send('showMedalMsg', this._toastMessage);
    }

    private _processData(betTime: IEventStatusClient['setting']['bet_time'][0]): IFloatingButtonStatus {
        const progress: IFloatingButtonStatus = {
            minimum: 0,
            maximum: 0,
            value: Number(betTime.betting_amount) || 0,
            isReward: false
        };

        // Sort reward levels in ascending order
        const sortedRewards = [...betTime.reward_progress].sort((a, b) => Number(a.amount_gte) - Number(b.amount_gte));

        // Find the next reward that has not been reached
        const nextReward = sortedRewards.find(
            (reward) => !reward.got && Number(reward.amount_gte) > Number(betTime.betting_amount)
        );

        if (nextReward) {
            // Find the closest achieved reward
            const lastAchievedReward = sortedRewards
                .filter((reward) => reward.got)
                .sort((a, b) => Number(b.amount_gte) - Number(a.amount_gte))[0];

            progress.maximum = Number(nextReward.amount_gte);
            progress.minimum = lastAchievedReward ? Number(lastAchievedReward.amount_gte) : 0;
            progress.isReward = true;
        } else {
            // If all rewards have been reached
            const lastReward = sortedRewards[sortedRewards.length - 1];
            progress.maximum = Number(lastReward.amount_gte);
            progress.minimum = 0;
            progress.isReward = true;
        }

        return progress;
    }

    protected onDestroy(): void {
        this.unschedule(this.playToast);
    }

    playShowAnimation(): void {
        this.animShow.playAnimation();
        this.p3dShow.playParticles3D();
    }

    playBarMaxAnimation(): void {
        this.tweenBarMax(true);
        this.p3dBarMax.playParticles3D();
        this.scheduleOnce(() => {
            this.node.emit('barMaxAnimationEnd');
        }, this.p3dBarMax.DelaySec + 0.5);
    }

    playBarMaxAnimationWithoutProgress(): void {
        this.tweenBarMax(false);
        this.p3dBarMax.playParticles3D();
        this.scheduleOnce(() => {
            this.node.emit('barMaxAnimationEnd');
        }, this.p3dBarMax.DelaySec + 0.5);
    }

    private tweenBarMax(isProgress?: boolean): void {
        this.ndParticlePivot.scale = 0;
        this.ndParticle.y = -5;
        this.ndBarGlow.opacity = 0;
        this.ndBox.scale = 1;
        if (isProgress) {
            this.ndBar.getComponent(cc.Sprite).fillRange = -1;
        }

        cc.tween(this.ndParticlePivot).to(0.05, { scale: 1 }, { easing: 'sineOut' }).start();

        cc.tween(this.ndParticle).to(0.2, { y: 15 }, { easing: 'sineOut' }).to(0.166, { y: 0 }).start();

        cc.tween(this.ndBarGlow).to(0.05, { opacity: 255 }).delay(0.066).to(0.55, { opacity: 0 }).start();

        cc.tween(this.ndBox)
            .to(0.083, { scale: 1.25 }, { easing: 'sineOut' })
            .to(0.55, { scale: 1 }, { easing: 'sineOut' })
            .start();

        if (isProgress) {
            const barSprite = this.ndBar.getComponent(cc.Sprite);
            const startFillRange = -1;
            const endFillRange = -0.5;
            const duration = 0.916;

            cc.tween({ progress: 0 })
                .delay(0.333)
                .to(
                    duration,
                    { progress: 1 },
                    {
                        easing: 'cubicOut',
                        onUpdate: (target: { progress: number }) => {
                            const currentFillRange = startFillRange + (endFillRange - startFillRange) * target.progress;
                            barSprite.fillRange = currentFillRange;
                        }
                    }
                )
                .start();
        }
    }

    private _isAllClaimed(): boolean {
        const allRewardsClaimed = this._currentEvent?.setting?.bet_time?.[0]?.reward_progress?.every(
            (reward) => reward.got
        );
        return allRewardsClaimed;
    }
}
