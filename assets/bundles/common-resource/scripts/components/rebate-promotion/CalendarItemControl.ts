import * as cr from 'common-resource';
import type { ICalendarItemParam } from '../../common/rebate-defines';
import { MiniGamePrizeSetting } from '../../common/rebate-defines';

const { ccclass, property } = cc._decorator;

@ccclass
export class CalendarItemControl extends cc.Component {
    @property(cc.Label) lblDate: cc.Label = null;
    @property(cc.Label) amountReward: cc.Label = null;
    @property(cc.Sprite) coinIcon: cc.Sprite = null;
    @property(cc.Button) btSelect: cc.Button = null;
    @property(cc.Node) flagOutdate: cc.Node = null;
    @property(cc.Node) flagFill: cc.Node = null;
    @property(cc.Node) outline: cc.Node = null;
    // @property(cc.SpriteFrame) coniSprites: cc.SpriteFrame[] = [];
    @property(cc.Sprite) headers: cc.Sprite[] = [];
    @property(cc.Color) colors: cc.Color[] = [];
    @property(MiniGamePrizeSetting) prizeSetting: MiniGamePrizeSetting[] = [];
    @property(cc.Boolean) refreshDateColor = true;

    _onSelected: (idx: number) => void;
    index: number = 0;

    // MsgSelectCalendarItem: string = 'onSelectCalendarItem';

    protected start(): void {
        this.btSelect.node.on(cc.Node.EventType.TOUCH_END, this.onSelect, this);
    }

    // protected onEnable(): void {
    //     cv.MessageCenter.register(this.MsgSelectCalendarItem, this.setOutline.bind(this), this);
    // }

    // protected onDisable(): void {
    //     cv.MessageCenter.unregister(this.MsgSelectCalendarItem, this);
    // }

    setData(data: ICalendarItemParam, onSelected: (idx: number) => void): void {
        this.node.active = true;
        this._onSelected = onSelected;
        this.index = data.index;
        this.lblDate.string = data.dateString;
        if (this.refreshDateColor) this.lblDate.node.color = this.colors[0];
        this.amountReward.string = cr.CommonUtil.getShortOwnCoinString(data.amountReward);
        const setting = this.prizeSetting.find((o) => o.currencyType === data.currencyType);

        if (!data.isClaimedAll) {
            this.coinIcon.spriteFrame = setting?.coinSprites[0];
            this.flagFill.active = false;
            this.amountReward.node.color = this.colors[1];
        } else {
            this.coinIcon.spriteFrame = setting?.coinSprites[1];
            this.flagFill.active = true;
            this.amountReward.node.color = this.colors[1];
        }

        const isExpired = data.isExpired;
        this.flagOutdate.active = isExpired;
        if (isExpired) {
            this.flagFill.active = false;
            this.btSelect.interactable = false;
            this.coinIcon.spriteFrame = setting?.coinSprites[2];
            this.amountReward.node.color = this.colors[2];
            if (this.refreshDateColor) this.lblDate.node.color = this.colors[2];
        }
        const idxExpired = isExpired ? 1 : 0;
        for (let i = 0; i < this.headers.length; i++) {
            this.headers[i].node.active = i === idxExpired;
        }
        this.outline.active = data.showOutline;
    }

    private onSelect(): void {
        this._onSelected?.(this.index);
        // cv.MessageCenter.send(this.MsgSelectCalendarItem, this.index);
    }

    setOutline(selectedIdx: number): void {
        this.outline.active = this.index === selectedIdx;
    }
}
