import * as cr from 'common-resource';

const { ccclass, property } = cc._decorator;

@ccclass
export class ConsumingPromptControl extends cc.Component {
    @property(cc.Label) txtConsume: cc.Label = null;
    @property(cc.Label) txtRemain: cc.Label = null;

    show(consume: number, remain: number, duration: number = 1) {
        this.node.active = true;

        this.txtConsume.string = cr.CommonUtil.getShortOwnCoinString(consume);
        this.txtRemain.string = cr.CommonUtil.getShortOwnCoinString(remain);
        this.node.setPosition(0, -10); // Init position to make it move from bottom to center
        const moveTime = 1;
        this.node.runAction(cc.moveTo(moveTime, cc.v2(0, 0)).easing(cc.easeBackOut()));

        this.node.opacity = 1;
        this.node.runAction(cc.fadeIn(1));
        this.doFadeOut(duration + moveTime);
    }

    private doFadeOut(delayTime: number) {
        this.scheduleOnce(() => {
            this.node.runAction(
                cc.sequence(
                    cc.fadeOut(1),
                    cc.callFunc(() => {
                        this.node.active = false;
                    })
                )
            );
        }, delayTime);
    }
}
