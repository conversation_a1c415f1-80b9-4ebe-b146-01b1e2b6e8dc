/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import * as cr from '../../../common-resource/scripts/common-resource';

import { HumanboyDialogControl } from './HumanboyDialogControl';
import { HumanboyBetCoinControl } from './HumanboyBetCoinControl';
import { MiniGameDialog } from './mini-game-dialog/HumanboyMiniGameDialog';
import { MiniGameAdvancedAuto } from './advanced-auto-bet/HumanoboyMiniGameAdvanceAuto';
import { macros } from '../common/common-resource-macros';
import { ConcreteMiniGameDialog } from './mini-game-dialog/HumanboyConcreteMiniGameDialog';
import type { IMiniGameDialog, IMiniGameDialogConfig } from './mini-game-dialog/HumanboyIMiniGameDialog';

import COWBOY_LOCAL_ZORDER = macros.COWBOY_LOCAL_ZORDER;
import { ConcreteAdvancedAuto } from './common-resource-components-index';
import AutoBetLevel = pf.client.session.AutoBetLevel;

export class HumanboyAdvancedSettingCoinOptNode {
    index: number = 0;
    llAmountLevel: number = 0;
    nodeRoot: cc.Node = null;
    coin: HumanboyBetCoinControl = null;
    checkBox: cc.Toggle = null;
}

/**
 * 高级续投设置面板
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameAdvancedSettingControl extends cc.Component {
    @property(cc.Prefab) prefab_dialog: cc.Prefab = null; // 通用对话框 预制件

    @property(cc.Sprite) imgBgRoot: cc.Sprite = null;
    @property(cc.Button) btn_close: cc.Button = null;
    @property(cc.Button) btn_clean: cc.Button = null;
    @property(cc.Button) btn_ensure: cc.Button = null;
    @property(cc.Toggle) opt_setting_normal: cc.Toggle = null;
    @property(cc.Toggle) opt_setting_advance: cc.Toggle = null;
    @property(cc.Prefab) MiniGameAddAdvancedAuto_prefab: cc.Prefab = null;

    protected _maxSelectedCoinOptNode: number = 5; // 最大勾选"投注金币选项"个数
    protected _vCoinOptNode: HumanboyAdvancedSettingCoinOptNode[] = []; // "投注金币选项"数组
    protected _vSelectedCoinOptNode: HumanboyAdvancedSettingCoinOptNode[] = []; // "投注金币选项"队列

    protected _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集
    protected _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集
    protected _clickbyprogram: boolean = false;
    protected advanceAutoAddBet: cc.Node = null;

    protected _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;

    show(): void {
        this.node.active = true;
        this._updateView();
    }

    hide(): void {
        this.node.active = false;
    }

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room;

        this._initAtlasList();
        this._initLanguageUI();
        this._initUI();
    }

    protected start(): void {}

    // private _initAtlasList(): void {
    //     this._atlas_cb_language = cv.resMgr.getSpriteAtlas(cv.config.getLanguagePath('game/cowboyPlist/language'));
    //     this._atlas_hb_language = cv.resMgr.getSpriteAtlas(cv.config.getLanguagePath('game/humanboyPlist/language'));
    // }

    protected _initAtlasList(): void {
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Asset.COWBOY_LANGUAGE_ATLAS);
        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Asset.HUMANBOY_LANGUAGE_ATLAS);
    }

    protected _initLanguageUI(): void {
        let img_title: cc.Sprite = this.imgBgRoot.node.getChildByName('img_title').getComponent(cc.Sprite);
        img_title.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_advanced_setting_txt_title');

        this.btn_clean.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame(
            'humanboy_advanced_setting_txt_clean'
        );

        let btn_clean_sprite: any = this.btn_clean.getComponent(cc.Sprite);

        btn_clean_sprite.srcBlendFactor = cc.macro.BlendFactor.SRC_ALPHA;
        btn_clean_sprite.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;

        let frame_tips_ok: cc.SpriteFrame = this._atlas_cb_language.getSpriteFrame('tips_ok');
        let frame_tips_ok_disable: cc.SpriteFrame = this._atlas_cb_language.getSpriteFrame('tips_ok_disable');
        this.btn_ensure.getComponent(cc.Sprite).spriteFrame = frame_tips_ok;
        this.btn_ensure.normalSprite = frame_tips_ok;
        this.btn_ensure.pressedSprite = frame_tips_ok;
        this.btn_ensure.hoverSprite = frame_tips_ok;
        this.btn_ensure.disabledSprite = frame_tips_ok_disable;
    }

    protected _initUI(): void {
        this.btn_close.node.on('click', (sender: cc.Node) => {
            pf.audioManager.playSoundEffect(macros.Audio.COMMON_CLOSE);
            this.hide();
        });
        this.btn_clean.node.on('click', (sender: cc.Node) => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            this._clearAllSelected();
        });
        this.btn_ensure.node.on('click', this._onClickBtnEnsure, this);

        // 默认勾选普通续投
        this.opt_setting_normal.check();

        // 初始化单选按钮组
        let panel_opts: cc.Node = this.imgBgRoot.node.getChildByName('panel_opts');
        let count: number = panel_opts.childrenCount;
        for (let i = 0; i < count; ++i) {
            let coinOptNode: HumanboyAdvancedSettingCoinOptNode = new HumanboyAdvancedSettingCoinOptNode();
            this._vCoinOptNode.push(coinOptNode);

            coinOptNode.index = i;
            coinOptNode.nodeRoot = panel_opts.getChildByName(pf.StringUtil.formatC('node_%d', i));
            coinOptNode.coin = coinOptNode.nodeRoot.getChildByName('coin').getComponent(HumanboyBetCoinControl);
            coinOptNode.checkBox = coinOptNode.nodeRoot.getChildByName('checkBox').getComponent(cc.Toggle);
            coinOptNode.checkBox.uncheck();
            coinOptNode.checkBox.node.on('toggle', (toggle: cc.Toggle) => {
                if (!this._clickbyprogram) {
                    pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                }
                this._clickbyprogram = false;
                this._makeSelectedByIndex(coinOptNode.index);
            });
        }
        this.opt_setting_normal.node.on('toggle', (toggle: cc.Toggle) => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        });
        this.opt_setting_advance.node.on('toggle', (toggle: cc.Toggle) => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        });
    }

    protected _updateView(): void {
        this._updateStaticText();
        this._updateAllOptBtn();
    }

    protected _onClickBtnEnsure(sender: cc.Node): void {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        let iUsedAutoBetCount = 0;
        let iSelectAutoBetCount = 0;
        let reachLimitBet = false;

        iUsedAutoBetCount = this._miniGameRoom.betSettings.usedAutoBetCount;
        iSelectAutoBetCount = this._miniGameRoom.betSettings.selectAutoBetCount;
        reachLimitBet = this._miniGameRoom.betSettings.reachLimitBet;

        let reqSetGameOption: () => void = (): void => {
            let vAmountLevel: number[] = [];
            for (const node of this._vSelectedCoinOptNode) {
                vAmountLevel.push(cr.CurrencyUtil.convertToServerAmount(node.llAmountLevel));
            }

            const autoLevel = this.opt_setting_normal.isChecked
                ? AutoBetLevel.Level_Normal
                : AutoBetLevel.Level_Advance;

            this._miniGameRoom.setGameOption(autoLevel, vAmountLevel);
            this.hide();
        };

        // 若是从高级续投状态切换到普通续投, 则应该弹框提示
        if (this.opt_setting_normal.isChecked && iSelectAutoBetCount > 0) {
            const dialogNode = cc.instantiate(this.prefab_dialog);
            const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
            this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

            const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
            const stringContent = pf.StringUtil.formatC(
                pf.languageManager.getString('Cowboy_auto_bet_switch_tips'),
                iUsedAutoBetCount,
                iSelectAutoBetCount
            );
            const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_switch_auto_bet');
            const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet');

            const cbLeftBtn = (dialog: IMiniGameDialog) => {
                reqSetGameOption();
                this._miniGameRoom.cancelAdavnceAutoBet();
                this.hide();
            };
            const cbRightBtn = (dialog: IMiniGameDialog) => {
                miniGameDialog?.close();
                this.hide();
            };
            const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
            const cbCenterBtn = (dialog: MiniGameDialog) => {
                this._showAutoAddBetList(dialog);
            };

            const _onUpdateContent = (dialog: IMiniGameDialog) => {
                const iUsedAutoBetCount = this._miniGameRoom.betSettings.usedAutoBetCount;
                const iSelectAutoBetCount = this._miniGameRoom.betSettings.selectAutoBetCount;
                const reachLimitBet = this._miniGameRoom.betSettings.reachLimitBet;

                if (legacyDialog) {
                    legacyDialog.txt_content.string = cr.UIUtil.calculateAutoWrapString(
                        legacyDialog.txt_content.node,
                        pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_switch_tips'),
                            iUsedAutoBetCount,
                            iSelectAutoBetCount
                        )
                    );
                }
                if (reachLimitBet) {
                    miniGameDialog?.blockCenterButton();
                }
            };
            const miniGameDialogConfig: IMiniGameDialogConfig = {
                miniDialog: miniGameDialog,
                stringContent,
                stringLeftBtn,
                stringRightBtn,
                cbLeftBtn,
                cbRightBtn,
                isReachedMax: reachLimitBet,
                legacyDialog,
                isShowBtnCenter: true,
                stringCenterButton: stringCenter,
                cbCenterBtn,
                onUpdateContent: _onUpdateContent
            };

            ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
        } else {
            reqSetGameOption();
        }
    }

    protected _updateStaticText(): void {
        let txt_desc_word: cc.Label = this.imgBgRoot.node.getChildByName('txt_desc_word').getComponent(cc.Label);
        txt_desc_word.string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_advancedSetting_desc'),
            this._maxSelectedCoinOptNode
        );

        let txt_auto_word: cc.Label = this.imgBgRoot.node.getChildByName('txt_auto_word').getComponent(cc.Label);
        txt_auto_word.string = pf.languageManager.getString('Humanboy_advancedSetting_auto');

        let txt_opt_normal_word: cc.Label = this.imgBgRoot.node
            .getChildByName('txt_opt_normal_word')
            .getComponent(cc.Label);
        txt_opt_normal_word.string = pf.languageManager.getString('Humanboy_advancedSetting_opt_normal');

        let txt_opt_advance_word: cc.Label = this.imgBgRoot.node
            .getChildByName('txt_opt_advance_word')
            .getComponent(cc.Label);
        txt_opt_advance_word.string = pf.languageManager.getString('Humanboy_advancedSetting_opt_advance');

        let txt_opt_advance_word_size = cr.UIUtil.updateAndMeasureLabel(txt_opt_advance_word);

        let txt_opt_advance_extra_word: cc.Label = this.imgBgRoot.node
            .getChildByName('txt_opt_advance_extra_word')
            .getComponent(cc.Label);
        txt_opt_advance_extra_word.string = pf.languageManager.getString('Humanboy_advancedSetting_opt_advance_extra');
        txt_opt_advance_extra_word.node.setPosition(
            txt_opt_advance_word.node.position.x + txt_opt_advance_word_size.width + 10,
            txt_opt_advance_extra_word.node.position.y
        );
    }

    protected _checkCoinOptsStatus(): void {
        if (this._vSelectedCoinOptNode.length > this._maxSelectedCoinOptNode) {
            let tSelectedCoinOptNode: HumanboyAdvancedSettingCoinOptNode = this._vSelectedCoinOptNode[0];
            this._vSelectedCoinOptNode.splice(0, 1);
            tSelectedCoinOptNode.checkBox.uncheck();
        }

        let enabled: boolean = this._vSelectedCoinOptNode.length === this._maxSelectedCoinOptNode;
        this.btn_ensure.interactable = enabled;
    }

    protected _updateAllOptBtn(): void {
        // 续投等级 1 高级  0 普通
        this.opt_setting_normal.uncheck();
        this.opt_setting_advance.uncheck();

        // 重置
        this._resetAllOptBtn();

        let bNormalCkeck = true;
        let vTotalOption: number[] = [];
        let vSelectOption: number[] = [];
        let llCoinUICritical = 0;

        bNormalCkeck = this._miniGameRoom.betSettings.autoBetLevel === AutoBetLevel.Level_Normal;

        vTotalOption = this._miniGameRoom.roomParams.totalAmountLevel;
        vSelectOption = this._miniGameRoom.betSettings.betCoinOptions;
        llCoinUICritical = this._miniGameRoom.llCoinUICritical;

        this._updateOptBtnStatus(bNormalCkeck, vTotalOption, vSelectOption, llCoinUICritical);
    }

    protected _resetAllOptBtn(): void {
        for (const node of this._vCoinOptNode) {
            node.llAmountLevel = 0;
            node.coin.setShape(HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN);
            node.coin.setTxtNum(cr.CurrencyUtil.applyDisplayRatioToNumber(node.llAmountLevel));
            node.coin.txtBetNode.setPosition(cc.Vec2.ZERO);
            this._clickbyprogram = true;
            node.checkBox.uncheck();
        }

        pf.DataUtil.clearArray(this._vSelectedCoinOptNode);
        this._checkCoinOptsStatus();
    }

    protected _updateOptBtnStatus(
        bNormalCkeck: boolean,
        vTotalOption: number[],
        vSelectOption: number[],
        llCoinUICritical: number
    ): void {
        if (bNormalCkeck) this.opt_setting_normal.check();
        else this.opt_setting_advance.check();

        let iMinCount: number = Math.min(this._vCoinOptNode.length, vTotalOption.length);
        for (let i = 0; i < iMinCount; ++i) {
            let llAmountLevel: number = cr.CurrencyUtil.convertToClientAmount(vTotalOption[i]);
            let shapeType: number =
                llAmountLevel < llCoinUICritical
                    ? HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN
                    : HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK;

            this._vCoinOptNode[i].llAmountLevel = llAmountLevel;
            this._vCoinOptNode[i].coin.setShape(shapeType);
            this._vCoinOptNode[i].coin.setTxtNum(
                cr.CurrencyUtil.applyDisplayRatioToNumber(this._vCoinOptNode[i].llAmountLevel)
            );
            this._vCoinOptNode[i].coin.txtBetNode.setPosition(cc.Vec2.ZERO);
        }

        iMinCount = Math.min(this._vCoinOptNode.length, vSelectOption.length);
        for (let i = 0; i < iMinCount; ++i) {
            let llAmountLevel: number = cr.CurrencyUtil.convertToClientAmount(vSelectOption[i]);
            for (const node of this._vCoinOptNode) {
                // 这里加了一个判断条件, 为了兼容多个一样数值的选项
                if (node.llAmountLevel === llAmountLevel && !node.checkBox.isChecked) {
                    this._clickbyprogram = true;
                    node.checkBox.check();
                    this._makeSelectedByIndex(node.index);
                    break;
                }
            }
        }
    }

    protected _makeSelectedByIndex(index: number): void {
        if (index < 0 || index >= this._vCoinOptNode.length) return;

        let bDuplicate = false;
        let nDuplicateIndex = 0;
        let coinOptNode: HumanboyAdvancedSettingCoinOptNode = this._vCoinOptNode[index];
        for (let i = 0; i < this._vSelectedCoinOptNode.length; ++i) {
            if (coinOptNode.index === this._vSelectedCoinOptNode[i].index) {
                bDuplicate = true;
                nDuplicateIndex = i;
                break;
            }
        }

        let bSelected: boolean = coinOptNode.checkBox.isChecked;
        if (bSelected) {
            if (!bDuplicate) {
                this._vSelectedCoinOptNode.push(coinOptNode);
            }
        } else {
            if (bDuplicate) {
                this._vSelectedCoinOptNode.splice(nDuplicateIndex, 1);
            }
        }

        this._checkCoinOptsStatus();
    }

    protected _showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this.advanceAutoAddBet) {
            this.advanceAutoAddBet = cc.instantiate(this.MiniGameAddAdvancedAuto_prefab);
            this.node.addChild(this.advanceAutoAddBet, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT);
        }
        const miniGameAdvanceAuto = this.advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    protected _clearAllSelected(): void {
        for (const node of this._vCoinOptNode) {
            node.checkBox.uncheck();
        }

        pf.DataUtil.clearArray(this._vSelectedCoinOptNode);
        this._checkCoinOptsStatus();
    }
}
