import type { IMiniGameDialog } from './HumanboyIMiniGameDialog';

export abstract class MiniGameDialogBase {
    protected implementation: IMiniGameDialog;

    constructor(implementation: IMiniGameDialog) {
        this.implementation = implementation;
    }

    abstract show(
        str_content: string,
        str_btn_left: string,
        str_btn_right: string,
        cb_left: (dialog: IMiniGameDialog) => void,
        cb_right: (dialog: IMiniGameDialog) => void
    ): void;
    abstract close(): void;
    abstract setLegacyDialog(dialog: IMiniGameDialog): void;
    abstract showCenterButton(str_btn_center: string, cb_center: (dialog: IMiniGameDialog) => void): void;
}
