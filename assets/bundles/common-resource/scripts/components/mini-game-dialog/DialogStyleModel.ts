import type { IMiniGameDialogStyleConfig } from './HumanboyIMiniGameDialog';

export enum ThemeSystemType {
    Default = 0,
    TwoButton_NoMoney_Style = 1
}

export default class DialogStyleModel {
    private static instance: DialogStyleModel;
    private themeData: any = {};

    static getInstance(): DialogStyleModel {
        if (!this.instance) {
            this.instance = new DialogStyleModel();
        }
        return this.instance;
    }
    constructor() {
        const twoButton: IMiniGameDialogStyleConfig = {
            spacing: 72,
            leftBtnSize: new cc.Vec2(300, 92),
            rightBtnSize: new cc.Vec2(300, 92),
            fontSize: 48
        };

        this.themeData = {
            [ThemeSystemType.TwoButton_NoMoney_Style]: twoButton
        };
    }

    getTheme(theme: ThemeSystemType): IMiniGameDialogStyleConfig {
        return this.themeData[theme];
    }
}
