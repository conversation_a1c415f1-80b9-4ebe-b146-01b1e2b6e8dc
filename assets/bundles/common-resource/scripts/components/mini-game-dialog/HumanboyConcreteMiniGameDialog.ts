/* eslint-disable camelcase */
import type { ThemeSystemType } from './DialogStyleModel';
import type { IMiniGameDialog, IMiniGameDialogConfig } from './HumanboyIMiniGameDialog';
import { MiniGameDialogBase } from './HumanboyMiniGameDialogBase';

export class ConcreteMiniGameDialog extends MiniGameDialogBase {
    updateCenterButton(value?: number): void {
        this.implementation?.updateCenterButton(value);
    }

    setLegacyDialog(dialog: IMiniGameDialog): void {
        this.implementation?.setLegacyDialog(dialog);
    }

    show(
        str_content: string,
        str_btn_left: string,
        str_btn_right: string,
        cb_left: (dialog: IMiniGameDialog) => void,
        cb_right: (dialog: IMiniGameDialog) => void,
        onUpdateContent?: (dialog: IMiniGameDialog) => void
    ): void {
        this.implementation?.show(str_content, str_btn_left, str_btn_right, cb_left, cb_right, onUpdateContent);
    }

    close(): void {
        this.implementation?.close();
    }

    showCenterButton(str_btn_center: string, cb_center: (dialog: IMiniGameDialog) => void): void {
        this.implementation?.showCenterButton(str_btn_center, cb_center);
    }

    blockCenterButton(): void {
        this.implementation?.blockCenterButton();
    }

    setTheme(themeType: ThemeSystemType) {
        this.implementation?.setTheme(themeType);
    }

    static showDialog(config: IMiniGameDialogConfig) {
        const {
            miniDialog,
            stringContent,
            stringLeftBtn,
            stringRightBtn,
            cbLeftBtn,
            cbRightBtn,
            isReachedMax,
            legacyDialog,
            isShowBtnCenter,
            stringCenterButton,
            cbCenterBtn,
            onUpdateContent,
            themeType
        } = config;

        const dialog = new ConcreteMiniGameDialog(miniDialog ? miniDialog : legacyDialog);
        if (!!legacyDialog && miniDialog) {
            dialog.setLegacyDialog(legacyDialog);
        }

        dialog.show(stringContent, stringLeftBtn, stringRightBtn, cbLeftBtn, cbRightBtn, onUpdateContent);

        if (!!isShowBtnCenter && miniDialog) {
            dialog.showCenterButton(stringCenterButton, cbCenterBtn);
        }

        if (isReachedMax) {
            dialog.blockCenterButton();
        }

        dialog.setTheme(themeType);
    }
}
