import type { ThemeSystemType } from './DialogStyleModel';

export interface IMiniGameDialog {
    show(
        str_content: string,
        str_btn_left: string,
        str_btn_right: string,
        cb_left: (dialog: IMiniGameDialog) => void,
        cb_right: (dialog: IMiniGameDialog) => void,
        onUpdateContent?: (dialog: IMiniGameDialog) => void
    ): void;
    close(): void;
    setLegacyDialog?(dialog: IMiniGameDialog): void;
    showCenterButton?(str_btn_center: string, cb_center: (dialog: IMiniGameDialog) => void): void;
    updateCenterButton?(value?: number): void;
    blockCenterButton?(): void;
    setTheme?(themeType: ThemeSystemType): void;
}

export interface IMiniGameDialogConfig {
    miniDialog: IMiniGameDialog;
    stringContent: string;
    stringLeftBtn: string;
    stringRightBtn: string;
    cbLeftBtn: (dialog: IMiniGameDialog) => void;
    cbRightBtn: (dialog: IMiniGameDialog) => void;
    isReachedMax: boolean;
    legacyDialog?: IMiniGameDialog;
    isShowBtnCenter?: boolean;
    stringCenterButton?: string;
    cbCenterBtn?: (dialog: IMiniGameDialog) => void;
    onUpdateContent?: (dialog: IMiniGameDialog) => void;
    themeType?: ThemeSystemType;
}

export interface IMiniGameDialogStyleConfig {
    leftBtnSize: cc.Vec2;
    rightBtnSize: cc.Vec2;
    spacing: number;
    fontSize?: number;
    leftPading?: number;
    rightPading?: number;
}
