/* eslint-disable camelcase */
import { HumanboyDialogControl } from '../HumanboyDialogControl';
import * as pf from '../../../../../poker-framework/scripts/pf';
import DialogStyleModel, { ThemeSystemType } from './DialogStyleModel';
import type { IMiniGameDialog } from './HumanboyIMiniGameDialog';
import { macros } from '../../common/common-resource-macros';

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameDialog extends cc.Component implements IMiniGameDialog {
    @property(cc.Node) center_btn_arrow: cc.Node = null;
    @property(cc.Button) btn_center: cc.Button = null;
    @property(cc.Layout) buttonGroup: cc.Layout = null;

    protected _cb_center: (dialog: IMiniGameDialog) => void = null;
    protected _onUpdateContent: (dialog: IMiniGameDialog) => void = null;
    protected legacyDialog: IMiniGameDialog;
    protected _miniGameRoom: pf.Nullable<pf.services.MiniGameRoom<pf.services.IMiniGameRoomEvents>> = null;

    protected _boundOnUpdateContent: any = null;

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room as pf.services.MiniGameRoom<pf.services.IMiniGameRoomEvents>;

        this.btn_center.node.on('click', (sender: cc.Node): void => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            if (this._cb_center) {
                this.center_btn_arrow.setScale(1, -1);
                this._cb_center(this);
            }
        });

        this.btn_center.node.active = false;
    }

    protected onDestroy(): void {
        this.unregisterNotifications();
    }

    show(
        str_content: string,
        str_btn_left: string,
        str_btn_right: string,
        cb_left: (dialog: IMiniGameDialog) => void,
        cb_right: (dialog: IMiniGameDialog) => void,
        onUpdateContent?: (dialog: IMiniGameDialog) => void
    ): void {
        this._onUpdateContent = onUpdateContent;
        this.legacyDialog?.show(str_content, str_btn_left, str_btn_right, cb_left, cb_right);
        if (this._onUpdateContent) {
            this._boundOnUpdateContent = (usedBetCount: number, autoBetCount: number) => {
                this._onUpdateContent(null);
            };
            this._miniGameRoom.addListener('advanceAutoBetCountAdd', this._boundOnUpdateContent);
            this._miniGameRoom.addListener('advanceAutoBet', this._boundOnUpdateContent);
        }
    }

    showCenterButton(str_btn_center: string, cb_center: (dialog: IMiniGameDialog) => void): void {
        this.btn_center.node.active = true;
        this._cb_center = cb_center;
        let txt_btn_center: cc.Label = this.btn_center.node.getChildByName('txt').getComponent(cc.Label);
        txt_btn_center.string = str_btn_center;
    }

    blockCenterButton(): void {
        const txt_btn_center: cc.Node = this.btn_center.node.getChildByName('txt');

        this.btn_center.interactable = false;
        this.btn_center.node.targetOff('click');
        txt_btn_center.color = cc.Color.GRAY;
        this.btn_center.node.color = cc.Color.GRAY;
        this.center_btn_arrow.color = cc.Color.GRAY;
    }

    close(): void {
        this.unregisterNotifications();
        this.legacyDialog?.close();
        this.btn_center.node.active = false;
    }

    setLegacyDialog(dialog: IMiniGameDialog): void {
        this.legacyDialog = dialog;
    }

    updateCenterButton(value?: number): void {
        this.center_btn_arrow?.setScale(1, 1);
    }

    setTheme(themeType: ThemeSystemType): void {
        const theme = DialogStyleModel.getInstance().getTheme(themeType);

        if (!theme) {
            return;
        }
        const { leftBtnSize, rightBtnSize, spacing, fontSize } = theme;

        switch (themeType) {
            case ThemeSystemType.TwoButton_NoMoney_Style: {
                this.btn_center.node.active = false;
                const dialog: HumanboyDialogControl = this.node.getComponent(HumanboyDialogControl);
                dialog.btn_left.node.width = leftBtnSize.x;
                dialog.btn_left.node.height = leftBtnSize.y;

                dialog.btn_right.node.width = rightBtnSize.x;
                dialog.btn_right.node.height = rightBtnSize.y;
                if (fontSize) {
                    dialog.btn_left.node.getChildByName('txt').getComponent(cc.Label).fontSize = fontSize;
                    dialog.btn_right.node.getChildByName('txt').getComponent(cc.Label).fontSize = fontSize;
                }
                this.buttonGroup.spacingX = spacing;
                break;
            }
        }
    }

    unregisterNotifications(): void {
        if (this._boundOnUpdateContent) {
            this._miniGameRoom.removeListener('advanceAutoBetCountAdd', this._boundOnUpdateContent);
            this._miniGameRoom.removeListener('advanceAutoBet', this._boundOnUpdateContent);
            this._boundOnUpdateContent = null;
        }
    }
}
