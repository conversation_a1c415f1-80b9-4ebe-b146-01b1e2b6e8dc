/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/common-resource-macros';

import type { AdvancedAuto } from '../common-resource-components-index';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { TagControl } from '../../../../common/scripts/components/TagControl';

/**
 * This is AutoBetAdvanced use for all mini casino games.
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameAdvancedAuto extends cc.Component implements AdvancedAuto {
    protected legacyAdvancedAuto: AdvancedAuto;

    _panel_select: cc.Node = null; // 高级续投选择面板
    _ps_block_panel: cc.Node = null; // 高级续投选择面板 选择次数面板
    _ps_btn_cancel: cc.Button = null; // 高级续投选择面板 取消按钮
    protected _miniGameRoom: pf.Nullable<pf.services.IMiniGameRoom> = null;

    protected _onBetCountUpdate: (value?: number) => void = undefined;

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._miniGameRoom = context.room;

        this.legacyAdvancedAuto = this.node.getComponent('HumanboyAdvancedAutoControl');
        this._panel_select = this.node.getChildByName('panel_select');
        this._panel_select.targetOff(this);
        this._panel_select.on(cc.Node.EventType.TOUCH_END, (sender: cc.Node): void => {
            this.hideSelectPanel(true);
        });
        this._ps_block_panel = this._panel_select.getChildByName('panel_block');
        this._ps_btn_cancel = this._ps_block_panel.getChildByName('btn_cancel').getComponent(cc.Button);
        // this._ps_btn_cancel.node.targetOff(this);
        this._ps_btn_cancel.node.on('click', (sender: cc.Node): void => {
            this.hideSelectPanel(true);
        });
    }

    // Implement AdvancedAuto
    betCountUpdateCallback(callback: (value?: number) => void): void {
        this._onBetCountUpdate = callback;
    }

    showSelectPanel(bAnim: boolean, bReset?: boolean): void {
        if (this.legacyAdvancedAuto?._panel_select.active) return;
        this.legacyAdvancedAuto._panel_select.active = true;
        this._layoutSelectPanelOnce(bReset);
        this.legacyAdvancedAuto?._autoSelectAnimFunc(true, bAnim);
    }

    hideSelectPanel(bAnim: boolean): void {
        this.legacyAdvancedAuto?.hideSelectPanel(bAnim);
        this._onBetCountUpdate && this._onBetCountUpdate();
        this._onBetCountUpdate = null;
    }

    adaptSelectPanelPos(benchMarkNode: cc.Node): void {
        this.legacyAdvancedAuto?.adaptSelectPanelPos(benchMarkNode);
    }

    showAdvanceAutoTips(strTips: string): void {
        this.legacyAdvancedAuto?.showAdvanceAutoTips(strTips);
    }

    hideAdvanceAutoTips(): void {
        this.legacyAdvancedAuto?.hideAdvanceAutoTips();
    }

    adaptAdvanceAutoTipsPos(benchMarkNode: cc.Node): void {
        this.legacyAdvancedAuto?.adaptAdvanceAutoTipsPos(benchMarkNode);
    }

    showAdvanceAutoCount(): void {
        this.legacyAdvancedAuto?.showAdvanceAutoCount();
    }

    hideAdvanceAutoCount(): void {
        this.legacyAdvancedAuto?.hideAdvanceAutoCount();
    }

    adaptAdvanceAutoCountPos(benchMarkNode: cc.Node): void {
        this.legacyAdvancedAuto?.adaptAdvanceAutoCountPos(benchMarkNode);
    }

    /**
     * Start Modify ----------
     */
    protected _layoutSelectPanelOnce(bReset: boolean): void {
        if (bReset) this.legacyAdvancedAuto._ps_layout_once = false;
        if (this.legacyAdvancedAuto._ps_layout_once) return;
        this.legacyAdvancedAuto._ps_layout_once = true;

        let vBtns: cc.Button[] = [];
        const vAutoBetCountList = this._miniGameRoom.betSettings.autoBetCountList;
        // NOTE:
        vBtns.push(this._ps_btn_cancel);

        // 先清除UI
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < this.legacyAdvancedAuto._ps_btns.length; ++i) {
            this.legacyAdvancedAuto._ps_btns[i].node.removeFromParent(true);
            cr.UIUtil.destroyNode(this.legacyAdvancedAuto._ps_btns[i].node);
        }
        pf.DataUtil.clearArray(this.legacyAdvancedAuto._ps_btns);

        // 再添加
        for (let i = 0; i < vAutoBetCountList.length; ++i) {
            let strBtnName: string = pf.StringUtil.formatC('btn_auto_%d', i);
            let btnNode: cc.Node = this.legacyAdvancedAuto._ps_block_panel.getChildByName(strBtnName);
            if (!btnNode) {
                btnNode = cc.instantiate(this.legacyAdvancedAuto._ps_btn_cancel.node);
                btnNode.name = strBtnName;
                btnNode.on('click', this._onClickAutoSelect, this);
                this.legacyAdvancedAuto._ps_block_panel.addChild(btnNode);
                this.legacyAdvancedAuto._ps_btns.push(btnNode.getComponent(cc.Button));
            }

            let tag: TagControl = btnNode.getComponent(TagControl);
            if (!tag) tag = btnNode.addComponent(TagControl);
            tag.nTag = vAutoBetCountList[i];

            let txt: cc.Label = btnNode.getChildByName('txt').getComponent(cc.Label);
            txt.string = pf.StringUtil.formatC(
                pf.languageManager.getString('MiniGame_Select_AddAutoBet_Text'),
                vAutoBetCountList[i]
            );

            vBtns.push(btnNode.getComponent(cc.Button));
        }

        // 计算真实大小
        let szPanel: cc.Size = cc.size(
            this.legacyAdvancedAuto._ps_cell_size.width,
            this.legacyAdvancedAuto._ps_cell_size.height * vBtns.length
        );
        this.legacyAdvancedAuto._ps_block_panel.setContentSize(szPanel);
        // 计算位置
        for (let i = 0; i < vBtns.length; ++i) {
            let x: number = szPanel.width / 2;
            let y: number = szPanel.height / vBtns.length / 2 + i * this.legacyAdvancedAuto._ps_cell_size.height;
            vBtns[i].node.setPosition(x, y);
        }
        // disable default cancel btn
        // this._ps_btn_cancel.node.active = false;
    }

    protected _onClickAutoSelect(sender: cc.Node): void {
        let iCount = 0;
        let tag: TagControl = sender.getComponent(TagControl);
        if (tag) iCount = tag.nTag;

        pf.audioManager.playSoundEffect(macros.Audio.Tab);
        this._miniGameRoom.addAdavnceAutoBetCount(iCount);

        this.hideSelectPanel(false);
    }
}
