export namespace macros {
    export const BUNDLE_NAME = 'common-resource';
    export const SHOP_SIGN = 'JWF7d20kSGdrQntCNiFTUGwraiRWejlCM3QkX3pedUw';
    export const UUID_WEB = 'd41d8cd98f00b204e9800998ecf8427e';
    export const ZORDER_TT = 12; // temp z order from PKW
    export const RADIX_DECIMAL = 10;

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/common-addressable-assets-zh_cn',
        EN_US = 'configs/common-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph',
        HI_IN = 'languages/string-hi_in'
    }

    export enum COWBOY_LOCAL_ZORDER {
        COWBOY_LOCAL_ZORDER_DUMMY = 0,
        COWBOY_LOCAL_ZORDER_IMG_HEAD,
        COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT,
        COWBOY_LOCAL_ZORDER_COIN_NODE,
        COWBOY_LOCAL_ZORDER_ANIM_NODE,
        COWBOY_LOCAL_ZORDER_TIMELINE_NODE,
        COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT, // 高级续投选择面板
        COWBOY_LOCAL_ZORDER_REWRAD_TIP, // 中奖提示面板
        COWBOY_LOCAL_ZORDER_RED_PACKAGE, // 红包面板
        COWBOY_LOCAL_ZORDER_MENU_PANEL = 99,
        COWBOY_LOCAL_ZORDER_TOAST,
        COWBOY_LOCAL_ZORDER_GUIDE,
        COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT
    }

    export enum CardNum {
        CARD_2 = 0,
        CARD_3,
        CARD_4,
        CARD_5,
        CARD_6,
        CARD_7,
        CARD_8,
        CARD_9,
        CARD_10,
        CARD_J,
        CARD_Q,
        CARD_K,
        CARD_A,
        CARD_INVALID
    }

    /**
     * 牌花色
     */
    export enum CardSuit {
        CARD_DIAMOND = 0, // 方片
        CARD_CLUB, // 梅花
        CARD_HEART, // 红心
        CARD_SPADE, // 黑桃
        CardSuit_MAX
    }

    export enum Asset {
        MINI_GAME_EXIT = 'common-resource.mini-game-exit',
        COWBOY_LANGUAGE_ATLAS = 'common-resource.cowboy-language-atlas',
        HUMANBOY_LANGUAGE_ATLAS = 'common-resource.humanboy-language-atlas',
        HUMANBOY_EXCHANGE_ATLAS = 'common-resource.humanboy-exchange-atlas',
        HUMANBOY_ATLAS = 'common-resource.humanboy-atlas',
        HEAD_POINTS_ANI = 'common-resource.head-points-ani'
    }

    export enum Audio {
        BUTTON_CLICK = 'common-resource-audio.button-click',
        COMMON_CLOSE = 'common-resource-audio.common-close',
        Tab = 'common-resource-audio.tab',
        BACK_BUTTON = 'common-resource-audio.back-button',
        DEAL_CARD = 'common-resource-audio.deal-card'
    }

    export enum DynamicAsset {
        TOAST_BG_PORTRAIT = 'common-resource-dynamic.toast-bg-portrait',
        TOAST_BG_LANDSCAPE = 'common-resource-dynamic.toast-bg-landscape',
        REBATE_TOAST = 'common-resource-dynamic.rebate-toast',
        REBATE_TOAST_NEW = 'common-resource-dynamic.rebate-toast-new'
    }

    export enum AudioSettingKeys {
        MUSIC = 'client_music_key',
        SOUND_EFFECT = 'client_sound_key'
    }

    export enum SCENE {
        TransitionScene = 'TransitionScene', // 过渡场景
        LOADING_SCENE = 'LoadingScene', // 加载场景
        LOGIN_SCENE = 'LoginScene', // 登陆场景
        HALL_SCENE = 'HallScene', // 大厅场景
        CLUB_SCENE = 'ClubScene', // 俱乐部场景
        GAME_SCENE = 'Game', // 游戏场景
        GAME_SCENE_AOF = 'GameAof', // 游戏场景
        COWBOY_SCENE = 'CowboyScene', // 德州牛仔
        VIDEOCOWBOY_SCENE = 'VideoCowboyScene', // 视频牛仔
        HUMANBOY_SCENE = 'HumanboyScene', // 百人德州
        POKERMASTER_SCENE = 'PokerMasterScene', // 扑克大师
        JACKFRUIT_SCENE = 'JackfruitScene', // 菠萝蜜
        HOTUPDATE_SCENE = 'HotUpdate', // 热更新场景
        SPORTS_SCENE = 'SportsScene', // 体育赛事
        POCKETGAME_SCENE = 'PocketGameScene', // 电子小游戏
        BLACKJACK_SCENE = 'BlackJackScene',
        FISHINGKING_SCENE = 'FishingKingScene',
        TOPMATCHE_SCENE = 'TopMatcheScene', // 一起看球
        // BLACKJACKPVP_SCENE = 'BlackjackPVP',             // 21点
        PKF_LIVE_SCENE = 'PKFLiveScene', // PKF 直播
        CARIBBEAN_POKER_SCENE = 'CaribbeanPokerScene', // Caribbean Stud Poker
        WEALTHTRIO_SCENE = 'WealthTrioScene',
        ISLOT_SCENE = 'ISlotScene' // ISlot slot machine
    }
}
