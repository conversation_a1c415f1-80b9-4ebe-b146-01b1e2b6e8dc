/* eslint-disable complexity */
import * as pf from '../../../../poker-framework/scripts/pf';
import { commonResourceAgent } from '../common-resource-agent';

export default class ErrorMessageControl {
    private _errorMessageService: pf.services.ErrorMessageService = null;

    constructor() {
        this._errorMessageService = pf.serviceManager.get(pf.services.ErrorMessageService);
        this._errorMessageService.addListener('handleError', this.onHandleError.bind(this));
    }

    onHandleError(error: number) {
        cc.log(`[ErrorMessageControl::onHandleError]:${error}`);
        if (error === 1 || error === 0) {
            return;
        }

        let finalError = error;
        if (error === 1203) {
            // 1203显示1201的提示
            finalError = 1201;
        } else if (error === 1275) {
            finalError = 87;
        }

        const errorDescription = pf.languageManager.getString(`ServerErrorCode${finalError}`) || '';

        switch (finalError) {
            case 22:
                // this.MessageCenter.send('on_room_not_exist');
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            case 32:
            case 42:
            case 53:
            case 98:
                // this.GameDataManager.tRoomData.isShowNeedShop = true;
                // this.MessageCenter.send('on_need_more_gold');
                // this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            case 105:
                // this.TP.showMsg(acBuffer, this.Enum.ButtonStyle.GOLD_BUTTON, null);
                // let image = this.TP.getMessageImage();
                // let msgTxt = this.TP.getMessageImageText();
                // let msgtext = this.TP.getMessageText();
                // image.node.active = true;
                // msgTxt.string = this.config.getStringData('TipsPanel_Image_duanxin_text_duanxin_2');
                // msgTxt.node.active = true;
                // this.resMgr.setSpriteFrame(image.node, 'zh_CN/hall/ui/common_prompt');
                // msgtext.node.setPosition(msgtext.node.x, 0);
                break;
            case 112:
                // this.GameDataManager.tRoomData.isShowNeedClub = true;
                // this.MessageCenter.send('needClub');
                break;
            case 48:
            case 51:
            case 92:
                // this.GameDataManager.tGameData.m_bIsOnSelfAction = false;
                // this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            case 49:
            case 50:
            case 71:
                // this.GameDataManager.tGameData.m_bIsOnSelfAction = false;
                // this.MessageCenter.send('on_action_error');
                // this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            case 93:
                // this.GameDataManager.tGameData.m_bIsOnSelfAction = true;
                // this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            case 2:
            case 229:
                // this.netWorkManager.OnNeedRelogin(i32Error);
                break;
            case 2000:
                // this.roomManager.setIsSpectatorRevealEnabled(false);
                // acBuffer = this.config.getStringData('ForceShowCardToastError');
                // this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
                commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString('ForceShowCardToastError'));
                break;
            case 31119:
            case 31120:
                // this.handleGeoComplyError(headerServerid, headerRoomid, data);
                break;
            case 31123:
                // this.netWorkManager.OnNeedRelogin(i32Error);
                commonResourceAgent.toastMessage.showMsg(errorDescription);
                break;
            default:
                // if (this.C2CNotify.isOrderActive() && this.GameDataManager.tRoomData.isZoom()) {
                //     this.C2CNotify.onStandupSuccess();
                // } else {
                //     this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
                // }
                commonResourceAgent.toastMessage.showMsg(errorDescription);
        }

        /*
        if (i32Error === 22) {
            this.MessageCenter.send('on_room_not_exist');
            this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeInfo);
        } else if (i32Error === 53 || i32Error === 42 || i32Error === 32 || i32Error === 98) {
            this.GameDataManager.tRoomData.isShowNeedShop = true;
            this.MessageCenter.send('on_need_more_gold');
            this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
        } else if (i32Error === 49 || i32Error === 50 || i32Error === 71) {
            this.GameDataManager.tGameData.m_bIsOnSelfAction = false;
            this.MessageCenter.send('on_action_error');
            this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
        } else if (i32Error === 52 || i32Error === 1001) {
        } else if (i32Error === 112) {
            this.GameDataManager.tRoomData.isShowNeedClub = true;
            this.MessageCenter.send('needClub');
        } else if (i32Error === 93) {
            this.GameDataManager.tGameData.m_bIsOnSelfAction = true;
            this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
        } else if (i32Error === 51 || i32Error === 48 || i32Error === 92) {
            this.GameDataManager.tGameData.m_bIsOnSelfAction = false;
            this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
        } else if (i32Error === 105) {
            this.TP.showMsg(acBuffer, this.Enum.ButtonStyle.GOLD_BUTTON, null);
            let image = this.TP.getMessageImage();
            let msgTxt = this.TP.getMessageImageText();
            let msgtext = this.TP.getMessageText();
            image.node.active = true;
            msgTxt.string = this.config.getStringData('TipsPanel_Image_duanxin_text_duanxin_2');
            msgTxt.node.active = true;
            this.resMgr.setSpriteFrame(image.node, 'zh_CN/hall/ui/common_prompt');
            msgtext.node.setPosition(msgtext.node.x, 0);
        } else if (i32Error === 229 || i32Error === 2 || i32Error === 31123) {
            this.netWorkManager.OnNeedRelogin(i32Error);
        } else if (i32Error === 113) {//ServerErrorCode113 Player all in,  all mute 
            cc.log("Error code " + i32Error + " " + acBuffer);
            this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
        } else if (i32Error === 2000) {
            this.roomManager.setIsSpectatorRevealEnabled(false);
            acBuffer = this.config.getStringData('ForceShowCardToastError');
            this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
        }
        else if (i32Error === 31123) {
            acBuffer = this.config.getStringData('ServerErrorCode31123');
            this.TT.showMsg(acBuffer, this.Enum.ToastType.ToastTypeError);
        }
        else if (this.isGeoComplyError(i32Error)) {
            this.handleGeoComplyError(headerServerid, headerRoomid, data);
        } else {
            if (this.C2CNotify.isOrderActive() && this.GameDataManager.tRoomData.isZoom()) {
                this.C2CNotify.onStandupSuccess();
            } else {
                this.TT.showMsg(this.StringTools.formatC(acBuffer), this.Enum.ToastType.ToastTypeError);
            }
        } */
    }
}
