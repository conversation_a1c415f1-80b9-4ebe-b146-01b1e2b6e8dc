const { ccclass, property } = cc._decorator;

export interface ICalendarItemParam {
    index: number;
    dateString?: string;
    isExpired: boolean;
    isClaimedAll: boolean;
    canClaim: boolean;
    amountReward?: number;
    amountBet?: number;
    showOutline?: boolean;
    currencyType?: number;
}

export enum MiniGameCurrencyTypes {
    Default = 0,
    GoldCoin = 2,
    CasinoCoin = 3
}

@ccclass('MiniGameCurrencyIconSetting')
export class MiniGameCurrencyIconSetting {
    @property({ type: cc.Enum(MiniGameCurrencyTypes) }) currencyType: MiniGameCurrencyTypes =
        MiniGameCurrencyTypes.GoldCoin;
    @property(cc.SpriteFrame) coinFrame: cc.SpriteFrame = null;
}

@ccclass('TextStyleSetting')
export class TextStyleSetting {
    @property({ type: cc.Float }) fontSize: number = 30;
    @property(cc.Color) fontColor: cc.Color = cc.Color.WHITE;
    @property(MiniGameCurrencyIconSetting) prizeType: MiniGameCurrencyIconSetting[] = [];

    getCoinFrame(currencyType: number): cc.SpriteFrame {
        return this.prizeType.find((o) => o.currencyType === currencyType)?.coinFrame;
    }
}

@ccclass('MiniGamePrizeSetting')
export class MiniGamePrizeSetting {
    @property({ type: cc.Enum(MiniGameCurrencyTypes) }) currencyType: MiniGameCurrencyTypes =
        MiniGameCurrencyTypes.GoldCoin;
    @property(cc.SpriteFrame) coinSprites: cc.SpriteFrame[] = [];
}
