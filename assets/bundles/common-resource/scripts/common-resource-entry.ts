import { registerEntry } from '../../../poker-framework/scripts/pf';
import * as pf from '../../../poker-framework/scripts/pf';
import * as components from './components/common-resource-components-index';
import { commonResourceAgent } from './common-resource-agent';

import { macros } from './common/common-resource-macros';

import BUNDLE_NAME = macros.BUNDLE_NAME;
import { DialogHubControl } from './components/DialogHubControl';

@registerEntry(BUNDLE_NAME)
export class CommonResourceEntry extends pf.BundleEntryBase {
    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_RESOURCE;
    }

    protected getLanguageStringPath() {
        let path = macros.Language_String_Path.ZH_CN;
        switch (pf.languageManager.currentLanguage) {
            case pf.LANGUAGE_GROUPS.en_US:
                path = macros.Language_String_Path.EN_US;
                break;
            case pf.LANGUAGE_GROUPS.yn_TH:
                path = macros.Language_String_Path.YN_TH;
                break;
            case pf.LANGUAGE_GROUPS.th_PH:
                path = macros.Language_String_Path.TH_PH;
                break;
            case pf.LANGUAGE_GROUPS.hi_IN:
                path = macros.Language_String_Path.HI_IN;
                break;
        }
        return path;
    }

    protected getAddressableConfigPath() {
        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
            ? macros.Addressable_Config_Path.ZH_CN
            : macros.Addressable_Config_Path.EN_US;
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onEnter`);

        await this.loadConfigs();

        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('common-resource');
        await assetLoader.start(options?.onProgress);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        let commonDialogPrefabName = 'common-resource.common-dialog';
        if (context.platform === 'wpk') commonDialogPrefabName = 'common-resource.wpk-common-dialog';
        const dlgPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(commonDialogPrefabName);
        const dlg = cc.instantiate(dlgPrefab);
        cc.game.addPersistRootNode(dlg);
        commonResourceAgent.commonDialog = dlg.getComponent(components.CommonDialogControl);

        const shopPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.shop');
        const shop = cc.instantiate(shopPrefab);
        cc.game.addPersistRootNode(shop);
        commonResourceAgent.shop = shop.getComponent(components.ShopControl);

        const toastPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.toast-message');
        const toastMessage = cc.instantiate(toastPrefab);
        cc.game.addPersistRootNode(toastMessage);
        commonResourceAgent.toastMessage = toastMessage.getComponent(components.ToastMessageControl);

        commonResourceAgent.initializeErrorMessageControl();

        // const calmDownDlgPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.calm-down-dialog');
        // const calmDownDlg = cc.instantiate(calmDownDlgPrefab);
        // cc.game.addPersistRootNode(calmDownDlg);
        // commonResourceAgent.calmDownDialog = calmDownDlg.getComponent(components.CalmDownDialogControl);

        const dialogHubPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.dialog-hub');
        const dialogHub = cc.instantiate(dialogHubPrefab);
        cc.game.addPersistRootNode(dialogHub);
        commonResourceAgent.dialogHub = dialogHub.getComponent(DialogHubControl);

        return Promise.resolve();
    }

    protected async onPreload(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload`);
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('common-resource-dynamic');
        assetLoader.addLoadAddressableGroupTask('common-resource-audio');
        await assetLoader.startPreload();
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload done`);
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onExit`);

        commonResourceAgent.commonDialog.node.destroy();
        commonResourceAgent.commonDialog = null;

        commonResourceAgent.shop.node.destroy();
        commonResourceAgent.shop = null;

        commonResourceAgent.toastMessage.node.destroy();
        commonResourceAgent.toastMessage = null;

        // commonResourceAgent.calmDownDialog.node.destroy();
        // commonResourceAgent.calmDownDialog = null;

        // pf.bundleManager.releaseAll(this.bundle);
    }

    onUnload(): void {
        cc.log(`bundle ${BUNDLE_NAME} onUnload`);
    }
}
