import * as pf from '../../../../poker-framework/scripts/pf';

export class CurrencyUtil {
    /**
     * This method was originally named `numToFloatString`.
     * *（服务器金币转客户端后保留2位有效位小数后转显示比例）
     * @param number       服务器比例金币
     */
    static serverAmountToDisplayString(serverAmount: number): string {
        return CurrencyUtil.clientAmountToDisplayString(CurrencyUtil.convertToClientAmount(serverAmount));
    }

    /**
     * This method was originally named `clientGoldByServer`.
     * *服务器转客户端
     * @param number       服务器比例金币
     */
    static convertToClientAmount(serverAmount: number): number {
        return pf.MathUtil.times(serverAmount, 0.01);
    }

    /**
     * This method was originally named `serverGoldByClient`.
     * *客户端转服务器
     * @param number       客户端比例金币
     */
    static convertToServerAmount(clientAmount: number): number {
        return pf.MathUtil.times(clientAmount, 100);
    }

    /**
     * This method was originally named `numberToString`.
     * （客户端金币保留2位有效位小数后转显示比例） 例如 0.123 转为0.12 在10倍比例下显示1.2， 0.1倍显示0.012
     * @param number浮点数  客户端比例金币
     */
    static clientAmountToDisplayString(clientAmount: number): string {
        let float = parseFloat(clientAmount.toString());
        let result = 0;
        if (clientAmount === 0) {
            return '0';
        } else if (clientAmount > 0) {
            if (clientAmount * 10 - parseInt((float * 10).toString(), 10) > 0) {
                result = pf.MathUtil.floorToDecimalPlaces(clientAmount, 2);
            } else if (clientAmount - parseInt(clientAmount.toString(), 10) === 0) {
                result = clientAmount;
            } else {
                result = pf.MathUtil.floorToDecimalPlaces(clientAmount, 1);
            }
            return CurrencyUtil.applyDisplayRatioToFormattedString(result);
        } else {
            // eslint-disable-next-line no-param-reassign
            clientAmount = -clientAmount;
            const float = parseFloat(clientAmount.toString());
            if (clientAmount - parseInt((float * 10).toString(), 10) > 0) {
                result = pf.MathUtil.floorToDecimalPlaces(clientAmount, 2);
            } else if (clientAmount - parseInt(clientAmount.toString(), 10) === 0) {
                result = clientAmount;
            } else {
                result = pf.MathUtil.floorToDecimalPlaces(clientAmount, 1);
            }
            return CurrencyUtil.applyDisplayRatioToFormattedString(-result);
        }
    }

    /**
     * This method was originally named `_getShowGoldRatio`.
     */
    static getDisplayRatio(): number {
        // TODO
        // refactor this function
        // cv.config.getShowGoldRatio()
        const number = 100;
        return pf.MathUtil.div(100, number);
    }

    /**
     * This method was originally named `numberToShowNumber`
     * 客户端金币转显示比例number
     */
    static applyDisplayRatioToNumber(clientAmount: number): number {
        return pf.MathUtil.times(CurrencyUtil.getDisplayRatio(), clientAmount);
    }

    /**
     * This method was originally named `numberToShowString`.
     * 客户端金币转显示比例字符串
     */
    static applyDisplayRatioToFormattedString(amount: number): string {
        return CurrencyUtil.applyDisplayRatioToNumber(amount).toString();
    }

    /**
     * This method was originally named `showStringToNumber`.
     * 显示比例转客户端金币number
     */
    static convertDisplayStringToClientAmount(amount: string): number {
        return pf.MathUtil.div(pf.TypeUtil.toSafeNumber(parseFloat(amount)), CurrencyUtil.getDisplayRatio());
    }

    /**
     * This method was originally named `serverGoldToShowNumber`.
     * 服务器金币转显示比例number
     */
    static convertServerAmountToDisplayNumber(amount: number): number {
        return CurrencyUtil.applyDisplayRatioToNumber(CurrencyUtil.convertToClientAmount(amount));
    }

    /**
     * This method was originally named `serverGoldToShowString`.
     * 服务器金币转显示比例字符串
     */
    static convertServerAmountToDisplayString(amount: number): string {
        return CurrencyUtil.convertServerAmountToDisplayNumber(amount).toString();
    }

    /**
     * This method was originally named `getSignString`.
     * 获取带符号的数字字符串( >0 返回 "+0" ; < 0 返回:"-0" )
     */
    static getSignedString(amount: number): string {
        // eslint-disable-next-line no-param-reassign
        amount = pf.TypeUtil.toSafeNumber(amount);
        let sRet: string = pf.TypeUtil.toSafeString(amount);
        if (amount > 0) sRet = '+' + sRet;
        return sRet;
    }

    /**
     * This method was originally named `formatGoldNumber`.
     * 格式化wpk金币
     */
    static formatAmountForDisplay(amount: number): string {
        return CurrencyUtil.clientAmountToDisplayString(amount);
    }

    /**
     * This method was originally named `transNumberToString`.
     * 转换指定数值为字符串
     * @param amount           数值
     * @param precision     精确度
     * @param sign          符号
     */
    static convertNumberToPrecisionString(amount: number, precision = 2, sign = false): string {
        let fValue: number = CurrencyUtil.convertServerAmountToDisplayNumber(amount);

        // 自动运算, 去除格式化 "%.f" 自动四舍五入
        fValue = Math.floor(fValue * Math.pow(10, precision)) / Math.pow(10, precision);

        let strSign = '';
        if (sign) strSign = fValue >= 0 ? '+' : '';

        let format: string = '%s%' + pf.StringUtil.formatC('.%df', precision);
        let strValue: string = pf.StringUtil.formatC(format, strSign, fValue);

        let count = 0;
        let index: number = strValue.indexOf('.');

        for (let i = strValue.length - 1; index > 0 && i >= index; --i) {
            if (strValue[i] === '0' || strValue[i] === '.') ++count;
            else break;
        }

        return strValue.substring(0, strValue.length - count);
    }
}
