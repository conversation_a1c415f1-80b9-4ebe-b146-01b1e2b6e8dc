/* eslint-disable no-param-reassign */
/* eslint-disable prefer-regex-literals */
import * as pf from '../../../../poker-framework/scripts/pf';

export class UIUtil {
    /**
     * This method was originally named `getLabelStringSize`.
     * 立即获取改变 string 后的 label 控件的大小(此方法略微消耗性能)
     * @param lab 要获取大小的 label控件
     * @param txt 改变后的string(可选，若缺省, 则返回原大小)
     */
    static updateAndMeasureLabel(lab: cc.Label, txt?: string): cc.Size {
        let size: cc.Size = cc.Size.ZERO;
        if (lab) {
            if (txt) lab.string = txt;
            let _lab: any = lab;
            _lab._forceUpdateRenderData();
            size = lab.node.getContentSize();
        }
        return size;
    }

    /**
     * This method was originally named `getRichTextStringSize`.
     * 立即获取改变 string 后的 RichText 控件的大小(此方法略微消耗性能)
     * @param richText 要获取大小的 RichText控件
     * @param txt 改变后的string(可选，若缺省, 则返回原大小)
     */
    static updateAndMeasureRichText(richText: cc.RichText, txt?: string): cc.Size {
        let size: cc.Size = cc.Size.ZERO;
        if (richText) {
            if (txt) richText.string = txt;
            let _richText: any = richText;
            _richText.node._activeInHierarchy = true;
            _richText._updateRichTextStatus();
            size = _richText.node.getContentSize();
        }
        return size;
    }

    /**
     * 适配 widget(当前帧立即生效)
     * @param node          要适配的节点
     * @param bTransChild   是否遍历适配子节点(默认 false)
     * @param isKeepEnable  是否保持widget的enable状态，默认不保持
     */
    static adaptWidget(node: cc.Node, bTransChild?: boolean, isKeepEnable = false): void {
        if (!node) return;

        if (!(node instanceof cc.Scene)) {
            // 加个过滤，Scene调用getComponent有警告
            let widget: cc.Widget = node.getComponent(cc.Widget);
            if (widget && cc.isValid(widget, true)) {
                if (isKeepEnable) {
                    if (widget.enabled) widget.updateAlignment();
                } else {
                    widget.enabled = true;
                    widget.updateAlignment();
                    widget.enabled = false;
                }
            }
        }

        if (bTransChild) {
            // eslint-disable-next-line @typescript-eslint/prefer-for-of
            for (let i = 0; i < node.children.length; ++i) {
                this.adaptWidget(node.children[i], bTransChild, isKeepEnable);
            }
        }
    }

    /**
     * 判断当前帧指引擎对象实例是否有效
     * @param node
     * @returns true:表示有效
     */
    static isValidNode(obj: any): boolean {
        return obj && cc.isValid(obj, true);
    }

    /**
     * 安全销毁指定节点
     * @param node
     */
    static destroyNode(node: cc.Node) {
        if (this.isValidNode(node)) node.destroy();
    }

    /**
     * This method was originally named `cleanNodeArray`.
     * 清空节点数组(且从父节点移除)
     */
    static destroyNodes(nodeArray: cc.Node[]): void {
        if (!nodeArray || nodeArray.length === 0) return;

        for (const node of nodeArray) {
            node.removeFromParent();
            node.destroy();
        }

        nodeArray.length = 0; // 清空数组
    }

    /**
     * 设置富文本的内容
     * 注意：富文本对2个以上的">"显示敏感，貌似“<"无法显示
     * @param node 富文本所在节点
     * @param str json文件自带颜色的字符串
     */
    static setRichTextString(node: cc.Node, str: string) {
        if (!node) {
            console.log('function setRichTextString parameter node is null');
            return;
        }

        let posBegin = 0;
        let posEnd = 0;
        let colorPos = 0;
        let msg = '';

        do {
            colorPos = str.indexOf('#', colorPos);
            if (colorPos !== -1) {
                posBegin = str.lastIndexOf('|', colorPos);
                if (posBegin === -1) {
                    str = '|' + str;
                    posBegin = 0;
                    colorPos = colorPos + 1;
                }
                if (posBegin > 0) {
                    if (msg !== '' && str.charAt(0) === '|') {
                        str = str.replace(str.slice(0, 1), '');
                    }
                    let tempSlice = str.slice(0, posBegin);
                    msg += tempSlice;
                    str = str.replace(tempSlice, '');

                    colorPos = str.indexOf('#', 0);
                    posBegin = 0;
                }

                posEnd = str.indexOf('|', colorPos);
                if (posEnd === -1) {
                    str += '|';
                    posEnd = str.length;
                }

                let tempStr = str.slice(posBegin + 1, colorPos);
                let colorStr = str.slice(colorPos, posEnd);

                let tempArr = tempStr.split('>');

                if (tempArr.length < 3) {
                    msg += pf.StringUtil.formatC('<color=%s>%s</color>', colorStr, tempStr);
                } else {
                    for (let j = 0; j < tempArr.length; j++) {
                        msg += pf.StringUtil.formatC('<color=%s>%s</color>', colorStr, tempArr[j]);
                        if (j < tempArr.length - 1) {
                            msg += pf.StringUtil.formatC('<color=%s>%s</color>', colorStr, '>');
                        }
                    }
                }
                str = str.replace(str.slice(posBegin, posEnd + 1), '');
                colorPos = 0;
            } else {
                msg += str;
            }
        } while (colorPos !== -1);
        node.getComponent(cc.RichText).string = msg;
    }

    /**
     * 获取富文本字符内容
     * @param node
     * @param noColor 去掉颜色值
     */
    static getRichTextString(node: cc.Node, noColor: boolean = true): string {
        if (!cc.isValid(node, true)) {
            return null;
        }
        if (!noColor) {
            return node.getComponent(cc.RichText).string;
        }
        let strColorArr: any[] = UIUtil.getRichTextStringAndColor(node);
        if (!strColorArr) {
            return null;
        }
        let str = '';
        for (let stringColor of strColorArr) {
            str += stringColor.string;
        }
        return str;
    }

    /**
     * 获取富文本{内容,颜色}数组
     * 元素={string:内容, color:颜色}
     * @param node
     * @returns
     */
    static getRichTextStringAndColor(node: cc.Node): any[] {
        if (!cc.isValid(node, true)) {
            return null;
        }
        let str: string = node.getComponent(cc.RichText).string;
        let ret: any[] = [];
        if (str.length === 0) {
            return ret;
        }
        // 文本格式为:<color=#000000>XXXX</color>
        let colorRegexpBegin = new RegExp('<color=#[A-Za-z0-9]{1,6}>');
        let colorRegexpEnd = new RegExp('</color>');
        let colorBegin = -1; // 颜色起始位置
        let colorEnd = -1; // 颜色结束位置
        let tempStr: string = str;
        let tempPos = 0;
        let tempChar = '>';
        while (tempStr.length > 0) {
            colorBegin = tempStr.search(colorRegexpBegin);
            if (colorBegin === -1) {
                ret.push({ string: tempStr, color: null });
                break;
            }
            colorEnd = tempStr.search(colorRegexpEnd);
            if (colorEnd === -1) {
                ret.push({ string: tempStr, color: null });
                break;
            }
            // 默认颜色文本
            if (tempPos < colorBegin) {
                ret.push({ string: tempStr.substring(tempPos, colorBegin), color: null });
            }
            // tempPos > colorBegin : 不存在该情况
            // 指定颜色文本
            tempPos = tempStr.indexOf(tempChar);
            ret.push({
                string: tempStr.substring(tempPos + 1, colorEnd),
                color: tempStr.substring(colorBegin + 7, tempPos)
            });
            // 继续下一次查找
            tempStr = tempStr.substring(colorEnd + 8);
            tempPos = 0;
        }
        return ret;
    }

    /**
     * 自动按照文本长度改变字体大小, 或者以省略号代替(暂时只支持单行)
     * @brief 该方法目前很少使用, 主要是弥补少数情况下若干"文本控件"等未设置 Overflow; 一般情况下 creator 引擎自带的 Overflow 能够满足大多数需求;
     * 貌似引擎高版本连 RichText 也支持 Overflow 了; 往后, 实际需求和表现实践中该方法存在的意义有些微不足道; 建议使用前仔细考虑, 尽量使用 Overflow
     * @param txtNode   文本控件节点(该节点下的 lab 组件必须开启宽度自定义模式, 即 Overflow 模式为非 NONE; 同理, 若是 RichText 则也需自定义宽度)
     * @param str       文本内容
     * @param ellipsis  是否已省略号结尾(true-省略号结尾, false-自动改变字体大小, 默认省略号; RichText 默认只支持缩小模式)
     */
    static setShrinkString(txtNode: cc.Node, str: string, ellipsis: boolean = true): number {
        if (!cc.isValid(txtNode, true)) return;
        if (txtNode.getContentSize().width === 0) {
            console.error('节点宽度不能为0********');
            return;
        }
        let lab: cc.Label = txtNode.getComponent(cc.Label);
        let richTxt: cc.RichText = txtNode.getComponent(cc.RichText);
        if (!cc.isValid(lab, true) && !cc.isValid(richTxt, true)) return;

        let strOut = '';
        let fontSize = 0;
        let actWidth = 0;

        // 文本
        if (lab) {
            lab.string = str;

            strOut = str;
            fontSize = lab.fontSize;

            let tmpLab: cc.Label = new cc.Node().addComponent(cc.Label);
            tmpLab.font = lab.font;
            tmpLab.fontSize = fontSize;
            tmpLab.fontFamily = lab.fontFamily;
            tmpLab.overflow = cc.Label.Overflow.NONE;
            tmpLab.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
            tmpLab.verticalAlign = cc.Label.VerticalAlign.CENTER;
            tmpLab.string = lab.string;

            let widthLimit: number = txtNode.width;

            let tempWidth: number = UIUtil.updateAndMeasureLabel(tmpLab).width;
            if (tempWidth > widthLimit) {
                // 省略号模式
                if (ellipsis) {
                    let strEllipsis = '...';
                    do {
                        strOut = strOut.substring(0, strOut.length - 1);
                        tmpLab.string = strOut + strEllipsis;
                    } while (UIUtil.updateAndMeasureLabel(tmpLab).width > widthLimit);
                    strOut += strEllipsis;
                }
                // 字体缩小模式
                else {
                    for (let i = fontSize - 1; i >= 1; --i) {
                        tmpLab.fontSize = i;
                        if (UIUtil.updateAndMeasureLabel(tmpLab).width <= widthLimit) {
                            fontSize = i;
                            break;
                        }
                    }
                }
                lab.string = strOut;
                lab.fontSize = fontSize;
                actWidth = UIUtil.updateAndMeasureLabel(tmpLab).width;
            } else {
                actWidth = tempWidth;
            }
            tmpLab.destroy();
            return actWidth;
        }
        // 富文本
        else if (richTxt) {
            richTxt.string = str;

            strOut = str;
            fontSize = richTxt.fontSize;

            let tmpRichText: cc.RichText = new cc.Node().addComponent(cc.RichText);
            tmpRichText.font = richTxt.font;
            tmpRichText.fontSize = richTxt.fontSize;
            tmpRichText.fontFamily = richTxt.fontFamily;
            tmpRichText.horizontalAlign = cc.macro.TextAlignment.LEFT;
            tmpRichText.string = richTxt.string;

            let widthLimit: number = txtNode.width;
            let tempWidth: number = tmpRichText.node.width;
            if (tempWidth > widthLimit) {
                // 字体缩小模式
                for (let i = fontSize - 1; i >= 1; --i) {
                    tmpRichText.fontSize = i;
                    if (tmpRichText.node.width <= widthLimit) {
                        fontSize = i;
                        break;
                    }
                }
                richTxt.fontSize = fontSize;
            }
            tmpRichText.destroy();
        }
    }

    /**
     * 自动计算换行, 主动插入换行符, 保证单词完整性(频繁使用或者内容过多, 会损耗性能, 慎用)
     * @brief 注: Js脚本层无法通过内存字节布局知道字符串语言组合方式, 此方法通过"正则分割"来切割字符串来保证"完整性", 可适当根据分割需求修改该函数内部的正则表达式
     * @param labNode       label 组件节点
     * @param strIn         输入字符串
     * @param wrapReplace   换行替代串(默认"\n")
     * @param skipSplit     是否跳过分割正则(默认:false), 这个参数只要是针对"纯中文"的, 因为中文要切割光依靠标点符号太局限, 如果一句话没有标点符号就很难正常切割了
     */
    static calculateAutoWrapString(
        labNode: cc.Node,
        strIn: string,
        wrapReplace: string = '\n',
        skipSplit: boolean = false
    ): string {
        let sRet: string = pf.TypeUtil.toSafeString(strIn);
        if (labNode instanceof cc.Node && cc.isValid(labNode)) {
            if (labNode.getComponent(cc.Label)) {
                let lab: cc.Label = cc.instantiate(labNode).getComponent(cc.Label);
                lab.string = '';
                lab.overflow = cc.Label.Overflow.NONE;

                let lastWidth = 0; // 记录上次字串的宽度
                let lastText = ''; // 记录上次的字串
                let maxWidth: number = lab.node.width; // 该 label 的最大宽度

                let subStr = ''; // 截取的子串
                let indexOfPos = 0; // indexOf 的起始索引
                let regex = /[*\s+|-]/gi; // 分割正则(regex = /[.*\s+|,\.\?，。、？]/ig)

                // 过滤 \r\n 换行符 统一转换成 \n(否则Label换行时ios等设备会错乱)
                strIn = strIn.replace(/(\r\n)/gi, '\n');

                // 搜索分隔符
                let searchString: Function = (searchString: string, position: number): number => {
                    let nRet: number = position;
                    let subStr: string = searchString.substring(position);
                    let index: number = subStr.search(regex);
                    if (index < 0) index = subStr.length;
                    return nRet + index;
                };

                // 开始切割
                for (let i = 0; i < strIn.length; ++i) {
                    let pos: number = skipSplit ? indexOfPos + 1 : searchString(strIn, indexOfPos);
                    let splitStr: string = strIn.charAt(pos);

                    if (strIn.charAt(i) === splitStr) {
                        subStr = strIn.charAt(i);
                        ++indexOfPos;

                        if (subStr === '\n') {
                            lastText += subStr;
                            lastWidth = 0;
                            continue;
                        }
                    } else {
                        if (pos < 0) {
                            pos = strIn.length;
                        }
                        // let sunLen: number = pos - indexOfPos;
                        subStr = strIn.substring(indexOfPos, pos);

                        indexOfPos = pos;
                        i = indexOfPos - 1;
                    }

                    // 计算该完整单词总宽度
                    let substringWidth: number = UIUtil.updateAndMeasureLabel(lab, subStr).width;

                    // 非正常单词, 若该完整单词总宽度超过控件总宽度, 则跳过本次切割, 采用引擎默认切割方式
                    // 一个完整词组都这么长, 还计算个毛, 肯定都出问题了, 也会影响后续排版计算, 反思以下几点:
                    // 1.label 控件设计大小是否合理?
                    // 2.label.string 内容是否正确?
                    // 3.正则分割表达式是否有遗漏?
                    // 4.该方法是否适合解决此问题?
                    // 5.找bug -_-!
                    if (substringWidth > maxWidth) {
                        lastText += subStr;
                        lastWidth = 0;
                        continue;
                    }
                    // 正常单词, 正常切割
                    else {
                        if (lastWidth + substringWidth <= maxWidth) {
                            lastText += subStr;
                            lastWidth += substringWidth;
                        } else {
                            lastText += wrapReplace;
                            lastText += subStr;
                            lastWidth = substringWidth;
                            lastWidth += UIUtil.updateAndMeasureLabel(lab, wrapReplace).width;
                        }
                    }
                }

                lab.node.destroy();
                sRet = lastText;
            }
        }

        return sRet;
    }
    /**
     * This method was originally named `getStringIndexByLength`.
     * 根据宽度获取字符串截取下标
     * @param labNode   label 组件节点
     * @param strIn     输入字符串
     * @param labWidth  节点限制宽度  默认2000像素  （Label和RichText在引擎里都有长度限制  大约超过2016像素后就会被剪裁  这里取个相近的范围）
     * 返回值
     * 字符下标
     */
    static getMaxVisibleLengthForLabel(labNode: cc.Node, strIn: string, labWidth: number = 2000): number {
        if (labNode instanceof cc.Node && cc.isValid(labNode)) {
            if (labNode.getComponent(cc.Label)) {
                let lab: cc.Label = cc.instantiate(labNode).getComponent(cc.Label);
                lab.overflow = cc.Label.Overflow.NONE;
                if (UIUtil.updateAndMeasureLabel(lab, strIn).width <= labWidth) {
                    return strIn.length;
                } else {
                    let subStr = '';
                    for (let i = 1; i < strIn.length; ++i) {
                        subStr = strIn.substring(0, i);
                        if (UIUtil.updateAndMeasureLabel(lab, subStr).width > labWidth) {
                            return i - 1;
                        }
                    }
                }
                lab.node.destroy();
            }
        } else if (labNode.getComponent(cc.RichText)) {
            let lab: cc.RichText = cc.instantiate(labNode).getComponent(cc.RichText);
            if (UIUtil.updateAndMeasureRichText(lab, strIn).width <= labWidth) {
                return strIn.length;
            } else {
                let subStr = '';
                for (let i = 1; i < strIn.length; ++i) {
                    subStr = strIn.substring(0, i);
                    if (UIUtil.updateAndMeasureRichText(lab, subStr).width > labWidth) {
                        return i - 1;
                    }
                }
            }
            lab.node.destroy();
        }
        return strIn.length;
    }

    /**
     * This method was originally named `getLengthForCN`.
     * 返回长度个数，作为calculateAutoWrapString无法对中文切割的补充
     * @param labNode
     * @param str
     */
    static getRequiredLinesForChinese(labNode: cc.Node, str: string): number {
        let len = 1;
        if (labNode instanceof cc.Node && cc.isValid(labNode)) {
            if (labNode.getComponent(cc.Label)) {
                let lab: cc.Label = cc.instantiate(labNode).getComponent(cc.Label);
                lab.string = '';
                lab.overflow = cc.Label.Overflow.NONE;
                let width = UIUtil.updateAndMeasureLabel(lab, str).width;
                len = Math.ceil(width / labNode.width);
            }
        }
        return len;
    }

    /**
     * This method was originally named `doFadeIn`.
     * @param node
     * @param time
     * @param callback
     */
    static fadeIn(node: cc.Node, time: number, callback?: Function) {
        node.opacity = 1;
        node.active = true;
        node.runAction(
            cc.sequence(
                cc.fadeIn(time),
                cc.callFunc(() => {
                    if (callback) {
                        callback();
                    }
                })
            )
        );
    }
}
