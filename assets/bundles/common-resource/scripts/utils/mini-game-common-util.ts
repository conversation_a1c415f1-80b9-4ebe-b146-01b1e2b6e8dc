import * as pf from '../../../../poker-framework/scripts/pf';
import { CurrencyUtil } from './mini-game-currency-util';

export class CommonUtil {
    static getShortOwnCoinString(coin: number): string {
        let formatCoin = CurrencyUtil.convertToClientAmount(coin);
        if (CurrencyUtil.applyDisplayRatioToNumber(formatCoin) < 10000) {
            return CurrencyUtil.clientAmountToDisplayString(formatCoin);
        } else {
            return (
                CurrencyUtil.clientAmountToDisplayString(CurrencyUtil.convertToClientAmount(coin / 10000)) +
                pf.languageManager.getString('Cowboy_coin_short_text')
            );
        }
    }

    /**
     * 转化获取指定金额(超过 10 的 exp 次幂, 显示 xxx 万, 百万, 十亿)
     * @param gold          数值
     * @param exp           底数为10的指数
     */
    static transGoldShortString(gold: number, exp = 4): string {
        let retValue = '';
        let bNegative = false;

        if (gold < 0) {
            bNegative = true;
            // eslint-disable-next-line no-param-reassign
            gold = -gold;
        }

        let formatCoin: number = CurrencyUtil.convertServerAmountToDisplayNumber(gold);
        let fMillion = Math.pow(10, 6);
        let fBillion = Math.pow(10, 9);
        let fTenThousand = Math.pow(10, 4);

        if (formatCoin >= Math.pow(10, exp)) {
            // 100亿
            if (formatCoin >= 10 * fBillion) {
                retValue =
                    CurrencyUtil.convertNumberToPrecisionString(gold / fBillion) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_billion');
            }
            // 亿
            else if (formatCoin >= 100 * fMillion) {
                retValue =
                    CurrencyUtil.convertNumberToPrecisionString(gold / fMillion) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_million');
            }
            // 万
            else if (formatCoin >= fTenThousand) {
                retValue =
                    CurrencyUtil.convertNumberToPrecisionString(gold / fTenThousand) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_w');
            } else {
                retValue = CurrencyUtil.convertNumberToPrecisionString(gold);
            }
        } else {
            retValue = CurrencyUtil.convertNumberToPrecisionString(gold);
        }

        if (bNegative) {
            retValue = '-' + retValue;
        }

        return retValue;
    }

    static ThousandPointFormat(value: number, isGoldByServer = true): string {
        const formatCoin: number = isGoldByServer ? CurrencyUtil.convertServerAmountToDisplayNumber(value) : value;
        const dotSplit = formatCoin.toString().split('.');
        const quotientStr = dotSplit[0];

        const quotientStrArray = quotientStr.split('');
        let thousandPointStr = '';
        quotientStrArray.reverse().forEach((digit: string, index: number) => {
            if (index % 3 === 0 && index > 0) {
                thousandPointStr = ',' + thousandPointStr;
            }
            thousandPointStr = digit + thousandPointStr;
        });

        if (dotSplit.length > 1) {
            thousandPointStr += '.' + dotSplit[1];
        }

        return thousandPointStr;
    }

    static isFitSafeAreaNeeded(): boolean {
        const deviceModel = pf.system.getDeviceModel();
        if (deviceModel === 'Not a IOS model') {
            return false;
        }

        const match = deviceModel.match(/^iPhone(\d+),(\d+)$/);

        if (match) {
            const majorVersion = parseInt(match[1], 10);
            const minorVersion = parseInt(match[2], 10);
            cc.log('[3in1] iOS device:' + deviceModel);
            if (majorVersion > 13) {
                return true;
            }
        }

        return false;
    }

    static getHeadPath(player: pf.services.Player | pf.services.RankData, currentUserId?: number): string {
        const isSelf = player.uid === currentUserId;
        return isSelf || !player.isAdUser || !player.wpkSysAvatar ? player.head : player.wpkSysAvatar;
    }

    /**
     * This method was originally named `Util.getServerStrByLanguage`.
     * Selects the appropriate text segment from a server-provided string based on the current language setting.
     * If the input string contains a specific color tag ('<color=#'), it returns the original string as-is.
     * Otherwise, it splits the string using '#' as a delimiter and selects the segment corresponding to
     * the current language. If the segment is not available, it falls back to default behavior.
     * @param stringData
     * @returns
     */
    static selectServerString(stringData: string): string {
        if (stringData.search('<color=#') !== -1) return stringData;

        const defaultIndex = 1;
        const indexMapping: Record<string, number> = {
            [pf.LANGUAGE_GROUPS.zh_CN]: 0,
            [pf.LANGUAGE_GROUPS.yn_TH]: 2,
            [pf.LANGUAGE_GROUPS.th_PH]: 3
        };

        // 兼容新跑马灯#号分割的问题
        let strArr = stringData.split('#');
        let index = indexMapping[pf.languageManager.currentLanguage] || defaultIndex;
        if (strArr.length < index + 1) {
            index = strArr.length >= 2 ? 1 : 0;
        }
        return strArr[index];
    }
}
