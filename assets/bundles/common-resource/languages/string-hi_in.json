{"groups": {"hi_IN": {"strings": {"InquireView_lab_3": "अभिन्न", "Cancel": "र<PERSON><PERSON><PERSON> करना", "Confirm": "पुष्टि करना", "MiniGame_AddAutoBet_Text": "20 hands", "MiniGame_Switch_Table": "किसी अन्य तालिका पर स्विच करें", "MiniGame_Exit": "छोड़ना", "MiniGame_Switch_Content": "टेबल बंद है", "MiniGame_Select_AddAutoBet_Text": "%d hands", "MiniGame_btn_desc_auto_bet_reached": "Continue betting has reached the upper limit, and %d hands will be added for you", "M_UITitle113": "हाई कार्ड", "M_UITitle114": "एक जोड़ी", "M_UITitle115": "दो जोड़ी", "M_UITitle116": "ट्रिप्स", "M_UITitle117": "स्ट्रेट", "M_UITitle118": "फ्लश", "M_UITitle119": "फुल हाउस", "M_UITitle120": "एक तरह के चार", "M_UITitle121": "स्ट्रेट फ्लश", "M_UITitle122": "रॉयल फ्लश", "UIOpenNewWindow": "पेज को एक नई विंडो में खोलने के बारे में", "TipsPanel_sure_button": "पुष्टि करें", "TipsPanel_cancel_button": "रद्<PERSON> करें", "LuckTurntables_des_text": "जीतने के लिए पहिये पर क्लिक करें!", "LuckTurntables_tips_text_0": "सोने के सिक्के वाला लाल\nलिफाफा प्राप्त करने के लिए बधाई [%s] कृपया ग्राहक सेवा से संपर्क करें\n48 घंटों में।", "LuckTurntables_tips_text_1": "कसीनो के सिक्के वाला लाल\nलिफाफा प्राप्त करने के लिए बधाई [%s] कृपया ग्राहक सेवा से संपर्क करें\n48 घंटों में।", "LuckTurntables_tips_text_2": "usd के सिक्के वाला लाल\nलिफाफा जीतने के लिए बधाई [%s] उसे पाने के लिए कृपया ग्राहक सेवा से संपर्क करें\n48 घंटों में।", "LuckTurntables_tips_text_3": "मेच टिकट जीतने\nके लिए बधाई [%s] उसे पाने के लिए कृपया ग्राहक सेवा से संपर्क करें\n48 घंटों में।", "LuckTurntables_tips_text_5": "Congratulations on winning the sports trial coins red\nenvelope [%s] Please contact customer service\nwithin 48 hours.", "LuckTurntables_tips_text_help_0": "Congratulations! You won [%s]\ngold coin red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_1": "Congratulations! You won [%s]\ncasino chip red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_2": "Congratulations! You won [%s]\nUSDT red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_3": "Congratulations! You won %s\nred packet, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_5": "Congratulations! You won %s\nsports trial coins, please check in\n【Profile】-【My Red Packet】", "LuckTurntablesButton_des_text": "शुरू होगा", "LuckTurntablesButton_des_1_text": "चालू है", "LuckTurntablesButton_tips_text": "लाल लिफाफा अवोर्ड निकालने के क्लिक करें, आज के लोटरी इवेंट के अंत से पहले यह मौका प्रभावी है।", "RedPackets_des_text": "आपने एक भाग्यशाली लाल लिफाफा जीता है", "Small_Game_Hongbao_Top": "मुख्य %d विजेता", "RedEnvelope_usdt_tool_tip": "Crypto wallet user. Use crypto wallet user to make your deposits/withdrawals easier and safer", "Game_Hongbao_desc_0": "बधाइयाँ, %s खिलाड़ी %s, ने जीता है| %s#E8C892| सोने का सिक्का लाल लिफाफा अवोर्ड।", "Small_Game_Hongbao_desc_0": "कॉन्फ़िगरेशन खिलाड़ी %s खेल में %s जीतने पर लाल लिफाफा| %s#E8C892| गोल्ड।", "Game_Hongbao_desc_1": "बधाइयाँ, %s खिलाड़ी %s, ने जीता है| %s#E8C892| सोने का सिक्का लाल लिफाफा अवोर्ड।", "Small_Game_Hongbao_desc_1": "कॉन्फ़िगरेशन खिलाड़ी %s खेल में %s जीतने पर लाल लिफाफा| %s#E8C892| गोल्ड।", "Game_Hongbao_desc_2": "%s खिलाड़ी %s |%s#FFCC00| कोUSD लाल लिफाफा मिलने पर बधाईयाँ", "Small_Game_Hongbao_desc_2": "%s खिलाड़ी %s |%s#FFCC00| कोUSD लाल लिफाफा मिलने पर बधाईयाँ", "Game_Hongbao_desc_3": "%s खिलाड़ी %s को %s मिलने पर बधाइयाँ", "Small_Game_Hongbao_desc_3": "%s खिलाड़ी %s को %s मिलने पर बधाइयाँ", "Game_Hongbao_desc_5": "Congratulations, |%s#D7C647| player |%s#D7C647|, has won a %s Sports Betting Trial Coin.", "Small_Game_Hongbao_desc_5": "Congratulations, |%s#D7C647| player |%s#D7C647|, has won a %s Sports Betting Trial Coin.", "ServerErrorCode1": "ठीक है", "ServerErrorCode2": "नया वर्ज़न उपलब्ध है, कृपया पहले अपडेट करें", "ServerErrorCode3": "खिलाड़ी ढूंढने में असमर्थ", "ServerErrorCode4": "अन्य उपकरण से लॉग इन किया हुआ है, कृपया फिर से लॉग इन करें", "ServerErrorCode5": "टोकन की पूछताछ करने में असमर्थ, कृपया हमारी ग्राहक सेवा को संपर्क करें", "ServerErrorCode6": "वैश्विक सर्वर से डेटा पाने में असमर्थ", "ServerErrorCode7": "आंतरिक RPC त्रुटि", "ServerErrorCode8": "आंतरिक RPC मूल्य वापस त्रुटि", "ServerErrorCode17": "ज्यादा रूम बनाने में असमर्थ", "ServerErrorCode18": "बहुत ज्यादा रूम बनाए गए है", "ServerErrorCode19": "अमान्य मापदंड", "ServerErrorCode20": "भुगतान करने में असमर्थ, कृपया रिचार्ज करें", "ServerErrorCode21": "मान्यकरण निष्फल हुआ है", "ServerErrorCode22": "रूम खारिज कर दिया गया है", "ServerErrorCode23": "सिर्फ रूम का मालिक उसे ख़ारिज कर सकता है", "ServerErrorCode24": "रूम भर गया", "ServerErrorCode25": "आप पहेले से ही रूम में है", "ServerErrorCode26": "खिलाड़ी रूम में नहीं है", "ServerErrorCode27": "पद पहले से ले लिया गया है", "ServerErrorCode28": "अगर आप को बैठना है तो सिक्के चाहिए होंगे", "ServerErrorCode29": "टेबल भर गया है", "ServerErrorCode30": "खिलाड़ी बैठे है", "ServerErrorCode31": "खेल के दौरान बैठने में असमर्थ", "ServerErrorCode32": "पर्याप्त सिक्के नहीं है", "ServerErrorCode33": "निरुदेश्यता से बैठने में असमर्थ", "ServerErrorCode34": "निरुदेश्यता से बैठने में असमर्थ2", "ServerErrorCode35": "पद ले लिया गया है", "ServerErrorCode36": "पद लेने में असमर्थ", "ServerErrorCode37": "पद लेने में असमर्थ2", "ServerErrorCode38": "खड़े होने और देखने में असमर्थ", "ServerErrorCode39": "सोने के सिक्के महत्तम सीमा से बढ़ गए है।", "ServerErrorCode39_usdt": "USDT महत्तम सीमा से बढ़ गए है।", "ServerErrorCode40": "मल्टी बाय-इन करने में असमर्थ", "ServerErrorCode41": "सिर्फ मालिक जवाब दे सकते है", "ServerErrorCode42": "रिकॉर्डिंग फी का भुगतान करने में असमर्थ, कृपया रिचार्ज करें", "ServerErrorCode43": "बाय-इन एप्लिकेशन टाइम आउट", "ServerErrorCode44": "अमान्य बाय-इन रकम स्वीकृति", "ServerErrorCode45": "सिर्फ मालिक खेल शुरू कर सकते है", "ServerErrorCode46": "खेल पहले से शुरू हो चूका है।", "ServerErrorCode47": "खेल शुरू करने के लिए अपर्याप्त खिलाडी।", "ServerErrorCode48": "आपका टर्न अभी तक नहीं आया है", "ServerErrorCode49": "बेट की गलत रकम", "ServerErrorCode50": "अवैध क्रिया", "ServerErrorCode51": "आपका टर्न अभी तक नहीं आया है", "ServerErrorCode52": "कन्फिगरेशन फाइल त्रुटि", "ServerErrorCode53": "अपर्याप्त धनराशि, कृपया रिचार्ज करे", "ServerErrorCode54": "सिर्फ बैठे हुए खिलाडी मालिक के साथ चेट कर सकते है", "ServerErrorCode55": "बीमा खरीदने के लिए अमान्य खिलाडी ID", "ServerErrorCode56": "बीमा खरीदारी निवेदन का समय ख़त्म हो गया है", "ServerErrorCode57": "चालू खेल में बीमा खरीदने में असमर्थ", "ServerErrorCode58": "बीमा ख़रीदा गया है", "ServerErrorCode59": "पॉट ID ढूंढने में असमर्थ", "ServerErrorCode60": "उपलब्ध आउट रकम से ज्यादा", "ServerErrorCode61": "खरीदारी की रकम पॉट की रकम से 1/3 ज्यादा है", "ServerErrorCode62": "खरीदारी करने के लिए पॉट पर्याप्त नहीं है", "ServerErrorCode63": "अमान्य आउट खरीदारी", "ServerErrorCode64": "अमान्य आउट खरीदारी2", "ServerErrorCode65": "बैठे हुए खिलाड़ी के लिए ही क्रिया उपलब्ध है", "ServerErrorCode66": "निकलने का समय आ गया है, आपका पद संरक्षित किया जाएगा", "ServerErrorCode67": "एप्लिकेशन भेजी गई है, मालिक की मंजूरी की प्रतीक्षा करें। 180 सेकंड में ख़त्म हो जाएगी", "ServerErrorCode68": "अभी सीट पर नहीं है", "ServerErrorCode69": "आपके पद का संरक्षण करते समय टेबल से निकल गए", "ServerErrorCode70": "जब आपका पद संरक्षित हो रहा हो तब आप बहार नहीं निकल सकते", "ServerErrorCode71": "अमान्य खिलाड़ी ID", "ServerErrorCode72": "अभी रेज करने में असमर्थ, आप ऑल इन या कॉल कर सकते हो", "ServerErrorCode73": "दुनियावी सर्वर से जुड़ने में असमर्थ", "ServerErrorCode74": "सिर्फ क्लब एडमिनिस्ट्रेटर क्लब रूम बना सकते है", "ServerErrorCode75": "बनाए हुए रूम की रकम सीलिंग तक पहुँच गई है", "ServerErrorCode76": "रूम बनाते समय अन्य त्रुटियाँ हुई है", "ServerErrorCode77": "अवैध बाय-इन रकम", "ServerErrorCode78": "आखिरी खिलाड़ी", "ServerErrorCode79": "बीमा वापस लाना पड़ेगा", "ServerErrorCode80": "मालिक नहीं मिला", "ServerErrorCode81": "गलत आउट रकम", "ServerErrorCode82": "अमान्य खरीदारी रकम", "ServerErrorCode83": "बीमा ज़रूरी है", "ServerErrorCode84": "गल<PERSON> करार", "ServerErrorCode85": "बाकी के कार्ड्स जाँचने में असमर्थ", "ServerErrorCode86": "बाकी के कार्ड्स जाँचने में असमर्थ", "ServerErrorCode87": "सिर्फ एडमिनिस्ट्रेटर", "ServerErrorCode88": "खेल शुरू नहीं हुआ है", "ServerErrorCode89": "खिलाड़ी बैठने के लिए ब्लेकलिस्ट में है", "ServerErrorCode90": "मालिक ने आपको बैठने से मना किया ", "ServerErrorCode91": "खिलाड़ी बैठने के लिए ब्लेकलिस्ट में नहीं है", "ServerErrorCode92": "वर्तमान खेल अभी शुरू नहीं हुआ है।", "ServerErrorCode93": "बहुत ज्यादा अतिरिक्त समय इस्तेमाल हुआ है", "ServerErrorCode94": "अलायंस क्लब पाने में असमर्थ", "ServerErrorCode95": "खेल चालू है", "ServerErrorCode96": "सिक्के निकालने में असमर्थ", "ServerErrorCode97": "आपको एंटी-गेंग-चीटिंग सिस्टम द्वारा बैठने से मना किया गया है", "ServerErrorCode98": "बाय-इन करने के लिए अपर्याप्त जेम्स", "ServerErrorCode99": "अलायंस की सीमा के साथ क्लब बाय-इन रकम जोड़ें", "ServerErrorCode100": "क्लब बाय-इन अलायंस द्वारा प्रतिबंधित", "ServerErrorCode101": "जबरन शोडाउन अभी के लिए प्रतिबंधित है", "ServerErrorCode102": "जबरन शोडाउन महत्तम सीमा तक पहुँच गया है", "ServerErrorCode103": "समय पूर्ण हो चूका है", "ServerErrorCode104": "खेल के सर्वर का रखरखाव चल रहा है", "ServerErrorCode105": "क्योंकि इस टेबल से आपका अकाउंट सेटल हो चूका है", "ServerErrorCode106": "आपने सेटल खाता खोला है, आप इस राउंड के बाद टेबल छोड़ने वाले है।", "ServerErrorCode107": "वर्तमान खेल से आप का खाता सेटल हो चूका है, कृपया फिर से शुरू न करें", "ServerErrorCode108": "आपने वर्तमान खेल बाय-इन नहीं किया है, खाता सेटल करने की ज़रूरत नहीं है।", "ServerErrorCode109": "आप एक ही समय में दोनों पासवर्ड सेट नहीं कर सकते।", "ServerErrorCode110": "गलत पासवर्ड", "ServerErrorCode111": "खिलाड़ी की सीमा पूर्ण हो चुकी है", "ServerErrorCode113": "खिलाड़ी ऑल इन, सब म्यूट...", "ServerErrorCode117": "क्लब बनाने में असमर्थ", "ServerErrorCode118": "ज्यादा क्लब बनाने में असमर्थ", "ServerErrorCode119": "मापदंड त्रुटि हुई", "ServerErrorCode120": "क्लब का गलत प्रकार", "ServerErrorCode121": "क्लब ID ढूंढने में असमर्थ", "ServerErrorCode122": "सिर्क एडमिनिस्ट्रेटर क्लब को खारिज कर सकते है", "ServerErrorCode123": "क्लब भर गया है, जुड़ने में असमर्थ", "ServerErrorCode124": "आप पहले से क्लब में शामिल है", "ServerErrorCode125": "क्लब में जुड़ने के लिए आपने पहले से ही निवेदन किया हुआ है", "ServerErrorCode126": "खिलाड़ी क्लब में नहीं है", "ServerErrorCode127": "क्लब का फंड अभी तक क्लियर नहीं हुआ है", "ServerErrorCode128": "क्लब का सदस्य अभी तक क्लियर नहीं हुआ है", "ServerErrorCode129": "क्लब एडमिनिस्ट्रेटर पूर्ण", "ServerErrorCode130": "पर्याप्त जेम्स नहीं है, कृपया रिचार्ज करें", "ServerErrorCode131": "बेकार क्लब स्टार रेंकिंग", "ServerErrorCode132": "क्लब क़ीमत पाने में असमर्थ", "ServerErrorCode133": "स्टार क्लब खरीदने में असमर्थ", "ServerErrorCode134": "कम्युनिटी नहीं मिली'", "ServerErrorCode135": "प्राधिकरण निष्फल", "ServerErrorCode136": "त्रुटि", "ServerErrorCode137": "क्लब का नाम पहले से मौजूद है", "ServerErrorCode138": "अन्य एडमिनिस्ट्रेटर पहले से ही यह कर रहे है", "ServerErrorCode139": "अन्य एडमिनिस्ट्रेटर पहले से ही यह कर रहे है", "ServerErrorCode149": "ज्यादा अलायंस बनाने में असमर्थ", "ServerErrorCode150": "अलायंस का नाम पहले से मौजूद है", "ServerErrorCode151": "अलायंस बनाने में असमर्थ", "ServerErrorCode152": "अलायंस सदस्य अभी तक क्लियर नहीं हुए", "ServerErrorCode153": "अलायंस ढूंढने में निष्फल", "ServerErrorCode154": "अलायंस मंजूरी अस्वीकृत की गई है", "ServerErrorCode155": "क्लब अलायंस में नहीं है", "ServerErrorCode156": "अलायंस ढूंढने में असमर्थ", "ServerErrorCode157": "क्लब पहले से अलायंस में है", "ServerErrorCode158": "एप्लिकेशन भेजी गई है", "ServerErrorCode159": "अलायंस भर गया है", "ServerErrorCode160": "अन्य एडमिनिस्ट्रेटर पहले से ही यह कर रहे है", "ServerErrorCode161": "अन्य त्रुटियाँ हुई", "ServerErrorCode162": "सर्वर <PERSON><PERSON> Parse निष्फल", "ServerErrorCode163": "डेटाबेस सहेजने में निष्फल", "ServerErrorCode164": "ज्यादा अलायंस जोड़ने में असमर्थ", "ServerErrorCode165": "क्लब जेकपोट बोनस दर सेट करने में असमर्थ", "ServerErrorCode166": "क्लब जेकपोट बोनस दर पाने में असमर्थ", "ServerErrorCode167": "खेल चालू है, अलायंस में से क्लब निकालने में असमर्थ", "ServerErrorCode168": "खेल चालू है, जेकपॉट सेट करने में असमर्थ", "ServerErrorCode169": "खेल चालू होने के समय पर क्लब ख़ारिज करने में असमर्थ", "ServerErrorCode170": "कृपया पहले बनाया हुआ अलायंस ख़ारिज करें", "ServerErrorCode171": "कृपया पहले बनाया हुआ अलायंस ख़ारिज करें", "ServerErrorCode172": "पर्याप्त सिक्के नहीं है", "ServerErrorCode173": "खिलाड़ी का सिस्टम मेल टाइम स्टेम्प पाने में निष्फल", "ServerErrorCode174": "खिलाड़ी का मेल लिस्ट पाने में निष्फल", "ServerErrorCode175": "अनुरोधित सूचकांक अवैध है", "ServerErrorCode176": "महत्तम सीमा तक पहुँचने के लिए क्लब से जुड़े", "ServerErrorCode177": "खेल चालू होने के समय पर क्लब ख़ारिज करने में असमर्थ", "ServerErrorCode179": "खिलाड़ी का घोषणा लिस्ट पाने में निष्फल", "ServerErrorCode180": "मेल का कन्टेन्ट योग्य नहीं है।", "ServerErrorCode181": "पहले से जुड़े हुए दलों से बहार निकलें", "ServerErrorCode182": "क्लब सदस्य मिल नहीं रहा है", "ServerErrorCode187": "यूनियन से बहार निकलने की एप्लिकेशन प्रतिबंधित है, क्योंकि अभी यूनियन खेल चालू है", "ServerErrorCode190": "मोबाईल या क्षेत्र कोड खाली नहीं होना चाहिए", "ServerErrorCode191": "मोबाईल फोर्मेट गलत है", "ServerErrorCode192": "ईमेल एड्रेस गलत है", "ServerErrorCode193": "आप क्लब नहीं छोड़ सकते क्योंकि जेकपॉट खाली नहीं है", "ServerErrorCode194": "क्लब तोड़ा नहीं जा सकता क्योंकि क्लब का बेलेन्स नेगेटिव है", "ServerErrorCode195": "व्यक्तिगत प्रतिशत निर्धारित करने में निष्फल, अनुमति ज़रूरी है", "ServerErrorCode196": "व्यक्तिगत प्रतिशत सेट करने में निष्फल", "ServerErrorCode197": "आपका अकाउंट प्रतिबंधित किया है, अगर आपको कोई प्रश्न है, तो कृपया ग्राहक सेवा में संपर्क करें", "ServerErrorCode198": "व्यक्तिगत प्रतिशत निर्धारित करने में निष्फल, कृपया सेटिंग ना दोहराएं", "ServerErrorCode199": "सेटिंग टिप्पणी महत्तम सीमा पर पहुँच गई है", "ServerErrorCode200": "क्या आप सच में अकाउंट पासवर्ड पुनःप्राप्त करना चाहते है?", "ServerErrorCode201": "कृपया खेल ID के साथ लॉग इन में जाए, %sअगर आप पासवर्ड भूल गए है तो, कृपया ग्राहक सेवा में संपर्क करें。", "ServerErrorCode205": "आपकी तिज़ोरी में अपर्याप्त सिक्के है", "ServerErrorCode207": "उपयोगकर्ता रिकोर्ड खोजने में असमर्थ।", "ServerErrorCode208": "लोटरी जीतने के रिकोर्ड खोजने में असमर्थ।", "ServerErrorCode209": "गलत माध्यमिक पासवर्ड", "ServerErrorCode210": "लोटरी निकालने में निष्फल", "ServerErrorCode211": "डेटा की अर्जी निष्फल हुई है", "ServerErrorCode212": "रेडबैग प्रतिस्पर्धा बंद हो गई है", "ServerErrorCode213": "रेडबैग लेवल सेट नहीं किया गया है", "ServerErrorCode214": "रेडबैग रकम मान्य रकम सीमा में नहीं है", "ServerErrorCode215": "गोल्ड ऑपरेशन निष्फल", "ServerErrorCode216": "रेडबैग भेजने में निष्फल", "ServerErrorCode217": "सारी रेडबैग निकाल ली गई है", "ServerErrorCode219": "रेडबैग है ही नहीं", "ServerErrorCode221": "रेडबैग एक्सपायर हो गई है", "ServerErrorCode222": "रेडबैग बहुत बार ले रहे हैं", "ServerErrorCode223": "रेंक पाने में असमर्थ।", "ServerErrorCode224": "आपका नेटवर्क स्थायी नहीं है, कृपया फिर से लॉग इन करें।", "ServerErrorCode225": "सिस्टम त्रुटि", "ServerErrorCode226": "सिस्टम रखरखाव के कारण फिर से लॉग इन करें।", "ServerErrorCode228": "गौण पासवर्ड सेट नहीं है।", "ServerErrorCode229": "कृपया फिर से लॉग इन करें", "ServerErrorCode230": "एप्लिकेशन रद्द", "ServerErrorCode232": "इस महीने कम्युनिटी बदल गई है", "ServerErrorCode233": "अभी कम्युनिटी बनाने की अनुमति नहीं है", "ServerErrorCode234": "वर्तमान खिलाड़ी अन्य कम्युनिटी से जुड़ नहीं सकते है", "ServerErrorCode235": "वर्तमान कम्युनिटी खिलाड़ी अन्य कम्युनिटी में जुड़ नहीं सकते", "ServerErrorCode238": "पहली कम्युनिटी में बदलाव करने में असमर्थ, कृपया ग्राहक सेवा में संपर्क करें", "ServerErrorCode251": "USDT की अपर्याप्त रकम", "ServerErrorCode252": "विनिमय असामान्य है, कृपया फिर से कोशिश करें", "ServerErrorCode253": "विनिमय सीमा 20USD～100W USD है", "ServerErrorCode254": "Your safe has insufficient USDT", "ServerErrorCode255": "The room opens on time", "ServerErrorCode256": "The room has been dismissed", "ServerErrorCode257": "Please wait for %s minutes before the next (gold coins to USDT) exchange", "ServerErrorCode280": "Booster does not exist", "ServerErrorCode281": "Booster expired", "ServerErrorCode282": "Maximum booster received", "ServerErrorCode283": "Already received a booster from the player", "ServerErrorCode284": "Not enough boosters to open red packet", "ServerErrorCode285": "Invalid Recipient", "ServerErrorCode286": "Daily booster limit reached", "ServerErrorCode287": "Can't help myself", "ServerErrorCode288": "Please register a game account firs", "ServerErrorCode291": "You have used up this week's boosters", "ServerErrorCode292": "Only accounts created for more than %s days are qualified", "ServerErrorCode293": "Only deposited users can send boosters", "ServerErrorCode501": "आपके क्षेत्र में सेवा उपलब्ध नहीं है।", "ServerErrorCode502": "एडवान्स IP/GPS चैक पास नहीं कर सका।", "ServerErrorCode503": "बैठा नही जा सकता, कृपया दूसरा टेबल आजमाएं", "ServerErrorCode504": "बैठने में निष्फल, यह विशेषता आज की उपयोग सीमा पर पहुँच गई है", "ServerErrorCode505": "GAMEUUID does not exist", "ServerErrorCode506": "इस हाथ के फैसले के बाद आप इस खेल से बहार निकल जाएंगे", "ServerErrorCode508": "ख़ास आमंत्रित खिलाड़ी टेबल पर बैठेंगे", "ServerErrorCode509": "टेबल पर के नरेटर की मदद न करें", "ServerErrorCode512": "Re-enter the room in %s", "ServerErrorCode513": "Re-enter your seat in %s", "ServerErrorCode515": "Buy-in has to be more than the chips you exited with, please add chips and try again.", "ServerErrorCode1002": "यह रूम भंग होने वाला है", "ServerErrorCode1201": "इस हाथ के फैसले के बाद आप इस खेल से बहार निकल जाएंगे", "ServerErrorCode1204": "इस टेबल की बाय-इन सीमा से ज्यादा है, इसीलिए अस्थायी रूप से आपको बाय-इन करने की ज़रूरत नहीं है।", "ServerErrorCode1206": "ऑपरेशन निष्फल, कृपया फिर से प्रयास करें", "ServerErrorCode1207": "हाथ रिपीट बेट का अनुमान लगाएं", "ServerErrorCode1208": "हाथ बेट टाइम आउट का अनुमान लगाएं", "ServerErrorCode1209": "अपर्याप्त बेलेन्स, हाथ बेट का अनुमान लगाने में निष्फल", "ServerErrorCode1210": "सट्टेबाजी सेटिंग मतदान में असफल", "ServerErrorCode1211": "अतिथि सट्टा लगाने पर अज्ञात एरर", "ServerErrorCode1212": "अनुमान अज्ञात त्रुटि", "ServerErrorCode1213": "आपके क्षेत्र में सेवा उपलब्ध नहीं है", "ServerErrorCode1216": "ज्यादातर इमोजी का उपयोग। कृपया बाद में फिर से प्रयास करें", "ServerErrorCode1249": "New exception-Vietnamese players cannot enter", "ServerErrorCode1250": "टेबल देखने की सीमा समाप्त हो गई है, कृपया कोई और टेबल आज़माएं", "ServerErrorCode1251": "कोई मेल खाता हुआ रूम नहीं मिला", "ServerErrorCode1252": "स्वीकृत", "ServerErrorCode1254": "This seat is reserved and will be released 5 - 10 minutes before the game", "ServerErrorCode1255": "This seat is reserved, Star table will begin in %s", "ServerErrorCode1256": "Special guests occupy the seat, If after %s the special guests is not online, Will be released", "ServerErrorCode1257": "Gifting failed, player not a star member", "ServerErrorCode1258": "Incorrect gift ID or quantity", "ServerErrorCode1262": "Star player not seated, please send gifts later", "ServerErrorCode1263": "Cannot gift yourself", "ServerErrorCode1302": "माफ़ कीजिए, इस लेवल पर कोई मेल खाता हुआ टेबल नहीं मिला।", "ServerErrorCode31121": "Reserved seat, please choose another table.", "ServerErrorCode31123": "खेल के सर्वर का रखरखाव चल रहा है", "pop_silence_title": "Responsible Gaming", "pop_silence_tips1": "Do you want to take a break?", "pop_silence_tips2": "It looks like you’ve been playing\nfor too long.", "pop_silence_tips3": "You are in breaktime", "pop_silence_btn_quit": "Exit", "pop_silence_btn_continue": "Continue", "CowboyExit_bg_exit_text": "क्या आप वाकई इस टेबल को छोड़ना चाहते हैं?", "InquireView_content_gold_coin": "GOLD COIN", "USDTView_title_label": "रूपांतरण", "USDTView_exchange": "एक्सचेंज", "USDTView_exchange_coin_label": "Exchange coins", "USDTView_exchange_usdt_label": "एक्सचेंज USDT", "USDTView_from_label": "से", "USDTView_to_label": "तक", "USDTView_coin_label": "Coins", "USDTView_input_num_label": "मात्रा दर्ज करें", "USDTView_exchange_num_label": "मात्रा बदलें", "USDTView_all_label": "सब", "USDTView_usdt_coin_ex_label_0": "रूपांतरण दर 1USD=%s सोने के सिक्के", "USDTView_usdt_coin_ex_label_1": "एक्सचेंज दर 1 सोने का सिक्का = %sUSD", "USDTView_exchange_label": "एक्सचेंज", "USDTView_explan_label": "एक्सचेज सीमा 20USD～100W USD है\nएक्सचेंज दर कभी भी बदल सकता है, दर्शाया गया एक्सचेंज दर केवल संदर्भ के लिए है, और सबमिशन के समय का एक्सचेंज दर इस्तेमाल में लिया जाएगा\nरिडेम्पशन प्रक्रिया के दौरान रद्द नहीं किया जा सकता ", "USDTView_ex_coin_success_label": "%s सोने के सिक्के सफलतापूर्वक एक्सचेंज किए गए", "USDTView_ex_usdt_success_label": "%sUSD को सफलतापूर्वक अवेजा गया", "USDTView_ex_coin_error_0_usdt": "USDT अकाउंट 0 है और इसे सोने के सिक्कों में नहीं बदला जा सकता है", "USDTView_usdt_balance_label": "USD बेलेन्स:", "USDTView_txt_usdt": "USD", "USDTView_exchange_btn_label": "अदला बदली", "USDTView_bring_num_label": "पॉइंट्स लाएं", "USDTView_input_invalid_num_label": "कृपया एक उचित मूल्य दर्ज करें", "USDTView_usdt_chanage_1": "%s सोने के सिक्कों के एक्सचेंज के लिए %sUSDT का उपयोग करें", "USDTView_usdt_chanage_2": "%s जेकफ्रूट पॉइंट के एक्सचेंज के लिए %sUSDT का उपयोग करें", "USDTView_usdt_change_free_tips": "Remaining free exchange: %s", "USDTView_usdt_change_fee_tips": "Service fee %s,actual USDT redeemed %s USDT", "USDTView_usdt_change_point_tips": "Use %s points for %s service fees", "USDTView_usdt_change_coin_tips": "Gold Coin Balance", "USDTView_usdt_change_usdt_tips": "USDT Balance", "USDTView_usdt_change_title_tips": "Account <PERSON><PERSON>", "USDTView_exchange_tips_label": "प्रत्येक खाते का आनंद %s हर रोज मुफ्त एक्सचेंज\nहर दिन आधी रात को नए मुफ्त एक्सचेंज जारी किए जाते हैं,बिना रोलओवर \n प्रत्येक लेनदेन के बीच का अंतराल है %s मिनट", "Humanboy_advancedSetting_desc": "आप पाँच अलग मूल्यवर्ग चुन सकते हैं।", "Humanboy_advancedSetting_auto": "ऑटोबेट सेटिंग्स", "Humanboy_advancedSetting_opt_normal": "सामान्य", "Humanboy_advancedSetting_opt_advance": "एडवांस", "Humanboy_advancedSetting_opt_advance_extra": "(हाथ की अवधि को कस्टमाइज करें)", "Cowboy_fuhao_no_text": "Rich#%d", "Cowboy_shensuanzi_text": "निपुण", "CowBoy_btn_desc_auto_cancel": "र<PERSON><PERSON><PERSON> करना", "CowBoy_btn_desc_auto_recharge": "रि<PERSON><PERSON><PERSON><PERSON>ज", "CowBoy_btn_desc_auto_count": "%d हाथ", "CowBoy_btn_desc_auto_using_count": "%d/%d हाथ", "CowBoy_btn_desc_stop_auto_bet": "ब<PERSON><PERSON> करें", "CowBoy_btn_desc_resume_auto_bet": "र<PERSON><PERSON><PERSON> करना", "CowBoy_btn_desc_exit_game": "छोड़ना", "CowBoy_btn_desc_resume_game": "र<PERSON><PERSON><PERSON> करना", "CowBoy_btn_desc_switch_auto_bet": "स्विच", "Cowboy_auto_bet_stop_tips": "क्या आप ऑटोबेट को बंद करेंगे?\n आपके पास अभी भी %d/%d बेट है।", "Cowboy_auto_bet_switch_tips": "ऑटोबेट सक्रिय है, स्विच करने से बाकी %d/%d बेट समाप्त हो जाएगी।", "Cowboy_auto_bet_exit_tips": "ऑटोबेट सक्रिय है, छोड़ने से बाकी\n %d/%d बेट समाप्त हो जाएगी।", "Cowboy_last20_text": "अंतिम 20 हाथ", "Humanboy_list_online": "ऑनलाइन खिलाड़ी:", "Humanboy_list_change_rank": "सूची बदलें", "Humanboy_list_rank_0": "सबसे ज्यादा जीते (Tdy)", "Humanboy_list_rank_1": "लगातार जीत (Tdy)", "Humanboy_list_rank_2": "एक हाथ (Tdy)", "Humanboy_list_rank_3": "लगातार जीत (Tdy)", "Humanboy_list_rank_4": "एक हाथ (ऑल)", "Humanboy_list_rank_5": "एक दिन में (ऑल)", "Humanboy_list_profit": "मुनाफ़ा राशि:", "Humanboy_list_frequency_time": "समय जीता:", "Humanboy_list_frequency": "%d", "Humanboy_list_myrank": "मेरी रेंकिंग", "Cowboy_ludan_guide_text": "हाथ का इतिहास देखने के लिए टैप करें", "minigame_rebate_top_rank": "%s place", "minigame_rebate_top_rank_button": "You beat %s, ranked %s", "minigame_rebate_top_rank_tips": "%s Place", "minigame_rebate_no_rank": "Unlisted", "minigame_rebate_toast_top_tips": "Top %d%", "minigame_rebate_toast_top_no_enemy": "You have become the %s place", "minigame_rebate_period_desc": "%s～%s\nGet reward daily during event", "minigame_rebate_text_month": "/", "minigame_rebate_text_period": "%s %s to %s %s", "minigame_rebate_surpassed_reward": "Players not in the top %d will also receive an %s reward subsidy.", "minigame_rebate_daily_ranking": "Overall Ranking:", "minigame_rebate_daily_ranking_daily": "Daily Ranking:", "minigame_rebate_claim_note": "<color=#9CB8A3>Note: Please collect the rewards in time so that you won’t miss them after the event expires</color>", "minigame_rebate_rank_1_reward": "1st place bonus: %s", "minigame_rebate_rank_2_reward": "2nd place bonus: %s", "minigame_rebate_rank_3_reward": "3rd place bonus: %s", "minigame_rebate_rank_4_reward": "4th place bonus: %s", "minigame_rebate_rank_5_reward": "5th place bonus: %s", "minigame_rebate_rank_6_reward": "6th place bonus: %s", "minigame_rebate_rank_7_reward": "7th place bonus: %s", "minigame_rebate_rank_8_reward": "8th place bonus: %s", "minigame_rebate_rank_9_reward": "9th place bonus: %s", "minigame_rebate_rank_10_reward": "10th place bonus: %s", "minigame_rebate_reward_popup": "<color=#ffffff>Congratulation on winning</c> %s", "minigame_rebate_receive_reward_success": "Congratulations on getting %s", "minigame_number_date": "st nd rd th", "minigame_number_month": "January February March April May June July August September October November December", "minigame_total_betting": "Already Bet: ", "minigame_cowboy_rebate_today": "Today", "minigame_cowboy_rebate_day": "Sun Mon Tues Wed Thurs Fr<PERSON>", "minigame_cowboy_rebate_expired": "Expired", "minigame_cowboy_rebate_title": "Limited Time Challenge", "minigame_cowboy_rebate_content": "For a limited time, bet on <color=#ECD27D>Texas Cowboy</color> to <color=#ECD27D>gain reward</color>. If you meet the required amount.", "minigame_cowboy_rebate_title_activity_2": "Limited Time Reward", "minigame_cowboy_rebate_content_activity_2": "During the event period, receive <color=#ECD27D>full rewards daily</color> when bet %s gold coins in <color=#ECD27D>Texas Cowboys</color>.\n<color=#FFF79B>Note: Please collect the rewards in time in case you can’t get them after the event expires.</color>", "minigame_cowboy_rebate_content_activity_2_one_game": "During the event period, receive <color=#ECD27D>full rewards daily</color> when bet %s gold coins in <color=#ECD27D>Texas Cowboys</color>.\n<color=#9CB8A3>Note: Please collect the rewards in time in case you can’t get them after the event expires.</color>", "minigame_cowboy_rebate_title_activity_3": "Challenge Day", "minigame_cowboy_rebate_content_activity_3": "在规定时间及日期内每天，在<color=#EBFF00>德州牛仔, 扑克大师或百人德州</c>中投注满XXX金币即可获得当天全部奖励。", "minigame_cowboy_rebate_title_activity_4": "Total Bet Ranking", "minigame_cowboy_rebate_title_activity_4_daily": "Limited Time Ranking", "minigame_cowboy_rebate_content_activity_4": "Limited time to place bets in the <color=#ECD27D>Texas Cowboy</color> mini-game. The top %d players will receive a fixed reward. %s Winners, please return to this event interface to claim your prize.", "minigame_cowboy_rebate_content_activity_4_daily": "From %s, there will be a limited time every day betting in the <color=#ECD27D>Texas Cowboy</color> mini-game. The top %d players will receive a fixed reward. %s Winners, please return to this event interface to claim your prize.", "minigame_already_bet": "Placed Bet", "day": "d", "hour": "hr", "minute": "min", "seconds": "sec", "CountDown": "Remaining time of event:", "Claimed": "Claimed", "Unclaimed": "Unclaimed", "string_comma": ", ", "string_and": "and", "minigame_currency_type_2": "gold coins", "minigame_currency_type_3": "casino coins", "Cowboy_coin_short_text": "W", "Humanboy_game_gold_short_suffix_w": "W", "Humanboy_game_gold_short_suffix_million": "<PERSON>l", "Humanboy_game_gold_short_suffix_billion": "Bil", "PokerMaster_dialog_recharge": "Go to the wallet to recharge", "minigame_prompt_coin_remain": "Remaining", "minigame_prompt_coin_used": "Used", "minigame_prompt_casino_coin": "Casino Coins:", "MiniGames_SYSTEM_FORCE_CLOSED": "Room closed by system", "minigame_Title_10": "COWBOY", "minigame_Title_30": "BEAT THE BANKER", "minigame_Title_50": "LIVE STREAM", "minigame_Title_70": "MASTER", "Rebate_betting_bonus": "Bet Bonus", "Rebate_all_claimed": "All Claimed.", "betting_rebate_event_title": "Betting Rebate Event", "bet_bonus_tab": "Bet Bonus", "leaderboard_tab": "Leaderboard", "total_bet": "Total Bet:", "next_level_bonus": "Next level bouns, get more:", "collected": "Collected:", "Rebate_bet_bnous_countdown": "Ends in", "Rebate_leaderboard_countdown": "Countdown to the final:", "Rebate_has_end_bet_bonus": "Event Ended", "Rebate_has_end_leaderboard": "Ends at %s", "Rebate_start_in": "Start Time: %s", "Rebate_leaderboard_no_ranking_yet": "No Rankings Yet", "Rebate_claim_reward_success": "You've successfully claimed %s! Check [My Wallet]", "Rebate_rank": "Rank", "Rebate_player": "Player", "Rebate_reward": "<PERSON><PERSON>", "Rebate_bet_amount": "Real-time bet amount", "Rebate_not_rank": "Not Rank", "Rebate_my_self": "I am my self"}}}}