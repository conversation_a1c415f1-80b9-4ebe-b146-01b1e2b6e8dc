{"groups": {"en_US": {"strings": {"InquireView_lab_3": "integral", "Cancel": "Cancel", "Confirm": "Confirm", "MiniGame_AddAutoBet_Text": "20 hands", "MiniGame_Switch_Table": "Switch to another table", "MiniGame_Exit": "Exit", "MiniGame_Switch_Content": "The table is closed", "MiniGame_Select_AddAutoBet_Text": "%d hands", "MiniGame_btn_desc_auto_bet_reached": "Continue betting has reached the upper limit, and %d hands will be added for you", "M_UITitle113": "High Card", "M_UITitle114": "One Pair", "M_UITitle115": "Two Pair", "M_UITitle116": "Trips", "M_UITitle117": "Straight", "M_UITitle118": "Flush", "M_UITitle119": "Full House", "M_UITitle120": "Quads", "M_UITitle121": "Straight Flush", "M_UITitle122": "Royal Flush", "UIOpenNewWindow": "About to open the page in a new window", "TipsPanel_sure_button": "Confirm", "TipsPanel_cancel_button": "Cancel", "LuckTurntables_des_text": "Tap the wheel to win!", "LuckTurntables_tips_text_0": "Congratulations on getting the gold coin red\nenvelope [%s] please contact customer service\nwithin 48 hours.", "LuckTurntables_tips_text_1": "Congratulations on getting the casino coin red\nenvelope [%s] please contact customer service\nwithin 48 hours.", "LuckTurntables_tips_text_2": "Congratulations on winning the USD red envelope [%s]\nPlease contact customer service within 48 hours to collect", "LuckTurntables_tips_text_3": "Congratulations on your winning the match ticket\nPlease contact customer service within 48 hours to collect it", "LuckTurntables_tips_text_5": "Congratulations on winning the sports trial coins red\nenvelope [%s] Please contact customer service\nwithin 48 hours.", "LuckTurntables_tips_text_help_0": "Congratulations! You won [%s]\ngold coin red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_1": "Congratulations! You won [%s]\ncasino chip red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_2": "Congratulations! You won [%s]\nUSD red packets, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_3": "Congratulations! You won %s\nred packet, please check in\n【Profile】-【My Red Packet】", "LuckTurntables_tips_text_help_5": "Congratulations! You won %s\nsports trial coins, please check in\n【Profile】-【My Red Packet】", "LuckTurntablesButton_des_text": "will start", "LuckTurntablesButton_des_1_text": "ongoing", "LuckTurntablesButton_tips_text": "Click to Draw Red Envelop Award, This Opportunity Is Effective Before the End of the Lottery Event of Today.", "RedPackets_des_text": "You have won a lucky red packet", "Small_Game_Hongbao_Top": "TOP %d Winners", "RedEnvelope_usdt_tool_tip": "Crypto wallet user. Use crypto wallet user to make your deposits/withdrawals easier and safer", "Game_Hongbao_desc_0": "Congratulations, %s player %s, has won a| %s#E8C892| gold coin red envelope reward.", "Small_Game_Hongbao_desc_0": "Configuration Player %s in Game %s on Winning Red Envelop of| %s#E8C892| Gold.", "Game_Hongbao_desc_1": "Congratulations, %s player %s, has won a| %s#E8C892| Casino coin red envelope reward.", "Small_Game_Hongbao_desc_1": "Configuration Player %s in Game %s on Winning Red Envelop of| %s#E8C892| Casino coin.", "Game_Hongbao_desc_2": "Congratulations to %s player %s for getting |%s#FFCC00|USD red envelope", "Small_Game_Hongbao_desc_2": "Congratulations player %s in %s for getting |%s#FFCC00|USD red envelope", "Game_Hongbao_desc_3": "Congratulations to %s player %s for getting %s", "Small_Game_Hongbao_desc_3": "Congratulations player %s in %s for getting %s", "Game_Hongbao_desc_5": "Congratulations, |%s#D7C647| player |%s#D7C647|, has won a %s Sports Betting Trial Coin.", "Small_Game_Hongbao_desc_5": "Congratulations, |%s#D7C647| player |%s#D7C647|, has won a %s Sports Betting Trial Coin.", "ServerErrorCode1": "OK", "ServerErrorCode2": "New version available, please update first", "ServerErrorCode3": "Unable to find the player", "ServerErrorCode4": "Logined by another equipment, please login again", "ServerErrorCode5": "Unable to inquire token, please contact our customer service", "ServerErrorCode6": "Unable to obtain data from global server", "ServerErrorCode7": "Internal RPC error", "ServerErrorCode8": "Internal RPC return value error", "ServerErrorCode17": "Unable to create more room", "ServerErrorCode18": "Created too many rooms", "ServerErrorCode19": "Invalid parameters", "ServerErrorCode20": "Unable to pay, please recharge", "ServerErrorCode21": "Validation failure occurs", "ServerErrorCode22": "The room has been dismissed", "ServerErrorCode23": "Only the owner can dismiss the room", "ServerErrorCode24": "Room full", "ServerErrorCode25": "You are already in the room", "ServerErrorCode26": "The player is not in the room", "ServerErrorCode27": "The position has been taken", "ServerErrorCode28": "Coins required if you want to sit down", "ServerErrorCode29": "Table full", "ServerErrorCode30": "Player seated", "ServerErrorCode31": "Unable to sit during game", "ServerErrorCode32": "Not enough coins", "ServerErrorCode33": "Unable to sit randomly", "ServerErrorCode34": "Unable to sit randomly2", "ServerErrorCode35": "Position occupied", "ServerErrorCode36": "Unable to be seated", "ServerErrorCode37": "Unable to be seated2", "ServerErrorCode38": "Unable to stand up and spectate", "ServerErrorCode39": "The number of gold coins has reached the Maximum Limit.", "ServerErrorCode39_usdt": "The number of USD has reached the Maximum Limit.", "ServerErrorCode40": "Unable to multi Buy In", "ServerErrorCode41": "Only the owner can respond", "ServerErrorCode42": "Unable to pay the recording fee,please recharge", "ServerErrorCode43": "Buy-in application timeout", "ServerErrorCode44": "Invalid buy-in amount approval", "ServerErrorCode45": "Only the owner can start the game", "ServerErrorCode46": "Game is already started.", "ServerErrorCode47": "Not enough players to start the game.", "ServerErrorCode48": "Not your turn yet", "ServerErrorCode49": "Incorrect bet amount", "ServerErrorCode50": "Illegal action", "ServerErrorCode51": "Not your turn yet", "ServerErrorCode52": "Configuration file error", "ServerErrorCode53": "Insufficient funds, please recharge", "ServerErrorCode54": "Only seated player can chat with owner", "ServerErrorCode55": "Invalid player ID to buy insurance", "ServerErrorCode56": "Insurance purchase request time out", "ServerErrorCode57": "Unable to buy insurance in the current game", "ServerErrorCode58": "Insurance purchased", "ServerErrorCode59": "Unable to find pot ID", "ServerErrorCode60": "Exceed available OUTS amount", "ServerErrorCode61": "Purchase amount exceeds 1/3 amount of pot", "ServerErrorCode62": "the pot is not enough to purchase", "ServerErrorCode63": "Invalid Outs purchase", "ServerErrorCode64": "Invalid Outs purchase2", "ServerErrorCode65": "Action only available to seated players", "ServerErrorCode66": "It is time to leave, your position will be preserved", "ServerErrorCode67": "Application sent, waiting for the owner to approve. Expire in 180 seconds", "ServerErrorCode68": "Not in seat now", "ServerErrorCode69": "Already left the table while preserving your position", "ServerErrorCode70": "You are not in the status of leaving while your position preserved", "ServerErrorCode71": "Invalid player ID", "ServerErrorCode72": "Unable to raise now, you can All in or Call", "ServerErrorCode73": "Unable to connect to world server", "ServerErrorCode74": "Only club administrators can create club rooms", "ServerErrorCode75": "The amount of room created has reached the ceiling", "ServerErrorCode76": "Other errors occur while creating rooms", "ServerErrorCode77": "Illegal buy-in amount", "ServerErrorCode78": "Last player", "ServerErrorCode79": "Insurance need to be brought back", "ServerErrorCode80": "Owner not found", "ServerErrorCode81": "Incorrect Outs amount", "ServerErrorCode82": "Invalid purchase amount", "ServerErrorCode83": "Insurance is required", "ServerErrorCode84": "Incorrect agreement", "ServerErrorCode85": "Unable to check the rest of the cards", "ServerErrorCode86": "Unable to check the rest of the cards", "ServerErrorCode87": "Administrator Only", "ServerErrorCode88": "Game not started", "ServerErrorCode89": "The player is in the blacklist to sitdown", "ServerErrorCode90": "Owner refused you to sit down ", "ServerErrorCode91": "The player is not in the blacklist to sitdown", "ServerErrorCode92": "Current game not started yet.", "ServerErrorCode93": "Too much extra time used", "ServerErrorCode94": "Unable to obtain alliance club", "ServerErrorCode95": "Game in process", "ServerErrorCode96": "Unable to withdraw coins", "ServerErrorCode97": "You are prohibited from sitting down by anti-gang-cheating system", "ServerErrorCode98": "Not enough gems to buy in", "ServerErrorCode99": "Club buy-in amount over the limit of the alliance", "ServerErrorCode100": "Club buy-in prohibited by alliance", "ServerErrorCode101": "Forced showdown prohibited for now", "ServerErrorCode102": "Forced ShowDown Count Has Reached Maximum Limit", "ServerErrorCode103": "the number of times has been used up", "ServerErrorCode104": "Game Server Is Under Maintenance", "ServerErrorCode105": "Because Your Account Has Been Settled From This Table", "ServerErrorCode106": "You Have Launched <PERSON><PERSON> Account, You Are About To Leave The Table After This Round.", "ServerErrorCode107": "Current game is settled, please do not re-submit request", "ServerErrorCode108": "You Have Not Buyin Current Game, No Need To Settle Account.", "ServerErrorCode109": "You Cannot Set Both Password At The Same Time", "ServerErrorCode110": "Incorrect Password", "ServerErrorCode111": "Player limit has been reached", "ServerErrorCode113": "Player all in,  all mute...", "ServerErrorCode117": "Unable to create club", "ServerErrorCode118": "Unable to create more club", "ServerErrorCode119": "Parameter error occurs", "ServerErrorCode120": "Incorrect type of club", "ServerErrorCode121": "Unable to find club ID", "ServerErrorCode122": "Only administrator can dismiss the club", "ServerErrorCode123": "Club full, unable to join in", "ServerErrorCode124": "You are already in the club", "ServerErrorCode125": "You have already applied to join this club", "ServerErrorCode126": "The player is not in the club", "ServerErrorCode127": "Club fund not cleared yet", "ServerErrorCode128": "Club member not cleared yet", "ServerErrorCode129": "Club administrator full", "ServerErrorCode130": "Not enough gems, please recharge", "ServerErrorCode131": "Useless club star ranking", "ServerErrorCode132": "Unable to obtain club price", "ServerErrorCode133": "Unable to purchase star club", "ServerErrorCode134": "Community not found", "ServerErrorCode135": "Authorization failure!", "ServerErrorCode136": "Error", "ServerErrorCode137": "Club name already exists", "ServerErrorCode138": "Other administrators are already on the move", "ServerErrorCode139": "Other administrators are already on the move", "ServerErrorCode149": "Unable to create more alliance", "ServerErrorCode150": "Alliance name exists", "ServerErrorCode151": "Unable to create alliance", "ServerErrorCode152": "Alliance member not cleared yet", "ServerErrorCode153": "Fail to find alliance", "ServerErrorCode154": "Alliance permission denied", "ServerErrorCode155": "The club is not in the alliance", "ServerErrorCode156": "Unable to find the alliance", "ServerErrorCode157": "The club is already in the alliance", "ServerErrorCode158": "Application sent", "ServerErrorCode159": "Alliance full", "ServerErrorCode160": "Other administrators are already on the move", "ServerErrorCode161": "Other error occurs", "ServerErrorCode162": "Server <PERSON><PERSON>rse failure", "ServerErrorCode163": "Database failed to save", "ServerErrorCode164": "Unable to join more alliance", "ServerErrorCode165": "Unable to set club JackPot bonus rate", "ServerErrorCode166": "Unable to obtain club JackPot bonus rate", "ServerErrorCode167": "Game in process, unable to remove club from alliance", "ServerErrorCode168": "Game in process, unable to set <PERSON><PERSON>ot", "ServerErrorCode169": "Unable to dismiss the club when game is on", "ServerErrorCode170": "Please dismiss the previous alliance created", "ServerErrorCode171": "Please dismiss the previous alliance created", "ServerErrorCode172": "Not enough coins", "ServerErrorCode173": "Fail To Obtain Player's System Mail Time Stamp", "ServerErrorCode174": "Fail To Obtain Player's Mail List", "ServerErrorCode175": "Request Index Is Illegal", "ServerErrorCode176": "Join the club to reach the Maximum Limit", "ServerErrorCode177": "Unable to dismiss the club when game is on", "ServerErrorCode179": "Fail To Obtain Player's Anounce List", "ServerErrorCode180": "Mail Content Is Not Qualified.", "ServerErrorCode181": "Please Quit Previously Joined Union", "ServerErrorCode182": "Club Member Is Not Found", "ServerErrorCode187": "Because There Is Union Game Undergoing, Club Application For Quit Union Is Prohibited", "ServerErrorCode190": "Mobile Or Area Code Should Not Be Empty", "ServerErrorCode191": "Incorrect Mobile Format", "ServerErrorCode192": "Incorrect EMail Address", "ServerErrorCode193": "You Cannot Quit Club Because Jackpot Is Not Empty", "ServerErrorCode194": "Club Cannot Be Disband Because Club Balance Is Negtive", "ServerErrorCode195": "Fail To Set Personal Percent,Permission Required", "ServerErrorCode196": "Fail To Set Personal Percent", "ServerErrorCode197": "Your Account Has Been Banned，If You Have Any Questions, Please Contact Customer Service", "ServerErrorCode198": "Fail To Set Personal Percent, Please Do Not Repeat Setting", "ServerErrorCode199": "Setting Remarks Have Reach Maximum Limit", "ServerErrorCode200": "Are you sure to retrieve account password?", "ServerErrorCode201": "Please switch to login with game ID ,%sIf you forget your password, please contact with customer service。", "ServerErrorCode205": "Your safe has insufficient coins", "ServerErrorCode207": "Cannot Find User Record.", "ServerErrorCode208": "Cannot Find Lottery Winning Record.", "ServerErrorCode209": "Incorrect Secondary Password", "ServerErrorCode210": "Drawing Lottery Failed", "ServerErrorCode211": "Data Request Failed", "ServerErrorCode212": "RedBag Event Has Been Closed", "ServerErrorCode213": "RedBag Level Has Not Been Set", "ServerErrorCode214": "RedBag Amount Is Not In Allowed Amount Range", "ServerErrorCode215": "Gold Operation Failed", "ServerErrorCode216": "Sending RedBag Failed", "ServerErrorCode217": "All Red Bags Have Been Drawed", "ServerErrorCode219": "RedBag Does Not Exist", "ServerErrorCode221": "RedBag Has Expired", "ServerErrorCode222": "Grabbing RedBag Too Often", "ServerErrorCode223": "Failed to fetch the rank.", "ServerErrorCode224": "Your network is unstable, please login again.", "ServerErrorCode225": "System error", "ServerErrorCode226": "In system maintenance, please log in again.", "ServerErrorCode228": "Secondary Password not set.", "ServerErrorCode229": "Please log in again", "ServerErrorCode230": "Application Cancelled", "ServerErrorCode232": "The community has changed this month", "ServerErrorCode233": "Community creation is currently not allowed", "ServerErrorCode234": "Current player cannot join other communities", "ServerErrorCode235": "Current community players cannot join other communities", "ServerErrorCode238": "Unable to modify the first community, please contact customer service", "ServerErrorCode251": "Insufficient amount of USD", "ServerErrorCode252": "The exchange is abnormal, please try again later", "ServerErrorCode253": "The exchange limit is 20USD～100W USD", "ServerErrorCode254": "Your safe has insufficient USD", "ServerErrorCode255": "The room opens on time", "ServerErrorCode256": "The room has been dismissed", "ServerErrorCode257": "Please wait for %s minutes before the next (gold coins to USD) exchange", "ServerErrorCode280": "Booster does not exist", "ServerErrorCode281": "Booster expired", "ServerErrorCode282": "Maximum booster received", "ServerErrorCode283": "Already received a booster from the player", "ServerErrorCode284": "Not enough boosters to open red packet", "ServerErrorCode285": "Invalid Recipient", "ServerErrorCode286": "Daily booster limit reached", "ServerErrorCode287": "Can't help myself", "ServerErrorCode288": "Please register a game account first", "ServerErrorCode291": "You have used up this week's boosters", "ServerErrorCode292": "Only accounts created for more than %s days are qualified", "ServerErrorCode293": "Only deposited users can send boosters", "ServerErrorCode501": "Service is not available in your region.", "ServerErrorCode502": " Cannot passed advanced IP/GPS check.", "ServerErrorCode503": "Can't sit down, please try another table", "ServerErrorCode504": "Failed of Sit Out, this feature has reached today's usage limit", "ServerErrorCode505": "GAMEUUID does not exist", "ServerErrorCode506": "You will exit this game after the conclusion of this hand", "ServerErrorCode508": "Special invited players will be on the table", "ServerErrorCode509": "Do not support the narrator on the table", "ServerErrorCode512": "Re-enter the room in %s", "ServerErrorCode513": "Re-enter your seat in %s", "ServerErrorCode515": "Buy-in has to be more than the chips you exited with, please add chips and try again.", "ServerErrorCode1002": "This room is about to be disbanded", "ServerErrorCode1201": "You Will Leave This Room After This Hand.", "ServerErrorCode1204": "Exceeding Buyin Upper Limit of This Table, So Temporarily You Do Not Need To Buyin.", "ServerErrorCode1206": "Operation Failed, Please Try It Again", "ServerErrorCode1207": "Guess the hand repeat bet", "ServerErrorCode1208": "Guess hand bet timeout", "ServerErrorCode1209": "Insufficient balance, guessing hand bet failed", "ServerErrorCode1210": "Guess hand setting failed to vote", "ServerErrorCode1211": "Guess hand bet option is invalid", "ServerErrorCode1212": "Guess hand unknown error", "ServerErrorCode1213": "Service is not available in your region", "ServerErrorCode1216": "Exceeded <PERSON><PERSON><PERSON>.  Please try again later", "ServerErrorCode1249": "New exception-Vietnamese players cannot enter", "ServerErrorCode1250": "Table viewing limit has expired，Please try another table", "ServerErrorCode1251": "No matching room found", "ServerErrorCode1252": "Approved", "ServerErrorCode1254": "This seat is reserved and will be released 5 - 10 minutes before the game", "ServerErrorCode1255": "This seat is reserved, Star table will begin in %s", "ServerErrorCode1256": "Special guests occupy the seat, If after %s the special guests is not online, Will be released", "ServerErrorCode1257": "Gifting failed, player not a star member", "ServerErrorCode1258": "Incorrect gift ID or quantity", "ServerErrorCode1262": "Star player not seated, please send gifts later", "ServerErrorCode1263": "Cannot gift yourself", "ServerErrorCode1302": "Sorry, there is no suitable table at the level.", "ServerErrorCode31121": "Reserved seat, please choose another table.", "ServerErrorCode31123": "Game Server Is Under Maintenance", "pop_silence_title": "Responsible Gaming", "pop_silence_tips1": "Do you want to take a break?", "pop_silence_tips2": "It looks like you’ve been playing\nfor too long.", "pop_silence_tips3": "You are in breaktime", "pop_silence_btn_quit": "Exit", "pop_silence_btn_continue": "Continue", "CowboyExit_bg_exit_text": "Are you sure you would like to leave this table?", "InquireView_content_gold_coin": "GOLD COIN", "USDTView_title_label": "Conversion", "USDTView_exchange": "EXCHANGE", "USDTView_exchange_coin_label": "Exchange coins", "USDTView_exchange_usdt_label": "Exchange USD", "USDTView_from_label": "From", "USDTView_to_label": "To", "USDTView_coin_label": "Coins", "USDTView_input_num_label": "Quantity", "USDTView_exchange_num_label": "Exchange quantity", "USDTView_all_label": "All", "USDTView_usdt_coin_ex_label_0": "Exchange rate 1USD=%s Gold Coin", "USDTView_usdt_coin_ex_label_1": "Exchange rate 1 Gold Coin = %sUSD", "USDTView_exchange_label": "Exchange", "USDTView_explan_label": "The exchange limit is 20USD～100W USD \n As the exchange rate changes at any time, the displayed exchange rate is for reference only, and the exchange rate at the time of submission shall be used \n Cannot be cancelled during the redemption process", "USDTView_ex_coin_success_label": "Successfully exchanged %s Gold Coins", "USDTView_ex_usdt_success_label": "Successfully redeemed %sUSD", "USDTView_ex_coin_error_0_usdt": "USD account is 0 and cannot be converted into Gold Coins", "USDTView_usdt_balance_label": "USD balance:", "USDTView_txt_usdt": "USD", "USDTView_exchange_btn_label": "Exchange", "USDTView_bring_num_label": "Bring in points", "USDTView_input_invalid_num_label": "Please enter a valid value", "USDTView_usdt_chanage_1": "Use %sUSD to exchange %s Gold Coins", "USDTView_usdt_chanage_2": "Use %sUSD to exchange %s jackfruit points", "USDTView_usdt_change_free_tips": "Remaining free exchange: %s", "USDTView_usdt_change_fee_tips": "Service fee %s,actual USD redeemed %s USD", "USDTView_usdt_change_point_tips": "Use %s points for %s service fees", "USDTView_usdt_change_coin_tips": "Gold Coin Balance", "USDTView_usdt_change_usdt_tips": "USD Balance", "USDTView_usdt_change_title_tips": "Account <PERSON><PERSON>", "USDTView_exchange_tips_label": "Each account enjoys %s free exchanges everyday\nNew free exchanges are released at midnight everyday, with no roll-overs.\nThe interval between each transaction is %s minutes", "Humanboy_advancedSetting_desc": "You can choose five different denominations.", "Humanboy_advancedSetting_auto": "Autobet Settings", "Humanboy_advancedSetting_opt_normal": "Normal", "Humanboy_advancedSetting_opt_advance": "Advance", "Humanboy_advancedSetting_opt_advance_extra": "(Customize for duration of hands)", "Cowboy_fuhao_no_text": "Rich#%d", "Cowboy_shensuanzi_text": "Skillful", "CowBoy_btn_desc_auto_cancel": "Cancel", "CowBoy_btn_desc_auto_recharge": "Recharge", "CowBoy_btn_desc_auto_count": "%d hands", "CowBoy_btn_desc_auto_using_count": "%d/%d hands", "CowBoy_btn_desc_stop_auto_bet": "Terminate", "CowBoy_btn_desc_resume_auto_bet": "Cancel", "CowBoy_btn_desc_exit_game": "Leave", "CowBoy_btn_desc_resume_game": "Cancel", "CowBoy_btn_desc_switch_auto_bet": "Switch", "Cowboy_auto_bet_stop_tips": "Will you terminate autobet？\nYou still have %d/%d bets.", "Cowboy_auto_bet_switch_tips": "Autobet is active, Switching will terminate the remaining %d/%d bets.", "Cowboy_auto_bet_exit_tips": "Autobet is active, leaving will terminate the remaining %d/%d bets.", "Cowboy_last20_text": "Last 20 hands", "Humanboy_list_online": "Online players:", "Humanboy_list_change_rank": "Change List", "Humanboy_list_rank_0": "Most Won (Tdy)", "Humanboy_list_rank_1": "Repeat Wins (Tdy)", "Humanboy_list_rank_2": "One Hand (Tdy)", "Humanboy_list_rank_3": "Repeat Wins (All)", "Humanboy_list_rank_4": "One Hand (All)", "Humanboy_list_rank_5": "In One Day (All)", "Humanboy_list_profit": "Profit amount:", "Humanboy_list_frequency_time": "Times won:", "Humanboy_list_frequency": "%d", "Humanboy_list_myrank": "My Ranking", "Cowboy_ludan_guide_text": "Tap to view the hand history", "minigame_rebate_top_rank": "%s place", "minigame_rebate_top_rank_button": "You beat %s, ranked %s", "minigame_rebate_top_rank_tips": "%s Place", "minigame_rebate_no_rank": "Unlisted", "minigame_rebate_toast_top_tips": "Top %d%", "minigame_rebate_toast_top_no_enemy": "You have become the %s place", "minigame_rebate_period_desc": "%s～%s\nGet reward daily during event", "minigame_rebate_text_month": "/", "minigame_rebate_text_period": "%s %s to %s %s", "minigame_rebate_surpassed_reward": "Players not in the top %d will also receive an %s reward subsidy.", "minigame_rebate_daily_ranking": "Overall Ranking:", "minigame_rebate_daily_ranking_daily": "Daily Ranking:", "minigame_rebate_claim_note": "<color=#9CB8A3>Note: Please collect the rewards in time so that you won’t miss them after the event expires</color>", "minigame_rebate_rank_1_reward": "1st place bonus: %s", "minigame_rebate_rank_2_reward": "2nd place bonus: %s", "minigame_rebate_rank_3_reward": "3rd place bonus: %s", "minigame_rebate_rank_4_reward": "4th place bonus: %s", "minigame_rebate_rank_5_reward": "5th place bonus: %s", "minigame_rebate_rank_6_reward": "6th place bonus: %s", "minigame_rebate_rank_7_reward": "7th place bonus: %s", "minigame_rebate_rank_8_reward": "8th place bonus: %s", "minigame_rebate_rank_9_reward": "9th place bonus: %s", "minigame_rebate_rank_10_reward": "10th place bonus: %s", "minigame_rebate_reward_popup": "<color=#ffffff>Congratulation on winning</c> %s", "minigame_rebate_receive_reward_success": "Congratulations on getting %s", "minigame_number_date": "st nd rd th", "minigame_number_month": "January February March April May June July August September October November December", "minigame_total_betting": "Already Bet: ", "minigame_cowboy_rebate_today": "Today", "minigame_cowboy_rebate_day": "Sun Mon Tues Wed Thurs Fr<PERSON>", "minigame_cowboy_rebate_expired": "Expired", "minigame_cowboy_rebate_title": "Limited Time Challenge", "minigame_cowboy_rebate_content": "For a limited time, bet on <color=#ECD27D>Texas Cowboy</color> to <color=#ECD27D>gain reward</color>. If you meet the required amount.", "minigame_cowboy_rebate_title_activity_2": "Limited Time Reward", "minigame_cowboy_rebate_content_activity_2": "During the event period, receive <color=#ECD27D>full rewards daily</color> when bet %s gold coins in <color=#ECD27D>Texas Cowboys</color>.\n<color=#9CB8A3>Note: Please collect the rewards in time in case you can’t get them after the event expires.</color>", "minigame_cowboy_rebate_content_activity_2_one_game": "During the event period, receive <color=#ECD27D>full rewards daily</color> when bet %s gold coins in <color=#ECD27D>Texas Cowboys</color>.\n<color=#9CB8A3>Note: Please collect the rewards in time in case you can’t get them after the event expires.</color>", "minigame_cowboy_rebate_title_activity_3": "Challenge Day", "minigame_cowboy_rebate_content_activity_3": "在规定时间及日期内每天，在<color=#EBFF00>德州牛仔, 扑克大师或百人德州</c>中投注满XXX金币即可获得当天全部奖励。", "minigame_cowboy_rebate_title_activity_4": "Total Bet Ranking", "minigame_cowboy_rebate_title_activity_4_daily": "Limited Time Ranking", "minigame_cowboy_rebate_content_activity_4": "Limited time to place bets in the <color=#ECD27D>Texas Cowboy</color> mini-game. The top %d players will receive a fixed reward. %s Winners, please return to this event interface to claim your prize.", "minigame_cowboy_rebate_content_activity_4_daily": "From %s, there will be a limited time every day betting in the <color=#ECD27D>Texas Cowboy</color> mini-game. The top %d players will receive a fixed reward. %s Winners, please return to this event interface to claim your prize.", "minigame_already_bet": "Placed Bet", "day": "d", "hour": "hr", "minute": "min", "seconds": "sec", "CountDown": "Remaining time of event:", "Claimed": "Claimed", "Unclaimed": "Unclaimed", "string_comma": ", ", "string_and": "and", "minigame_currency_type_2": "gold coins", "minigame_currency_type_3": "casino coins", "Cowboy_coin_short_text": "W", "Humanboy_game_gold_short_suffix_w": "W", "Humanboy_game_gold_short_suffix_million": "<PERSON>l", "Humanboy_game_gold_short_suffix_billion": "Bil", "PokerMaster_dialog_recharge": "Go to the wallet to recharge", "minigame_prompt_coin_remain": "Remaining", "minigame_prompt_coin_used": "Used", "minigame_prompt_casino_coin": "Casino Coins:", "MiniGames_SYSTEM_FORCE_CLOSED": "Room closed by system", "minigame_Title_10": "COWBOY", "minigame_Title_30": "BEAT THE BANKER", "minigame_Title_50": "LIVE STREAM", "minigame_Title_70": "MASTER", "Rebate_betting_bonus": "Bet Bonus", "Rebate_all_claimed": "All Claimed.", "betting_rebate_event_title": "Betting Rebate Event", "bet_bonus_tab": "Bet Bonus", "leaderboard_tab": "Leaderboard", "total_bet": "Total Bet:", "next_level_bonus": "Next level bouns, get more:", "collected": "Collected:", "Rebate_bet_bnous_countdown": "Ends in", "Rebate_leaderboard_countdown": "Countdown to the final:", "Rebate_has_end_bet_bonus": "Event Ended", "Rebate_has_end_leaderboard": "Ends at %s", "Rebate_start_in": "Start Time: %s", "Rebate_leaderboard_no_ranking_yet": "No Rankings Yet", "Rebate_claim_reward_success": "You've successfully claimed %s! Check [My Wallet]", "Rebate_rank": "Rank", "Rebate_player": "Player", "Rebate_reward": "<PERSON><PERSON>", "Rebate_bet_amount": "Real-time bet amount", "Rebate_not_rank": "Not Rank", "Rebate_my_self": "I am my self"}}}}