<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>cacel_btn.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1865},{239,108}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{239,108}}</string>
                <key>sourceSize</key>
                <string>{239,108}</string>
            </dict>
            <key>music.png</key>
            <dict>
                <key>frame</key>
                <string>{{87,1975},{61,79}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{61,79}}</string>
                <key>sourceSize</key>
                <string>{61,79}</string>
            </dict>
            <key>ok_btn.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1624},{239,108}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{239,108}}</string>
                <key>sourceSize</key>
                <string>{239,108}</string>
            </dict>
            <key>sound.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1975},{83,65}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{83,65}}</string>
                <key>sourceSize</key>
                <string>{83,65}</string>
            </dict>
            <key>sound_close.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1366},{256,115}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{256,115}}</string>
                <key>sourceSize</key>
                <string>{256,115}</string>
            </dict>
            <key>sound_open.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1108},{256,115}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{256,115}}</string>
                <key>sourceSize</key>
                <string>{256,115}</string>
            </dict>
            <key>tips_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,558},{971,548}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{971,548}}</string>
                <key>sourceSize</key>
                <string>{971,548}</string>
            </dict>
            <key>tips_bg2.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{977,554}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{977,554}}</string>
                <key>sourceSize</key>
                <string>{977,554}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>setting_dznz.png</string>
            <key>size</key>
            <string>{1024,2048}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:6a4d56cc665e4bc2e2c9d4018b6753dd$</string>
            <key>textureFileName</key>
            <string>setting_dznz.png</string>
        </dict>
    </dict>
</plist>
