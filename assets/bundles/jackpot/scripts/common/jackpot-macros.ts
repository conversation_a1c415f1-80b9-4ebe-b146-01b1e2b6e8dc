export namespace macros {
    export const BUNDLE_NAME = 'jackpot';

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/jackpot-assets-zh_cn',
        EN_US = 'configs/jackpot-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        HI_IN = 'languages/string-hi_in',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph'
    }

    export enum Assets {
        JACK_ACTION = 'jackpot.jackpot-action',
        BIGWIN_MARS = 'jackpot.bigWin-mars'
    }

    export enum Audio {
        BUTTON_CLICK = 'common-audio.button-click',
        Tab = 'common-audio.tab'
    }
}
