import * as pf from 'pf';
import { registerEntry } from 'pf';
import { macros } from './common/jackpot-macros';
import { JackpotService } from './domain/jackpot-service';
import BUNDLE_NAME = macros.BUNDLE_NAME;

@registerEntry(BUNDLE_NAME)
export class JackpotEntry extends pf.BundleEntryBase {
    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_LOBBY;
    }

    protected getLanguageStringPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Language_String_Path.ZH_CN;
            case pf.LANGUAGE_GROUPS.yn_TH:
                return macros.Language_String_Path.YN_TH;
            case pf.LANGUAGE_GROUPS.th_PH:
                return macros.Language_String_Path.TH_PH;
            case pf.LANGUAGE_GROUPS.hi_IN:
                return macros.Language_String_Path.HI_IN;
            default:
                return macros.Language_String_Path.EN_US;
        }
    }

    protected getAddressableConfigPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Addressable_Config_Path.ZH_CN;
            default:
                return macros.Addressable_Config_Path.EN_US;
        }
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onEnter`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        // pf.serviceManager.register(new JackpotService(context.socket));

        pf.serviceManager.register(new JackpotService(context.socket));

        await this.loadConfigs();

        // download assets
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask(BUNDLE_NAME);
        await assetLoader.start();

        return Promise.resolve();
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onExit`);
        pf.serviceManager.unregister(JackpotService);
        pf.bundleManager.releaseAll(this.bundle);
    }

    onUnload(): void {
        cc.log(`bundle ${BUNDLE_NAME} onUnload`);
    }
}
