{"groups": {"hi_IN": {"strings": {"Common_GC": "GC", "Common_SC": "SC", "Common_Cancel": "<PERSON><PERSON><PERSON>", "Common_Confirm": "confirmar", "Common_Exit": "Salir", "Common_HighCard": "Carta alta", "Common_OnePair": "Par", "Common_TwoPair": "<PERSON>s pares", "Common_Trips": "Tercia", "Common_Straight": "Escalera", "Common_Flush": "Color", "Common_FullHouse": "Full", "Common_Quads": "Quads", "Common_StraightFlush": "Escalera de color", "Common_RoyalFlush": "Escalera real", "ServerErrorCode_1": "OK", "ServerErrorCode_2": "New version available, please update first", "ServerErrorCode_3": "Unable to find the player", "ServerErrorCode_4": "Logged in from another device. Please log in again.", "ServerErrorCode_5": "Unable to inquire token, please contact our customer service", "ServerErrorCode_6": "Unable to obtain data from global server", "ServerErrorCode_7": "Internal RPC error", "ServerErrorCode_8": "Internal RPC return value error", "ServerErrorCode_17": "Unable to create more room", "ServerErrorCode_18": "Created too many rooms", "ServerErrorCode_19": "Invalid parameters", "ServerErrorCode_20": "Unable to pay, please recharge", "ServerErrorCode_21": "Validation failure occurs", "ServerErrorCode_22": "The room has been dismissed", "ServerErrorCode_23": "Only the owner can dismiss the room", "ServerErrorCode_24": "Room full", "ServerErrorCode_25": "You are already in the room", "ServerErrorCode_26": "The player is not in the room", "ServerErrorCode_27": "The position has been taken", "ServerErrorCode_28": "Coins required if you want to sit down", "ServerErrorCode_29": "Table full", "ServerErrorCode_30": "Player seated", "ServerErrorCode_31": "Unable to sit during game", "ServerErrorCode_32": "Not enough coins", "ServerErrorCode_33": "Unable to sit randomly", "ServerErrorCode_34": "Unable to sit randomly2", "ServerErrorCode_35": "Position occupied", "ServerErrorCode_36": "Unable to be seated", "ServerErrorCode_37": "Unable to be seated2", "ServerErrorCode_38": "Unable to stand up and spectate", "ServerErrorCode_39": "The number of gold coins has reached the Maximum Limit.", "ServerErrorCode_39_usdt": "The number of USD has reached the Maximum Limit.", "ServerErrorCode_40": "Unable to multi Buy In", "ServerErrorCode_41": "Only the owner can respond", "ServerErrorCode_42": "Unable to pay the recording fee,please recharge", "ServerErrorCode_43": "Buy-in application timeout", "ServerErrorCode_44": "Invalid buy-in amount approval", "ServerErrorCode_45": "Only the owner can start the game", "ServerErrorCode_46": "Game is already started.", "ServerErrorCode_47": "Not enough players to start the game.", "ServerErrorCode_48": "Not your turn yet", "ServerErrorCode_49": "Incorrect bet amount", "ServerErrorCode_50": "Illegal action", "ServerErrorCode_51": "Not your turn yet", "ServerErrorCode_52": "Configuration file error", "ServerErrorCode_53": "Insufficient funds, please recharge", "ServerErrorCode_54": "Only seated player can chat with owner", "ServerErrorCode_55": "Invalid player ID to buy insurance", "ServerErrorCode_56": "Insurance purchase request time out", "ServerErrorCode_57": "Unable to buy insurance in the current game", "ServerErrorCode_58": "Insurance purchased", "ServerErrorCode_59": "Unable to find pot ID", "ServerErrorCode_60": "Exceed available OUTS amount", "ServerErrorCode_61": "Purchase amount exceeds 1/3 amount of pot", "ServerErrorCode_62": "the pot is not enough to purchase", "ServerErrorCode_63": "Invalid Outs purchase", "ServerErrorCode_64": "Invalid Outs purchase2", "ServerErrorCode_65": "Action only available to seated players", "ServerErrorCode_66": "It is time to leave, your position will be preserved", "ServerErrorCode_67": "Application sent, waiting for the owner to approve. Expire in 180 seconds", "ServerErrorCode_68": "Not in seat now", "ServerErrorCode_69": "Already left the table while preserving your position", "ServerErrorCode_70": "You are not in the status of leaving while your position preserved", "ServerErrorCode_71": "Invalid player ID", "ServerErrorCode_72": "Unable to raise now, you can All in or Call", "ServerErrorCode_73": "Unable to connect to world server", "ServerErrorCode_74": "Only club administrators can create club rooms", "ServerErrorCode_75": "The amount of room created has reached the ceiling", "ServerErrorCode_76": "Other errors occur while creating rooms", "ServerErrorCode_77": "Illegal buy-in amount", "ServerErrorCode_78": "Last player", "ServerErrorCode_79": "Insurance need to be brought back", "ServerErrorCode_80": "Owner not found", "ServerErrorCode_81": "Incorrect Outs amount", "ServerErrorCode_82": "Invalid purchase amount", "ServerErrorCode_83": "Insurance is required", "ServerErrorCode_84": "Incorrect agreement", "ServerErrorCode_85": "Unable to check the rest of the cards", "ServerErrorCode_86": "Unable to check the rest of the cards", "ServerErrorCode_87": "Administrator Only", "ServerErrorCode_88": "Game not started", "ServerErrorCode_89": "The player is in the blacklist to sitdown", "ServerErrorCode_90": "Owner refused you to sit down ", "ServerErrorCode_91": "The player is not in the blacklist to sitdown", "ServerErrorCode_92": "Current game not started yet.", "ServerErrorCode_93": "Too much extra time used", "ServerErrorCode_94": "Unable to obtain alliance club", "ServerErrorCode_95": "Game in process", "ServerErrorCode_96": "Unable to withdraw coins", "ServerErrorCode_97": "You are prohibited from sitting down by anti-gang-cheating system", "ServerErrorCode_98": "Not enough gems to buy in", "ServerErrorCode_99": "Club buy-in amount over the limit of the alliance", "ServerErrorCode_100": "Club buy-in prohibited by alliance", "ServerErrorCode_101": "Forced showdown prohibited for now", "ServerErrorCode_102": "Forced ShowDown Count Has Reached Maximum Limit", "ServerErrorCode_103": "the number of times has been used up", "ServerErrorCode_104": "Game Server Is Under Maintenance", "ServerErrorCode_105": "Because Your Account Has Been Settled From This Table", "ServerErrorCode_106": "You Have Launched <PERSON><PERSON> Account, You Are About To Leave The Table After This Round.", "ServerErrorCode_107": "Current game is settled, please do not re-submit request", "ServerErrorCode_108": "You Have Not Buyin Current Game, No Need To Settle Account.", "ServerErrorCode_109": "You Cannot Set Both Password At The Same Time", "ServerErrorCode_110": "Incorrect Password", "ServerErrorCode_111": "Player limit has been reached", "ServerErrorCode_113": "Player all in,  all mute...", "ServerErrorCode_117": "Unable to create club", "ServerErrorCode_118": "Unable to create more club", "ServerErrorCode_119": "Parameter error occurs", "ServerErrorCode_120": "Incorrect type of club", "ServerErrorCode_121": "Unable to find club ID", "ServerErrorCode_122": "Only administrator can dismiss the club", "ServerErrorCode_123": "Club full, unable to join in", "ServerErrorCode_124": "You are already in the club", "ServerErrorCode_125": "You have already applied to join this club", "ServerErrorCode_126": "The player is not in the club", "ServerErrorCode_127": "Club fund not cleared yet", "ServerErrorCode_128": "Club member not cleared yet", "ServerErrorCode_129": "Club administrator full", "ServerErrorCode_130": "Not enough gems, please recharge", "ServerErrorCode_131": "Useless club star ranking", "ServerErrorCode_132": "Unable to obtain club price", "ServerErrorCode_133": "Unable to purchase star club", "ServerErrorCode_134": "Community not found", "ServerErrorCode_135": "Authorization failure!", "ServerErrorCode_136": "Error", "ServerErrorCode_137": "Club name already exists", "ServerErrorCode_138": "Other administrators are already on the move", "ServerErrorCode_139": "Other administrators are already on the move", "ServerErrorCode_149": "Unable to create more alliance", "ServerErrorCode_150": "Alliance name exists", "ServerErrorCode_151": "Unable to create alliance", "ServerErrorCode_152": "Alliance member not cleared yet", "ServerErrorCode_153": "Fail to find alliance", "ServerErrorCode_154": "Alliance permission denied", "ServerErrorCode_155": "The club is not in the alliance", "ServerErrorCode_156": "Unable to find the alliance", "ServerErrorCode_157": "The club is already in the alliance", "ServerErrorCode_158": "Application sent", "ServerErrorCode_159": "Alliance full", "ServerErrorCode_160": "Other administrators are already on the move", "ServerErrorCode_161": "Other error occurs", "ServerErrorCode_162": "Server <PERSON><PERSON>rse failure", "ServerErrorCode_163": "Database failed to save", "ServerErrorCode_164": "Unable to join more alliance", "ServerErrorCode_165": "Unable to set club JackPot bonus rate", "ServerErrorCode_166": "Unable to obtain club JackPot bonus rate", "ServerErrorCode_167": "Game in process, unable to remove club from alliance", "ServerErrorCode_168": "Game in process, unable to set <PERSON><PERSON>ot", "ServerErrorCode_169": "Unable to dismiss the club when game is on", "ServerErrorCode_170": "Please dismiss the previous alliance created", "ServerErrorCode_171": "Please dismiss the previous alliance created", "ServerErrorCode_172": "Not enough coins", "ServerErrorCode_173": "Fail To Obtain Player's System Mail Time Stamp", "ServerErrorCode_174": "Fail To Obtain Player's Mail List", "ServerErrorCode_175": "Request Index Is Illegal", "ServerErrorCode_176": "Join the club to reach the Maximum Limit", "ServerErrorCode_177": "Unable to dismiss the club when game is on", "ServerErrorCode_179": "Fail To Obtain Player's Anounce List", "ServerErrorCode_180": "Mail Content Is Not Qualified.", "ServerErrorCode_181": "Please Quit Previously Joined Union", "ServerErrorCode_182": "Club Member Is Not Found", "ServerErrorCode_187": "Because There Is Union Game Undergoing, Club Application For Quit Union Is Prohibited", "ServerErrorCode_190": "Mobile Or Area Code Should Not Be Empty", "ServerErrorCode_191": "Incorrect Mobile Format", "ServerErrorCode_192": "Incorrect EMail Address", "ServerErrorCode_193": "You Cannot Quit Club Because Jackpot Is Not Empty", "ServerErrorCode_194": "Club Cannot Be Disband Because Club Balance Is Negtive", "ServerErrorCode_195": "Fail To Set Personal Percent,Permission Required", "ServerErrorCode_196": "Fail To Set Personal Percent", "ServerErrorCode_197": "Your Account Has Been Banned，If You Have Any Questions, Please Contact Customer Service", "ServerErrorCode_198": "Fail To Set Personal Percent, Please Do Not Repeat Setting", "ServerErrorCode_199": "Setting Remarks Have Reach Maximum Limit", "ServerErrorCode_200": "Are you sure to retrieve account password?", "ServerErrorCode_201": "Please switch to login with game ID ,%sIf you forget your password, please contact with customer service。", "ServerErrorCode_205": "Your safe has insufficient coins", "ServerErrorCode_207": "Cannot Find User Record.", "ServerErrorCode_208": "Cannot Find Lottery Winning Record.", "ServerErrorCode_209": "Incorrect Secondary Password", "ServerErrorCode_210": "Drawing Lottery Failed", "ServerErrorCode_211": "Data Request Failed", "ServerErrorCode_212": "RedBag Event Has Been Closed", "ServerErrorCode_213": "RedBag Level Has Not Been Set", "ServerErrorCode_214": "RedBag Amount Is Not In Allowed Amount Range", "ServerErrorCode_215": "Gold Operation Failed", "ServerErrorCode_216": "Sending RedBag Failed", "ServerErrorCode_217": "All Red Bags Have Been Drawed", "ServerErrorCode_219": "RedBag Does Not Exist", "ServerErrorCode_221": "RedBag Has Expired", "ServerErrorCode_222": "Grabbing RedBag Too Often", "ServerErrorCode_223": "Failed to fetch the rank.", "ServerErrorCode_224": "Your network is unstable, please login again.", "ServerErrorCode_225": "System error", "ServerErrorCode_226": "In system maintenance, please log in again.", "ServerErrorCode_228": "Secondary Password not set.", "ServerErrorCode_229": "Please log in again", "ServerErrorCode_230": "Application Cancelled", "ServerErrorCode_232": "The community has changed this month", "ServerErrorCode_233": "Community creation is currently not allowed", "ServerErrorCode_234": "Current player cannot join other communities", "ServerErrorCode_235": "Current community players cannot join other communities", "ServerErrorCode_238": "Unable to modify the first community, please contact customer service", "ServerErrorCode_251": "Insufficient amount of USD", "ServerErrorCode_252": "The exchange is abnormal, please try again later", "ServerErrorCode_253": "The exchange limit is 20USD～100W USD", "ServerErrorCode_254": "Your safe has insufficient USD", "ServerErrorCode_255": "The room opens on time", "ServerErrorCode_256": "The room has been dismissed", "ServerErrorCode_257": "Please wait for %s minutes before the next (gold coins to USD) exchange", "ServerErrorCode_280": "Booster does not exist", "ServerErrorCode_281": "Booster expired", "ServerErrorCode_282": "Maximum booster received", "ServerErrorCode_283": "Already received a booster from the player", "ServerErrorCode_284": "Not enough boosters to open red packet", "ServerErrorCode_285": "Invalid Recipient", "ServerErrorCode_286": "Daily booster limit reached", "ServerErrorCode_287": "Can't help myself", "ServerErrorCode_288": "Please register a game account first", "ServerErrorCode_291": "You have used up this week's boosters", "ServerErrorCode_292": "Only accounts created for more than %s days are qualified", "ServerErrorCode_293": "Only deposited users can send boosters", "ServerErrorCode_501": "Service is not available in your region.", "ServerErrorCode_502": " Cannot passed advanced IP/GPS check.", "ServerErrorCode_503": "Can't sit down, please try another table", "ServerErrorCode_504": "Failed of Sit Out, this feature has reached today's usage limit", "ServerErrorCode_505": "GAMEUUID does not exist", "ServerErrorCode_506": "You will exit this game after the conclusion of this hand", "ServerErrorCode_508": "Special invited players will be on the table", "ServerErrorCode_509": "Do not support the narrator on the table", "ServerErrorCode_511": "Unable to join without location service on", "ServerErrorCode_512": "Re-enter the room in %s", "ServerErrorCode_513": "Re-enter your seat in %s", "ServerErrorCode_515": "Buy-in has to be more than the chips you exited with, please add chips and try again.", "ServerErrorCode_1002": "This room is about to be disbanded", "ServerErrorCode_1201": "You Will Leave This Room After This Hand.", "ServerErrorCode_1203": "You Will Leave This Room After This Hand.", "ServerErrorCode_1204": "Exceeding Buyin Upper Limit of This Table, So Temporarily You Do Not Need To Buyin.", "ServerErrorCode_1206": "Operation Failed, Please Try It Again", "ServerErrorCode_1207": "Guess the hand repeat bet", "ServerErrorCode_1208": "Guess hand bet timeout", "ServerErrorCode_1209": "Insufficient balance, guessing hand bet failed", "ServerErrorCode_1210": "Guess hand setting failed to vote", "ServerErrorCode_1211": "Guess hand bet option is invalid", "ServerErrorCode_1212": "Guess hand unknown error", "ServerErrorCode_1213": "Service is not available in your region", "ServerErrorCode_1216": "Exceeded <PERSON><PERSON><PERSON>.  Please try again later", "ServerErrorCode_1249": "New exception-Vietnamese players cannot enter", "ServerErrorCode_1250": "Table viewing limit has expired，Please try another table", "ServerErrorCode_1251": "No matching room found", "ServerErrorCode_1252": "Approved", "ServerErrorCode_1254": "This seat is reserved and will be released 5 - 10 minutes before the game", "ServerErrorCode_1255": "This seat is reserved, Star table will begin in %s", "ServerErrorCode_1256": "Special guests occupy the seat, If after %s the special guests is not online, Will be released", "ServerErrorCode_1257": "Gifting failed, player not a star member", "ServerErrorCode_1258": "Incorrect gift ID or quantity", "ServerErrorCode_1262": "Star player not seated, please send gifts later", "ServerErrorCode_1263": "Cannot gift yourself", "ServerErrorCode_1275": "Administrator Only", "ServerErrorCode_1302": "Sorry, there is no suitable table at the level.", "ServerErrorCode_31121": "Reserved seat, please choose another table.", "ServerErrorCode_31123": "Game Server Is Under Maintenance", "Common_Hour": "hr(s)", "Common_Minute": "min(s)", "Common_Seconds": "s", "Common_Comma": ", ", "Common_And": "and", "Common_LoadingResource": "Loading resources", "Common_LoadingLobby": "Loading game lobby", "ServerTimeOut_Title": "Still playing?", "ServerTimeOut_Content": "Your connection to the game server has timed out or encountered an issue. If you’d like to keep playing, please try to reconnect.", "ServerTimeOut_Confirm": "RECONNECT", "ServerTimeOut_Cancel": "EXIT GAME", "ConnecServerFailed_Title": "Server under maintenance", "ConnecServerFailed_Content": "We will redirect you to maintenance page.", "ConnectionError_Title": "Connection issue detected", "ConnectionError_Content": "It seems you've been disconnected several times in a short period, possibly due to an unstable internet connection. You can try reconnecting to continue playing, or you may choose to exit the game.", "ConnectionError_Confirm": "TRY AGAIN", "ConnectionError_Cancel": "EXIT GAME", "MiniGame_RebateTopRank": "%s place", "MiniGame_RebateNoRank": "Unlisted", "MiniGame_RebateToastTopTips": "Top %d%", "Recaptcha_ResultSuccess": "You have successfully authenticated", "Recaptcha_ResultFaild": "Authentication failed", "Recaptcha_CurrentResultFaild": "Authentication failed, Please verify again later", "Recaptcha_ResultForbid": "After %d seconds, real person verification can be performed again", "Review_Tip_NoReviewAvaliable": "No review available currently！", "Review_Tip_UnAvailable": "Replay function unavailable in history review", "Audit_NeedRecharge": "This Report Would Cost %d Coin, Your Coin Is Not Enough, Do You Want To Recharge Coin?", "Audit_ConfirmDialog": "This Report Would Cost %d Coin, Are You Sure You Want To Report?", "Audit_CoinValue": "%d Coin", "Audit_ExpenseFormat": "<color=#86899E>Review free for this time: </c><color=#3482F5>%s</color>", "Audit_FirstTimeFree": "Free For The First Time", "Audit_FreeTimeLeft": "%d Free Times Left", "Audit_NoPermission": "You Cannot Report Without Report Permission", "Audit_MustTwoPersons": "Fair Play Report Requires At Least Two Suspects.", "Audit_EmailTips": "Incorrect email address, please enter again.", "Audit_Success": "Report success! Review department would response within 48 hours, if you have any question, please contact fairplay customer service.", "game_dzpoker_peek_card_title": "Open", "lobby_security_guard_title": "Security Guard", "lobby_security_guard_banned_list_title": "Ban List", "lobby_security_guard_desc": "7X24 Real-time Protection Activated", "lobby_security_guard_total_player_get_banned": "Total banned", "lobby_security_guard_number_player_get_banned_30_days": "Banned in the last 30 days", "lobby_security_guard_number_player_get_banned_3_days": "Banned in the last 3 days", "lobby_security_guard_decs_tracker_detection": "No Trackers", "lobby_security_guard_decs_bot_detection": "Bot Detection", "lobby_security_guard_decs_collusion_detection": "Collusion Detection", "lobby_security_guard_decs_tool_detection": "Tool Detection", "lobby_security_guard_player_ban_is_related": "Related", "lobby_security_guard_player_ban_frozen": "Frozen", "lobby_security_guard_player_ban_reason_bot": "<PERSON><PERSON>", "lobby_security_guard_player_ban_reason_collusion": "Collusion", "lobby_security_guard_player_ban_reason_tool": "Tools", "lobby_security_guard_player_ban_list_empty": "No banned user", "lobby_security_guard_player_ban_list_30_days_label": "Only past 30 days’ ban records shown", "lobby_security_guard_player_ban_detail": "Details", "Faceview_danmu_text_0": "Man, such a bad beat!", "Faceview_danmu_text_1": "That bad beat was brutal", "Faceview_danmu_text_2": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> is the devil", "Faceview_danmu_text_3": "Nice fold. I had nuts there", "Faceview_danmu_text_4": "You played way too slow, buddy.", "Faceview_danmu_text_5": "That was impossible", "Faceview_danmu_text_6": "That was a textbook play", "Faceview_danmu_text_7": "Don't even think about it, I'm a calling station", "Faceview_danmu_text_8": "Wow, your game is next level", "Faceview_danmu_text_9": "Let's see a cheap flop, what do you say?", "Faceview_danmu_text_10": "Just bluffing my way through", "Faceview_danmu_text_11": "Whew, bluffing ain't easy!", "Faceview_danmu_text_12": "Don't raise me, i will all in", "Faceview_danmu_text_13": "I folded a great hand, you've got the nuts.", "Faceview_danmu_text_14": "Hey, all in if you are bluffing", "Faceview_danmu_text_15": "Insane luck!", "Faceview_danmu_text_16": "Good game, let’s be friends", "Faceview_danmu_text_17": "Alright, until next time!", "Faceview_danmu_text_18": "Family pot! Let's see who gets lucky", "Faceview_danmu_text_19": "This one's on me, you are welcome", "Faceview_danmu_text_20": "You are playing like a pro", "Faceview_danmu_text_21": "Not gonna lie, you totally got me tilted", "Faceview_danmu_text_22": "Really? You checked to this?", "Faceview_danmu_text_23": "Ok that was pure luck", "Faceview_danmu_cd_tips": "Resend after %d seconds", "Faceview_danmu_button_danmu": "Split screen", "Faceview_danmu_button_face": "<PERSON><PERSON><PERSON>", "Faceview_danmu_button_emoji": "Magic Emoji", "Faceview_danmu_button_on": "Barrage feature is On", "Faceview_danmu_button_off": "Bullet comment disabled", "Faceview_danmu_button_onOff_Tips": "Please turn on the barrage function in the settings first", "System_Name": "System Announcement"}}}}