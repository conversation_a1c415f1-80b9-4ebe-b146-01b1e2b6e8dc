import * as common from 'common';
import type * as pf from 'pf';

export class HandMapCacheManagerControl {
    private _mHandMapCache: { [key: string]: pf.data.PokerHandData } = null;
    constructor() {
        this._mHandMapCache = null;
        HandMapCacheManagerControl.isLock = false;
    }

    private static handMapCacheKey: string = `PKW_92_GameReviewHandMapCache`;
    private static isLock = false;

    private get _lock(): boolean {
        return HandMapCacheManagerControl.isLock;
    }

    private get _handMapCacheKey(): string {
        return HandMapCacheManagerControl.handMapCacheKey;
    }

    clearHandMapCache() {
        this._mHandMapCache = null;
        common.CommonUtil.RemoveStringByCCFile(this._handMapCacheKey);
    }

    loadCache() {
        this._mHandMapCache = {};
        const storedHashMap = common.CommonUtil.GetStringByCCFile(this._handMapCacheKey);
        if (storedHashMap) {
            this._mHandMapCache = JSON.parse(storedHashMap);
        }
    }

    has(key: string): boolean {
        return this._mHandMapCache.hasOwnProperty(key);
    }

    getHand(key: string): any {
        return this._mHandMapCache[key];
    }

    addHand(key: string, value: any) {
        this._mHandMapCache[key] = value;
    }

    saveCache() {
        if (this._lock) return;
        localStorage.setItem(this._handMapCacheKey, JSON.stringify(this._mHandMapCache));
    }

    static clearAllCache() {
        localStorage.removeItem(HandMapCacheManagerControl.handMapCacheKey);
        this.isLock = true;
    }

    removeItem(key: string) {
        delete this._mHandMapCache[key];
    }
}
