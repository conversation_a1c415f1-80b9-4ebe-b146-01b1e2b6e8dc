import * as pf from 'pf';
import { macros } from '../common/common-macros';

const { ccclass, property } = cc._decorator;

let avatarTextures: any = {};

@ccclass
export class AvatarControl extends cc.Component {
    @property(cc.Sprite)
    head: cc.Sprite = null;

    _url: string = '';
    private _domainService: pf.services.DomainService;

    start() {}

    loadRemoteHeadImage(url: string, onComplete?: () => void) {
        if (avatarTextures[url]) {
            this.initTexture(url, avatarTextures[url]);
            if (onComplete) {
                onComplete();
            }
        } else {
            cc.assetManager.loadRemote(url, (err, texture: cc.Texture2D) => {
                if (err) {
                    cc.warn(`fail to load avatar image ${url}. ${err}`);
                } else if (this && cc.isValid(this.head)) {
                    avatarTextures[url] = texture;
                    this.initTexture(url, texture);
                }

                if (onComplete) {
                    onComplete();
                }
            });
        }
    }

    initTexture(url: string, texture: cc.Texture2D) {
        this.head.spriteFrame = new cc.SpriteFrame(texture);
        this._url = url;
    }

    loadDefaultHeadImage(num: number, onComplete?: () => void) {
        let name = num;
        if (num < 1 || num > macros.AVATAR_MAX_NUMBER) {
            name = 1;
        }

        // await pf.bundleManager.loadBundle(macros.BUNDLE_NAME);
        const bundle = pf.bundleManager.getBundle(macros.BUNDLE_NAME);
        const fullPath = macros.AVATAR_BASE_PATH + name;
        bundle.load(fullPath, cc.SpriteFrame, (err, asset: cc.SpriteFrame) => {
            if (err) {
                cc.warn(`fail to load default head image ${fullPath}. ${err}`);
            } else {
                this.head.spriteFrame = asset;
                this._url = fullPath;
            }

            if (onComplete) {
                onComplete();
            }
        });
    }

    loadHeadImage(url: string, plat: number, onComplete?: () => void) {
        const imageName = url.slice(url.lastIndexOf('/') + 1);
        if (pf.ValidationUtil.isNumber(imageName)) {
            this.loadDefaultHeadImage(Number(imageName), onComplete);
        } else {
            // 暂时只有系统头像，旧账号如果是remote头像，则设置为默认头像
            // this.loadDefaultHeadImage(1, onComplete);

            if (!this._domainService) {
                this._domainService = pf.serviceManager.get(pf.services.DomainService);
            }
            const urlWithDomain = this._domainService.getAvatarUrl(url, plat);
            if (urlWithDomain.includes('http')) {
                this.loadRemoteHeadImage(urlWithDomain, onComplete);
            } else {
                // this.loadRemoteHeadImage(this._domainService.getAvatarUrl(url, plat), onComplete);
                this.loadDefaultHeadImage(1, onComplete);
            }
        }
    }
}
