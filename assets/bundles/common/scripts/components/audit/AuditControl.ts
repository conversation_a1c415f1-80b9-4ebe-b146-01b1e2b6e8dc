import * as pf from 'pf';
import * as common from 'common';
import PokerHandData = pf.data.PokerHandData;
import PlayerRecord = pf.data.PlayerRecord;
import { macros } from '../../common/common-macros';
import { AuditPlayerItem } from './AuditPlayerItem';
import {
    type AlertDialogOptions,
    type BannerPopupOptions,
    type ToastPopupOptions,
    PopupDialogType,
    PopupManager
} from '../common-components-index';

const { ccclass, property } = cc._decorator;

@ccclass
export class AuditControl extends cc.Component {
    @property(cc.Button) sureBtn: cc.Button = null;
    @property(cc.Button) cancelBtn: cc.Button = null;
    @property(cc.RichText) expenseTxt: cc.RichText = null;
    @property(cc.Label) pleaseChooseTxt: cc.Label = null;
    @property(cc.Label) titleTxt: cc.Label = null;
    @property(cc.EditBox) emailEditBox: cc.EditBox = null;
    @property(cc.Node) listContent: cc.Node = null;
    obItemList: cc.Node[] = [];
    @property(cc.Prefab) obItemPrefab: cc.Prefab = null;

    _handData: PokerHandData = null;
    _first: number = 0;
    _goldEnough: number = 0;
    _chargefFee: number = 0;
    _freeCounts: number = 0;
    auditGameuuid: string = '';

    show(): void {
        this.node.active = true;
        this.updateView();
        this.updateAuditMoney();
    }

    hide() {
        this.node.destroy();
    }

    onLoad() {
        // let nodeStatusListener = this.node.addComponent(NodeStatusListener);
        // nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);
        this.sureBtn.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                this.onClickSureBtn();
            },
            this
        ); // 提交审核
        this.cancelBtn.node.on(
            'click',
            (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
                this.cancelAudit();
            },
            this
        ); // 我再想想
    }

    setData(handData: PokerHandData, resp: pf.client.IResponseQuerySendFairReport): void {
        this._handData = handData;
        this._first = resp.isfirst;
        this._chargefFee = resp.chargefee;
        this._freeCounts = resp.freecounts;
        this._goldEnough = resp.isgoldenough;
    }

    private _getSuspectUids() {
        let suspectUids: number[] = [];
        for (const ob of this.obItemList) {
            let item = ob.getComponent(AuditPlayerItem);
            if (item.headIsSelect()) {
                suspectUids.push(item.playerId);
            }
        }
        return suspectUids;
    }

    onClickSureBtn(): void {
        // 举报必须两个以上
        const suspectUids = this._getSuspectUids();
        // if (suspectUids.length < 2) {
        //     const options: BannerPopupOptions = {
        //         content: pf.languageManager.getString('Audit_MustTwoPersons')
        //     };
        //     PopupManager.showBanner(options);
        //     return;
        // }

        const fee = common.CurrencyUtil.convertServerAmountToDisplayNumber(this._chargefFee);
        let tipsStr = pf.StringUtil.formatC(pf.languageManager.getString('Audit_ConfirmDialog'), fee);
        let emailAddress = this.emailEditBox.string;
        if (emailAddress.length > 0) {
            if (!pf.ValidationUtil.isValidEmail(emailAddress)) {
                // 邮件格式不合格
                const options: BannerPopupOptions = {
                    content: pf.languageManager.getString('Audit_EmailTips')
                };
                PopupManager.showBanner(options);
                return;
            }
        }

        if (this._first === 1 || this._freeCounts > 0) {
            const options: AlertDialogOptions = {
                content: tipsStr,
                firstBtnText: pf.languageManager.getString('Common_Confirm'),
                secondBtnText: pf.languageManager.getString('Common_Cancel'),
                firstCallback: this.sureToAudit.bind(this)
            };
            PopupManager.showDialog(PopupDialogType.AlertDialog, options);
        } else {
            if (this._goldEnough) {
                const options: AlertDialogOptions = {
                    content: tipsStr,
                    firstBtnText: pf.languageManager.getString('Common_Confirm'),
                    secondBtnText: pf.languageManager.getString('Common_Cancel'),
                    firstCallback: this.sureToAudit.bind(this)
                };
                PopupManager.showDialog(PopupDialogType.AlertDialog, options);
            } else {
                tipsStr = pf.StringUtil.formatC(pf.languageManager.getString('Audit_NeedRecharge'), this._chargefFee);
                const options: AlertDialogOptions = {
                    content: tipsStr,
                    firstBtnText: pf.languageManager.getString('Common_Confirm'),
                    secondBtnText: pf.languageManager.getString('Common_Cancel'),
                    firstCallback: this.gotoShop.bind(this)
                };
                PopupManager.showDialog(PopupDialogType.AlertDialog, options);
            }
        }
    }

    async sureToAudit(): Promise<void> {
        let tPokerHandData = this._handData;
        let roomid = tPokerHandData.nRoomID;
        let clubId = tPokerHandData.nClubID;
        let roomUuid = Number(tPokerHandData.sRoomUUID);
        let gameUuid = Number(tPokerHandData.sGameUUID);

        let emailAddress = this.emailEditBox.string;
        this.node.active = false;
        const auditService = pf.serviceManager.get(pf.services.AuditService);
        const suspectUids = this._getSuspectUids();
        const resp = await auditService.requestAuditPlayers(
            roomid,
            clubId,
            roomUuid,
            gameUuid,
            suspectUids,
            emailAddress
        );

        const options: ToastPopupOptions = {
            content: pf.languageManager.getString('Audit_Success')
        };
        PopupManager.showToast(options);
    }

    cancelAudit(): void {
        this.node.active = false;
    }

    gotoShop(): void {
        this.cancelAudit();
        // cv.SHOP.RechargeClick();
    }

    updateAuditMoney(): void {
        let moneyTxt = '';
        if (this._first === 1) {
            moneyTxt = pf.languageManager.getString('Audit_FirstTimeFree');
        } else if (this._freeCounts > 0) {
            let acBuffer = pf.StringUtil.formatC(pf.languageManager.getString('Audit_FreeTimeLeft'), this._freeCounts);
            moneyTxt = acBuffer;
        } else {
            let num = common.CurrencyUtil.convertServerAmountToDisplayNumber(this._chargefFee);
            let acBuffer = pf.StringUtil.formatC(pf.languageManager.getString('Audit_CoinValue'), num);
            moneyTxt = acBuffer;
        }

        this.expenseTxt.string = pf.StringUtil.formatC(pf.languageManager.getString('Audit_ExpenseFormat'), moneyTxt);
    }

    updateView(): void {
        let kPlayerRecords: PlayerRecord[] = this._handData.vPlayerRecords;

        if (this.obItemList.length > 0) {
            // eslint-disable-next-line @typescript-eslint/prefer-for-of
            for (let i = 0; i < this.obItemList.length; i++) {
                this.obItemList[i].removeFromParent(true);
                this.obItemList[i].destroy();
            }
        }

        this.obItemList = [];
        let scale = 0.8;
        for (let i = 0; i < kPlayerRecords.length; i++) {
            let obItem = cc.instantiate(this.obItemPrefab);
            obItem.setPosition(
                cc.v2(
                    (i % 5) * obItem.getContentSize().width * scale + 30,
                    this.listContent.getContentSize().height -
                        (Math.floor(i / 5) + 1) * obItem.getContentSize().height * scale -
                        450
                )
            );
            obItem.setScale(scale);
            obItem.getComponent(AuditPlayerItem).setData(kPlayerRecords[i]);
            this.listContent.addChild(obItem);
            this.obItemList.push(obItem);
        }

        if (this._first !== 1) {
            this.expenseTxt.node.active = true;
        }
    }
}
