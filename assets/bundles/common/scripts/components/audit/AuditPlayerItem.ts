import * as pf from 'pf';
import PlayerRecord = pf.data.PlayerRecord;
import { AvatarControl } from '../AvatarControl';
import { macros } from '../../common/common-macros';

const { ccclass, property } = cc._decorator;
@ccclass
export class AuditPlayerItem extends cc.Component {
    @property(cc.Label) roleName: cc.Label = null;
    @property(AvatarControl) avatar: AvatarControl = null;
    @property(cc.Node) check: cc.Node = null;

    playerId: number = null;

    onBtnHeadClick(event: cc.Event.EventCustom) {
        pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
        this.check.active = !this.check.active;
        event.stopPropagation();
        // cv.MessageCenter.send("showObRoleInfo");
    }

    setData(data: PlayerRecord) {
        this.roleName.string = data.playerName;
        this.playerId = data.playerid;
        // let rdata: RemarkData = cv.dataHandler.getUserData().getRemarkData(data.nPlayerID);
        // if (rdata.sRemark.length <= 0) {
        //     cv.StringTools.setShrinkString(this.roleName.node, data.sPlayerName, true);
        // } else {
        //     cv.StringTools.setShrinkString(this.roleName.node, rdata.sRemark, true);
        // }
        this.avatar.loadHeadImage(data.playerHead, data.plat);
    }

    headIsSelect(): Boolean {
        return this.check.active;
    }
}
