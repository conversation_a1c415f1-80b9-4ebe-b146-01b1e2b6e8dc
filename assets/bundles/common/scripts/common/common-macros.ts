/* eslint-disable @typescript-eslint/consistent-type-definitions */
/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
export namespace macros {
    export const BUNDLE_NAME = 'common';
    export const SHOP_SIGN = 'JWF7d20kSGdrQntCNiFTUGwraiRWejlCM3QkX3pedUw';
    export const UUID_WEB = 'd41d8cd98f00b204e9800998ecf8427e';
    export const ZORDER_TT = 12; // temp z order from PKW
    export const ZORDER_LOADING = 14; // temp z order from PKW
    export const RADIX_DECIMAL = 10;

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/common-addressable-assets-zh_cn',
        EN_US = 'configs/common-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        HI_IN = 'languages/string-hi_in',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph'
    }

    export enum ZORDER_TYPE {
        ZORDER_verylow = -20,
        ZORDER_low = -10,
        ZORDER_0 = 0,
        ZORDER_1 = 1,
        ZORDER_2 = 2,
        ZORDER_3 = 3,
        ZORDER_4 = 4,
        ZORDER_5 = 5,
        ZORDER_6 = 6,
        ZORDER_7 = 7,
        ZORDER_15 = 15,
        ZORDER_SHADER = 9,
        ZORDER_TOP = 10,
        ZORDER_TT = 11,
        ZORDER_ACTIVITY = 12,
        ZORDER_LOADING = 13,
        ZORDER_LOG = 14,
        RG_BUTTON = 7,
        RG_VIEW = 7,
        SCREEN_PROTECTOR = 20
    }

    export enum CardNum {
        CARD_2 = 0,
        CARD_3,
        CARD_4,
        CARD_5,
        CARD_6,
        CARD_7,
        CARD_8,
        CARD_9,
        CARD_10,
        CARD_J,
        CARD_Q,
        CARD_K,
        CARD_A,
        CARD_INVALID
    }

    /**
     * 牌花色
     */
    export enum CardSuit {
        CARD_DIAMOND = 0, // 方片
        CARD_CLUB, // 梅花
        CARD_HEART, // 红心
        CARD_SPADE, // 黑桃
        CardSuit_MAX
    }

    export enum Asset {
        CARD_BACK_ATLAS = 'common.card-back-atlas',
        CHIP_ICON_GC = 'common.gc-stack-1',
        CHIP_ICON_SC = 'common.sc-stack-1',
        AVATAR_MASK_CIRCLE = 'common.common-mask-circle',
        AVATAR_MASK_SQUARE = 'common.common-mask-square',
        AVATAR_DEFAULT = 'common.default-avatar'
    }

    export enum Audio {
        BUTTON_CLICK = 'common-audio.button-click',
        COMMON_CLOSE = 'common-audio.common-close',
        Tab = 'common-audio.tab',
        BACK_BUTTON = 'common-audio.back-button',
        LUCK_START = 'common-audio.luck-start',
        LUCK_RESULT = 'common-audio.luck-result',
        CHIP_FLY = 'common-audio.chip-fly',
        DEAL_CARD = 'common-audio.deal-card',
        PLAYER_TURN = 'common-audio.player-turn',
        CHIPS_TO_POT = 'common-audio.chips-to-pot'
    }

    export enum DynamicAsset {
        TOAST_BG_PORTRAIT = 'common-dynamic.toast-bg-portrait',
        TOAST_BG_LANDSCAPE = 'common-dynamic.toast-bg-landscape',
        SLIDER_VERIFY_BG = 'common-dynamic.slider-verify-bg',
        GOOGLE_RECAPTCHA = 'common-dynamic.google-recaptcha',
        AUDIT = 'common-dynamic.audit',
        CARD_ATLAS_1 = 'common-dynamic.card-atlas-1',
        CARD_ATLAS_2 = 'common-dynamic.card-atlas-2',
        CARD_ATLAS_3 = 'common-dynamic.card-atlas-3',
        ICON_RESULT_SUCCESS = 'common-dynamic.icon-result-success',
        ICON_RESULT_FAILED = 'common-dynamic.icon-result-failed',

        RED_ENVELOPE_CLOSED = 'common-dynamic.red-envelope-closed',
        RED_ENVELOPE_OPENED = 'common-dynamic.red-envelope-opened',
        SPIN_NORMAL_SPRITE = 'common-dynamic.spin-button-normal',
        SPIN_DISABLE_SPRITE = 'common-dynamic.spin-button-disable',
        TEXT_REWARD_SPRITE = 'common-dynamic.red-envelope-text-reward',
        WIN_RESULT_SPRITE = 'common-dynamic.win-result',
        RED_ENVELOPE_BG_SPRITE = 'common-dynamic.red-envelope-bg',
        IPHONE_SPRITE = 'common-dynamic.iphone-icon',
        TICKET_SPRITE = 'common-dynamic.ticket-icon',
        CURRENCY_TYPE0_ICON_SMALL = 'common-dynamic.currency-type0-icon-small',
        CURRENCY_TYPE0_ICON_LARGE = 'common-dynamic.currency-type0-icon-large',
        CURRENCY_TYPE1_ICON_SMALL = 'common-dynamic.currency-type1-icon-small',
        CURRENCY_TYPE1_ICON_LARGE = 'common-dynamic.currency-type1-icon-large',
        CURRENCY_TYPE2_ICON_SMALL = 'common-dynamic.currency-type2-icon-small',
        CURRENCY_TYPE2_ICON_LARGE = 'common-dynamic.currency-type2-icon-large',
        CURRENCY_TYPE5_ICON_SMALL = 'common-dynamic.currency-type5-icon-small',
        CURRENCY_TYPE5_ICON_LARGE = 'common-dynamic.currency-type5-icon-large',
        RANK_1 = 'common-dynamic.rank-1',
        RANK_2 = 'common-dynamic.rank-2',
        RANK_3 = 'common-dynamic.rank-3',
        CURRENCY_TYPE0_ICON_RED_ENVELOPE = 'common-dynamic.currency-type0-icon-red-envelope',
        CURRENCY_TYPE1_ICON_RED_ENVELOPE = 'common-dynamic.currency-type1-icon-red-envelope',
        CURRENCY_TYPE2_ICON_RED_ENVELOPE = 'common-dynamic.currency-type2-icon-red-envelope',
        CURRENCY_TYPE3_ICON_RED_ENVELOPE = 'common-dynamic.currency-type3-icon-red-envelope',
        CURRENCY_TYPE5_ICON_RED_ENVELOPE = 'common-dynamic.currency-type5-icon-red-envelope',
        RED_ENVELOPE = 'common-dynamic.red-envelope',
        RED_ENVELOPE_BANNER = 'common-dynamic.red-envelope-banner',
        LUCK_TURNTABLE = 'common-dynamic.luck-turntable',
        LUCK_TURNTABLE_H = 'common-dynamic.luck-turntable-h',
        LUCK_TURNTABLE_SQUID = 'common-dynamic.luck-turntable-squid',
        LUCK_TURNTABLE_H_SQUID = 'common-dynamic.luck-turntable-h-squid'
    }

    export enum WebUrl {
        // COIN_STORE_URL = 'https://web.clubwpt.liuxinyi1.cn/index',
        // HELP_PAGE_URL = 'https://web.clubwpt.liuxinyi1.cn/help',
        // KYC_VERIFY_URL = 'https://web.clubwpt.liuxinyi1.cn/index',
        BLANK_PAGE = 'about:blank'
    }

    export enum AudioSettingKeys {
        MUSIC = 'client_music_key',
        SOUND_EFFECT = 'client_sound_key'
    }

    export enum SCENE {
        TransitionScene = 'TransitionScene', // 过渡场景
        LOADING_SCENE = 'LoadingScene', // 加载场景
        LOGIN_SCENE = 'LoginScene', // 登陆场景
        HALL_SCENE = 'HallScene', // 大厅场景
        CLUB_SCENE = 'ClubScene', // 俱乐部场景
        GAME_SCENE = 'Game', // 游戏场景
        GAME_SCENE_AOF = 'GameAof', // 游戏场景
        COWBOY_SCENE = 'CowboyScene', // 德州牛仔
        VIDEOCOWBOY_SCENE = 'VideoCowboyScene', // 视频牛仔
        HUMANBOY_SCENE = 'HumanboyScene', // 百人德州
        POKERMASTER_SCENE = 'PokerMasterScene', // 扑克大师
        JACKFRUIT_SCENE = 'JackfruitScene', // 菠萝蜜
        HOTUPDATE_SCENE = 'HotUpdate', // 热更新场景
        SPORTS_SCENE = 'SportsScene', // 体育赛事
        POCKETGAME_SCENE = 'PocketGameScene', // 电子小游戏
        BLACKJACK_SCENE = 'BlackJackScene',
        FISHINGKING_SCENE = 'FishingKingScene',
        TOPMATCHE_SCENE = 'TopMatcheScene', // 一起看球
        // BLACKJACKPVP_SCENE = 'BlackjackPVP',             // 21点
        PKF_LIVE_SCENE = 'PKFLiveScene', // PKF 直播
        CARIBBEAN_POKER_SCENE = 'CaribbeanPokerScene', // Caribbean Stud Poker
        WEALTHTRIO_SCENE = 'WealthTrioScene',
        ISLOT_SCENE = 'ISlotScene' // ISlot slot machine
    }

    export enum REPORT_TYPE {
        TOURNAMENT = 'Tournament',
        GC = 'GC',
        SC = 'SC',
        LOBBY = 'lobby'
    }

    export const AVATAR_BASE_PATH = 'textures/first-load-auto-atlas/head/circle/head_';
    export const AVATAR_MAX_NUMBER = 18;

    export enum ElectronPermissions {
        CAMERA = 'camera',
        MICROPHONE = 'microphone'
    }

    export enum PermissionsStatus {
        GRANTED = 'granted',
        NOT_DETERMINED = 'not-determined',
        DENIED = 'denied',
        UNKNOWN = 'unknown'
    }
    // WEB AGORA SDK WEB
    export const WEB_AGORA_REQ_HEAD = window.location.href;
    export const WEB_AGORA_REQ_HEAD_DEV = 'https://frontend.dev.liuxinyi1.cn/pkw-lite/';
    export const WEB_AGORA_REQ_HEAD_STAGE = 'https://frontend.dev.liuxinyi1.cn/pkw-lite/';

    // Agora SDK WEB 接口
    export const WEB_AGORA_APP_ID = 'e3e16446c0d44bb6a04597f0668b9b6a';
    export const WEB_AGORA_REQ_URL = 'extern/h5StreamLive/joinReq/index1.html';

    export enum SettingKey {
        EnableSound = 'setting_enableSound',
        BidBlindBets = 'gameplay_bb_key',
        EmailNoticeForLogin = 'setting_emailnotice_login',
        EmailNoticeForEvent = 'setting_emailnotice_event',
        QuickBetPots = 'quick_bet_pots',
        BackGround = 'back_ground_index',
        CardBack = 'card_back_index',
        CardFront = 'card_front_index',

        MultiOrPerShow = 'setting_multi_or_per_show'
    }

    export enum CurrencyType {
        None = 'None',
        GOLD = 'Gold',
        USDT = 'USDT'
    }

    export type Currency = {
        symbol: string;
        shortSymbol?: string;
        code: string;
        type: CurrencyType;
        iconPath: string;
    };

    type CurrenciesType = {
        [key in CurrencyType]: Currency;
    };

    export const Currencies: CurrenciesType = {
        [CurrencyType.None]: {
            symbol: '',
            code: '',
            type: CurrencyType.None,
            iconPath: ''
        },
        [CurrencyType.GOLD]: {
            symbol: 'CN¥',
            shortSymbol: '¥',
            code: 'CNY',
            type: CurrencyType.GOLD,
            iconPath: 'textures/currencies/CNY'
        },
        [CurrencyType.USDT]: {
            symbol: '',
            code: 'USDT',
            type: CurrencyType.USDT,
            iconPath: 'textures/currencies/USDT'
        }
    };

    export type CountryCurrency = {
        [countryISO2: string]: Currency;
    };
}
