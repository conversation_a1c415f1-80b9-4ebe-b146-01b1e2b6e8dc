import * as pf from 'pf';
import { macros } from './common/common-macros';

import BUNDLE_NAME = macros.BUNDLE_NAME;

@pf.registerEntry(BUNDLE_NAME)
export class CommonEntry extends pf.BundleEntryBase {
    private _changeLanguageBinder = this.onChangeLanguage.bind(this);
    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_RESOURCE;
    }

    protected getLanguageStringPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Language_String_Path.ZH_CN;
            case pf.LANGUAGE_GROUPS.yn_TH:
                return macros.Language_String_Path.YN_TH;
            case pf.LANGUAGE_GROUPS.th_PH:
                return macros.Language_String_Path.TH_PH;
            case pf.LANGUAGE_GROUPS.hi_IN:
                return macros.Language_String_Path.HI_IN;
            default:
                return macros.Language_String_Path.EN_US;
        }
    }

    protected getAddressableConfigPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Addressable_Config_Path.ZH_CN;
            default:
                return macros.Addressable_Config_Path.EN_US;
        }
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onLoad`);
        pf.languageManager.addListener('beforeLanguageChange', this._changeLanguageBinder);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onEnter`);

        await this.loadConfigs();

        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask(BUNDLE_NAME);
        await assetLoader.start(options?.onProgress);
        /*
        if (!Array.isArray(options.nodes)) {
            throw new pf.InvalidParameterError('Lack root nodes');
        }
        */
        // if (options.nodes[0]) {
        //     const popupNode = await PopupManager.getInstance().createPopupParent();
        //     popupNode.parent = options.nodes[0];
        // } else {
        //     throw new pf.InvalidParameterError('Lack popup root node');
        // }

        // if (options.nodes[1]) {
        //     await LoadingViewManager.getInstance().createNodes(options.nodes[1]);
        // } else {
        //     throw new pf.InvalidParameterError('Lack loadingview root node');
        // }

        // const livePefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common.liveprefab');
        // const livepefab = cc.instantiate(livePefab);
        // cc.director.getScene().addChild(livepefab);
    }

    private loadPersistNode<T extends cc.Component>(key: string, type: { prototype: T }): T {
        const prefab = pf.addressableAssetManager.getAsset<cc.Prefab>(key);
        const node = cc.instantiate(prefab);
        cc.game.addPersistRootNode(node);
        return node.getComponent(type);
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onExit`);
        // pf.bundleManager.releaseAll(this.bundle);
    }

    onChangeLanguage(newLanguage: string, promises: Promise<unknown>[]): void {
        const promise = this.loadConfigs(newLanguage);
        promises.push(promise);
    }

    onUnload(): void {
        cc.log(`bundle ${BUNDLE_NAME} onUnload`);
        pf.languageManager.removeListener('beforeLanguageChange', this._changeLanguageBinder);
    }
}
