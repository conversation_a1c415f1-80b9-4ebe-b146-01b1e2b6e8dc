import * as pf from '../../../poker-framework/scripts/pf';
import registerEntry = pf.registerEntry;
import * as network from './network/humanboy-network-index';
import { macros } from './common/humanboy-macros';
import { HumanboyService } from './domain/humanboy-service';
import type { HumanboyRoom } from './domain/humanboy-room';

@registerEntry(macros.BUNDLE_NAME)
export class HumanboyEntry extends pf.BundleEntryBase {
    private _socket: pf.Nullable<pf.client.ISocket> = null;
    private _humanboyService: pf.Nullable<HumanboyService> = null;
    private _room: pf.Nullable<HumanboyRoom> = null;

    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_GAME;
    }

    protected getLanguageStringPath() {
        let path = macros.Language_String_Path.ZH_CN;
        switch (pf.languageManager.currentLanguage) {
            case pf.LANGUAGE_GROUPS.en_US:
                path = macros.Language_String_Path.EN_US;
                break;
            case pf.LANGUAGE_GROUPS.yn_TH:
                path = macros.Language_String_Path.YN_TH;
                break;
            case pf.LANGUAGE_GROUPS.th_PH:
                path = macros.Language_String_Path.TH_PH;
                break;
            case pf.LANGUAGE_GROUPS.hi_IN:
                path = macros.Language_String_Path.HI_IN;
                break;
        }
        return path;
    }

    protected getAddressableConfigPath() {
        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
            ? macros.Addressable_Config_Path.ZH_CN
            : macros.Addressable_Config_Path.EN_US;
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onEnter`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        this._socket = context?.socket;

        if (!this._socket) {
            return Promise.reject(new pf.InvalidParameterError('options.socket is null or undefined!'));
        }

        if (options.roomId === undefined) {
            return Promise.reject(new pf.InvalidParameterError('options.roomId is undefined!'));
        }

        await this.loadConfigs();

        // download assets
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('humanboy');

        // NOTE:
        // make address progress not over 20%
        let addressalbeTotalCount = 0;

        await assetLoader.start((finish, total) => {
            if (addressalbeTotalCount === 0) {
                addressalbeTotalCount = total * 5;
            }
            options?.onProgress(finish, addressalbeTotalCount);
        });

        const asyncOp = new pf.AsyncOperation<void>();

        cc.log('load scene HumanboyScene');
        this.bundle.loadScene(
            'HumanboyScene',
            (finish, total) => {
                options?.onProgress(
                    finish + assetLoader.totalCount,
                    Math.max(total + assetLoader.totalCount, addressalbeTotalCount)
                );
            },
            (err, scene) => {
                if (err) {
                    cc.warn(err);
                    asyncOp.reject(err);
                } else {
                    cc.log('run scene HumanboyScene');
                    this.loginGame(context, options.roomId)
                        .then(() => {
                            cc.director.runScene(
                                scene,
                                () => {
                                    if (this.onBeforeLoadScene) {
                                        this.onBeforeLoadScene();
                                    }
                                },
                                () => {
                                    asyncOp.resolve();
                                }
                            );
                        })
                        .catch((err) => {
                            asyncOp.reject(err);
                        });
                }
            }
        );

        return asyncOp.promise;
    }

    protected async onPreload(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload`);
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('humanboy-dynamic');
        assetLoader.addLoadAddressableGroupTask('humanboy-audio');
        await assetLoader.startPreload();
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload done`);
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onExit`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        // moved to HumanboyControl::tryLeaveRoom()
        // try {
        //     await this._room?.leaveRoom();
        // } catch (err) {
        //     cc.warn(err);
        // }
        pf.serviceManager.unregister(HumanboyService);
        this._humanboyService = null;
        this._room = null;

        this._socket?.removeGameSession(network.PKWHumanboySession);
        this._socket = null;

        context.bundle = '';
        context.session = null;
        context.service = null;
        context.room = null;
        context.exitCallback = null;
    }

    onUnload(): void {
        cc.log(`bundle ${macros.BUNDLE_NAME} onUnload`);
    }

    protected async loginGame(context: pf.services.MiniGameContext, roomId: number): Promise<void> {
        try {
            // for identifying which game the user is trying to play if errors happen
            context.gameId = pf.client.GameId.HumanBoy;

            cc.log('create pkw humanboy session');
            const humanboySession = this._socket.createGameSession(
                network.PKWHumanboySession,
                pf.client.GameId.HumanBoy
            );

            this._humanboyService = new HumanboyService(humanboySession);
            pf.serviceManager.register(this._humanboyService);

            cc.log(`login ${macros.BUNDLE_NAME}`);
            await this._humanboyService.login();

            cc.log(`${macros.BUNDLE_NAME} join room`);
            this._room = (await this._humanboyService.joinRoom(roomId)) as HumanboyRoom;

            // store mini game state to game context
            context.bundle = macros.BUNDLE_NAME;
            // context.gameId = pf.client.GameId.HumanBoy;
            context.session = humanboySession;
            context.service = this._humanboyService;
            context.room = this._room;
            context.roomId = this._room.id;
        } catch (err) {
            // NOTE:
            // Do not throw error to prevent exit bundle when login fail.
            // Let GameSession re join room when socket reconnect
            cc.warn(`login ${macros.BUNDLE_NAME} failed: ${(err as Error).message})`);

            if (this._humanboyService) {
                pf.serviceManager.unregister(HumanboyService);
                this._socket.removeGameSession(network.PKWHumanboySession);
                this._humanboyService = null;
            }

            throw err;
        }
    }
}
