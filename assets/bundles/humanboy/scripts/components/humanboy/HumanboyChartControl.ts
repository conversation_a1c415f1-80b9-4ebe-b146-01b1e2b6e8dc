/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/humanboy-macros';
import * as domain from '../../domain/humanboy-domain-index';
import * as network from '../../network/humanboy-network-index';
import * as cr from '../../../../common-resource/scripts/common-resource';

enum PageMode {
    PageMode_None = 0,
    PageMode_Spade,
    PageMode_Heart,
    PageMode_Club,
    PageMode_Diamond,
    PageMode_Type
}

const { ccclass, property } = cc._decorator;
@ccclass
export class HumanboyChartControl extends cc.Component {
    // 1-4Button
    @property(cc.Node) chartbg: cc.Node = null;
    @property(cc.Button) btn_close: cc.Button = null;
    @property(cc.Button) btn_1: cc.Button = null;
    @property(cc.Button) btn_2: cc.Button = null;
    @property(cc.Button) btn_3: cc.Button = null;
    @property(cc.Button) btn_4: cc.Button = null;
    @property(cc.Button) btn_5: cc.Button = null;

    @property(cc.Node) _layout1: cc.Node = null;
    @property(cc.Node) _layout2: cc.Node = null;
    @property(cc.Node) _layout3: cc.Node = null;
    @property(cc.Node) _layout4: cc.Node = null;
    @property(cc.Node) _layout5: cc.Node = null;

    @property(cc.Node) _panelRecord: cc.Node = null;
    @property(cc.Node) _panelBiaoGe2: cc.Node = null;

    // 5-1
    @property(cc.Node) _zhuang_text: cc.Node = null;
    @property(cc.Node) _title_text: cc.Node = null;

    @property(cc.Node) tong_txt: cc.Node = null;
    @property(cc.Node) tong_num_txt: cc.Node = null;

    // 5-1
    @property(cc.Node) gaopai_txt: cc.Node = null;
    @property(cc.Node) gaopai_num_txt: cc.Node = null;
    @property(cc.Node) santiao_txt: cc.Node = null;
    @property(cc.Node) santiao_num_txt: cc.Node = null;
    @property(cc.Node) liangdui_txt: cc.Node = null;
    @property(cc.Node) liangdui_num_txt: cc.Node = null;
    @property(cc.Node) yidui_txt: cc.Node = null;
    @property(cc.Node) yidui_num_txt: cc.Node = null;

    // 5-2
    @property(cc.Node) shun_txt: cc.Node = null;
    @property(cc.Node) shun_num_txt: cc.Node = null;
    @property(cc.Node) ths_txt: cc.Node = null;
    @property(cc.Node) ths_num_txt: cc.Node = null;

    @property(cc.Node) _zhuang_bg: cc.Node = null;
    @property(cc.Node) title_bg: cc.Node = null;
    @property(cc.Node) _zhuang_img: cc.Node = null;

    // @property(cc.Prefab) hollow: cc.Prefab = null;
    // @property(cc.Prefab) dot: cc.Prefab = null;

    @property _buttonList: any[] = [];
    @property _layoutList: any[] = [];

    @property _recordNum: number = 20;
    @property _recordDots: any[][] = [];
    @property _oriRecordDotsPos: any[] = [];
    @property _hollowDots: cc.Node[][][] = [];

    _mapPageMode: Map<number, number> = new Map();
    _curPage: PageMode = PageMode.PageMode_Spade;

    @property(cc.SpriteAtlas) humanboy_chart_PLIST: cc.SpriteAtlas = null;

    private _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集

    @property(cc.Button) des_btn: cc.Button = null;
    @property(cc.Sprite) des_spr: cc.Sprite = null;

    private _humanboyRoom: pf.Nullable<domain.HumanboyRoom> = null;

    _roomTrend = new domain.RoomTrend();

    _boundChangeLanguage = this.onChangeLanguage.bind(this);

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._humanboyRoom = context.room as domain.HumanboyRoom;

        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_LANGUAGE_ATLAS);
        for (let i = 1; i < 6; i++) {
            let str = pf.StringUtil.formatC('Layout%d', i);
            let layout = cc.find(str, this.chartbg);
            layout.name = String(i);
            this._layoutList.push(layout);
        }

        this._layout1 = this._layoutList[0];
        this._layout2 = this._layoutList[1];
        this._layout3 = this._layoutList[2];
        this._layout4 = this._layoutList[3];
        this._layout5 = this._layoutList[4];

        this._title_text = cc.find('5/title_bg/title_text', this.chartbg);

        this._zhuang_bg = cc.find('5/zhuang_bg', this.chartbg);
        this._zhuang_text = cc.find('5/zhuang_bg/zhuang_text', this.chartbg);
        this._zhuang_img = cc.find('5/zhuang_img', this.chartbg);

        this.btn_close.node.on('click', () => {
            pf.audioManager.playSoundEffect(macros.Audio.Close);
            this.des_spr.node.parent.active = false;
            this.node.active = false;
        });

        this.des_spr.node.parent.active = false;
        this.node.getChildByName('zhezhao_panel').on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                this.des_spr.node.parent.active = false;
                event.stopPropagation();
            },
            this
        );
        this.des_spr.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                event.stopPropagation();
            },
            this
        );
        this.des_btn.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                pf.audioManager.playSoundEffect(macros.Audio.Tab);
                this.des_spr.node.parent.active = true;
            },
            this
        );

        this.des_spr.spriteFrame = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_ROAD_RULE_SPRITE);

        this._mapPageMode.set(network.BetZoneOption.POS1, PageMode.PageMode_Spade);
        this._mapPageMode.set(network.BetZoneOption.POS2, PageMode.PageMode_Heart);
        this._mapPageMode.set(network.BetZoneOption.POS3, PageMode.PageMode_Club);
        this._mapPageMode.set(network.BetZoneOption.POS4, PageMode.PageMode_Diamond);

        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_1, PageMode.PageMode_Type);
        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_2, PageMode.PageMode_Type);
        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_3, PageMode.PageMode_Type);
        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_4, PageMode.PageMode_Type);
        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_5, PageMode.PageMode_Type);
        this._mapPageMode.set(network.BetZoneOption.POS_LUCK_6, PageMode.PageMode_Type);

        // 初始化三维数组
        for (let i = 0; i < 4; i++) {
            let arr2: any[] = [];
            this._hollowDots[i] = arr2;
            for (let j = 0; j < 6; j++) {
                let arr3: any[] = [];
                arr2[j] = arr3;
            }
        }

        for (let i = 1; i < 6; i++) {
            let arr2: any[] = [];
            this._recordDots[i] = arr2;
        }

        for (let i = 1; i < 6; i++) {
            let str = pf.StringUtil.formatC('Button_%d', i);
            let btn = cc.find('title_img/' + str, this.chartbg);
            btn.name = String(i);
            this._buttonList.push(btn);
            btn.on(
                'click',
                (event: { target: { name: any } }, customEventData: any) => {
                    pf.audioManager.playSoundEffect(macros.Audio.Tab);
                    this._curPage = Number(event.target.name);
                    let xxx = this._layoutList[this._curPage - 1];
                    this._panelRecord = cc.find('panelRecord', xxx);
                    this.updateData();
                },
                this
            );
        }

        this.cleanData();

        for (let i = 4; i > 0; i--) {
            this.initRecord(i);
            this.initBiaoge2(i);
        }

        this.updateData();

        pf.languageManager.addListener('languageChange', this._boundChangeLanguage);

        // 两个监听
        this.onChangeLanguage();
    }

    onMsgGameTrend(roomTrend: domain.RoomTrend) {
        this._roomTrend = roomTrend;
        this.updateChartList(this._curPage);
    }

    showView(option: network.BetZoneOption): void {
        this.node.active = true;
        let self = this;

        this._mapPageMode.forEach((value: number, key: number) => {
            if (key === option) {
                self._curPage = value;
            }
        });

        this.queryTrend();
        this.displayPage(self._curPage);
    }

    updateChartList(page: PageMode): void {
        this._curPage = page;

        this.updateData();
    }

    updateReusult() {
        this.showHistoryMoveAnim();
        this.queryTrend();
        // this._humanboyRoom.queryTrend();
    }

    queryTrend() {
        this._humanboyRoom.queryTrend().then((trend) => {
            this.onMsgGameTrend(trend);
        });
    }

    displayPage(page: PageMode) {
        switch (page) {
            case PageMode.PageMode_None:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_spade');

                this._layout1.active = false;
                this._layout2.active = false;
                this._layout3.active = false;
                this._layout4.active = false;
                this._layout5.active = false;
                this.setQuestionView(false);

                break;
            case PageMode.PageMode_Spade:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_spade');

                this._layout1.active = true;
                this._layout2.active = false;
                this._layout3.active = false;
                this._layout4.active = false;
                this._layout5.active = false;
                this.setQuestionView(true);

                break;
            case PageMode.PageMode_Heart:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_heart');

                this._layout1.active = false;
                this._layout2.active = true;
                this._layout3.active = false;
                this._layout4.active = false;
                this._layout5.active = false;
                this.setQuestionView(true);

                break;
            case PageMode.PageMode_Club:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_club');

                this._layout1.active = false;
                this._layout2.active = false;
                this._layout3.active = true;
                this._layout4.active = false;
                this._layout5.active = false;
                this.setQuestionView(true);

                break;
            case PageMode.PageMode_Diamond:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_diamond');

                this._layout1.active = false;
                this._layout2.active = false;
                this._layout3.active = false;
                this._layout4.active = true;
                this._layout5.active = false;
                this.setQuestionView(true);

                break;
            case PageMode.PageMode_Type:
                this.title_bg.getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame('humanboy_title_statistics');

                this._layout1.active = false;
                this._layout2.active = false;
                this._layout3.active = false;
                this._layout4.active = false;
                this._layout5.active = true;
                this.setQuestionView(false);
                break;
            default:
                break;
        }
    }

    initRecord(page: PageMode) {
        let index = page - 1;
        this._panelRecord = cc.find('panelRecord', this._layoutList[index]);
        let item = cc.find('recordDot', this._panelRecord);
        item.active = false;

        let pos = item.getPosition();
        let offsetX = 64.64; // (this._panelRecord.getContentSize().width - pos.x * 2) / (this._recordNum - 1)/2;
        this._recordDots[index] = [];
        this._oriRecordDotsPos = [];

        for (let i = 0; i < this._recordNum + 1; i++) {
            let dotitem = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.DOT));
            dotitem.setContentSize(cc.size(50, 50));
            this._panelRecord.addChild(dotitem);
            dotitem.active = false;
            dotitem.setPosition(cc.v2(pos.x + i * offsetX, pos.y));
            this._recordDots[index].push(dotitem);
            this._oriRecordDotsPos.push(this._recordDots[index][i].getPosition());
        }
    }

    updateBiaoge2(page: PageMode) {
        if (page < 5) {
            for (let i = 0; i < 6; i++) {
                for (let j = 0; j < 23; j++) {
                    this._hollowDots[page - 1][i][j].stopAllActions();
                    this._hollowDots[page - 1][i][j].active = false;
                }
            }

            let index = page - 1;

            if (index >= 0 && index < this._roomTrend.trendOption.length) {
                for (let i = 0; i < this._roomTrend.trendOption[page - 1].road.length; i++) {
                    let row = this._roomTrend.trendOption[page - 1].road[i];
                    for (let j = 0; j < row.roadRow.length; j++) {
                        this._hollowDots[page - 1][i][j].destroyAllChildren();
                        this._hollowDots[page - 1][i][j].removeAllChildren(true);
                        if (row.roadRow[j].result < 0) {
                            this._hollowDots[page - 1][i][j].active = true;
                            this._hollowDots[page - 1][i][j].opacity = 255;
                            this._hollowDots[page - 1][i][j].getComponent(cc.Sprite).spriteFrame =
                                this.humanboy_chart_PLIST.getSpriteFrame('humanboy_hollow_red');
                        } else if (row.roadRow[j].result > 0) {
                            this._hollowDots[page - 1][i][j].active = true;
                            this._hollowDots[page - 1][i][j].opacity = 255;
                            this._hollowDots[page - 1][i][j].getComponent(cc.Sprite).spriteFrame =
                                this.humanboy_chart_PLIST.getSpriteFrame('humanboy_hollow_gray');
                        }

                        if (row.roadRow[j].eqc > 0) {
                            // 平次数
                            let hecount = pf.StringUtil.formatC('%d', row.roadRow[j].eqc);

                            let node = new cc.Node('text');
                            node.name = 'ping';
                            node.addComponent(cc.Label);
                            node.getComponent(cc.Label).string = hecount;
                            node.getComponent(cc.Label).fontSize = 24;
                            node.color = cc.color(23, 130, 82);
                            node.setPosition(cc.v2(0, -11));

                            this._hollowDots[page - 1][i][j].addChild(node);
                        }

                        if (
                            i === this._roomTrend.trendOption[page - 1].lastRow &&
                            j === this._roomTrend.trendOption[page - 1].lastCol
                        ) {
                            if (i === 0 && j === 0) {
                                return;
                            }

                            this._hollowDots[page - 1][i][j].runAction(cc.blink(2, 2));
                        }
                    }
                }
            }
        }
    }

    hideHistoryMoveAnim() {
        for (let i = 0; i < this._recordDots[this._curPage].length; i++) {
            this._recordDots[this._curPage - 1][i].stopAllActions();
            this._recordDots[this._curPage - 1][i].setPosition(this._oriRecordDotsPos[i]);
        }
    }

    showHistoryMoveAnim() {
        if (this._curPage < 5) {
            let index = this._curPage - 1;

            if (this._humanboyRoom.historyResults.length > 0) {
                let betOption =
                    this._roomTrend.trendOption[index].lastResult[
                        this._roomTrend.trendOption[index].lastResult.length - 1
                    ];
                let frameName = 'humanboy_draw';

                if (betOption === network.BetZoneOption.POS_LUCK_1) {
                    frameName = 'humanboy_win';
                } else if (betOption === network.BetZoneOption.POS_LUCK_2) {
                    frameName = 'humanboy_lose';
                }
                this._recordDots[index][this._recordDots[index].length - 1].getComponent(cc.Sprite).spriteFrame =
                    this._atlas_hb_language.getSpriteFrame(frameName);

                for (let i = 0; i < this._recordDots[index].length; i++) {
                    if (i === this._recordDots[index].length - 1) {
                        this.updateHistoryResults();
                    }
                }
            }
        }
    }

    updateHistoryResults() {
        if (this._curPage < 5) {
            this.hideHistoryMoveAnim();

            let index = this._curPage - 1;

            if (index >= 0 && index < this._roomTrend.trendOption.length) {
                let historySize = this._roomTrend.trendOption[index].lastResult.length;
                for (let i = 0; i < this._recordNum; i++) {
                    let historyIdx = historySize - i - 1;
                    let recordDotIdx = this._recordNum - i - 1;

                    const dotSprite: cc.Sprite = this._recordDots[index][recordDotIdx].getComponent(cc.Sprite);

                    if (historyIdx < 0) {
                        dotSprite.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_draw');
                        dotSprite.node.active = false;
                    } else {
                        let betOption = this._roomTrend.trendOption[index].lastResult[historyIdx];
                        let frameName = 'humanboy_draw';

                        if (betOption < 0) {
                            frameName = 'humanboy_win';
                        } else if (betOption > 0) {
                            frameName = 'humanboy_lose';
                        }

                        dotSprite.spriteFrame = this._atlas_hb_language.getSpriteFrame(frameName);
                        dotSprite.node.active = true;
                    }
                }
            }
        }
    }

    initBiaoge2(page: PageMode) {
        this._panelBiaoGe2 = cc.find('panelBiaoGe2', this._layoutList[page - 1]);
        let item = cc.find('item_image', this._panelBiaoGe2);
        let pos = item.getPosition();
        item.active = false;

        let offset = cc.v2(1288 / 23, -336 / 6);
        for (let i = 0; i < 6; i++) {
            for (let j = 0; j < 23; j++) {
                let hollowitem = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HOLLOW));
                this._panelBiaoGe2.addChild(hollowitem);
                hollowitem.active = false;
                hollowitem.setPosition(cc.v2(pos.x + j * offset.x, pos.y + i * offset.y));
                this._hollowDots[page - 1][i].push(hollowitem);
            }
        }
    }

    cleanData() {
        this.gaopai_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.yidui_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.liangdui_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.santiao_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.shun_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.ths_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
        this.tong_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
            pf.languageManager.getString('Humanboy_chart_type_ju'),
            0
        );
    }

    onChangeLanguage() {
        this.gaopai_txt.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_chart_type_high_card');
        this.yidui_txt.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_chart_type_high_pairs');
        this.liangdui_txt.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_chart_type_two_pairs');
        this.santiao_txt.getComponent(cc.Label).string = pf.languageManager.getString(
            'Humanboy_chart_type_three_of_a_kind'
        );
        this.shun_txt.getComponent(cc.Label).string = pf.languageManager.getString(
            'Humanboy_chart_type_straight_flush'
        );
        this.ths_txt.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_chart_type_combine');

        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            this.gaopai_txt.getComponent(cc.Label).fontSize = 30;
            this.gaopai_num_txt.getComponent(cc.Label).fontSize = 30;
            this.yidui_txt.getComponent(cc.Label).fontSize = 30;
            this.yidui_num_txt.getComponent(cc.Label).fontSize = 30;
            this.liangdui_txt.getComponent(cc.Label).fontSize = 30;
            this.liangdui_num_txt.getComponent(cc.Label).fontSize = 30;
            this.santiao_txt.getComponent(cc.Label).fontSize = 30;
            this.santiao_num_txt.getComponent(cc.Label).fontSize = 30;
            this.shun_txt.getComponent(cc.Label).fontSize = 30;
            this.shun_num_txt.getComponent(cc.Label).fontSize = 30;
            this.ths_txt.getComponent(cc.Label).fontSize = 30;
            this.ths_num_txt.getComponent(cc.Label).fontSize = 30;
            this.tong_txt.getComponent(cc.Label).fontSize = 30;
            this.tong_num_txt.getComponent(cc.Label).fontSize = 30;

            let _interval = 6;
            let xx = cr.UIUtil.updateAndMeasureLabel(
                this.gaopai_txt.getComponent(cc.Label),
                pf.languageManager.getString('Humanboy_chart_type_high_card')
            );

            this.gaopai_num_txt.setPosition(
                this.gaopai_txt.getPosition().x + xx.width + _interval,
                this.gaopai_num_txt.getPosition().y
            );

            let yidui = cr.UIUtil.updateAndMeasureLabel(
                this.yidui_txt.getComponent(cc.Label),
                pf.languageManager.getString('Humanboy_chart_type_high_pairs')
            );
            this.yidui_num_txt.setPosition(
                this.yidui_txt.getPosition().x + yidui.width + _interval,
                this.yidui_num_txt.getPosition().y
            );

            let liangdui = cr.UIUtil.updateAndMeasureLabel(
                this.liangdui_txt.getComponent(cc.Label),
                pf.languageManager.getString('Humanboy_chart_type_two_pairs')
            );
            this.liangdui_num_txt.setPosition(
                this.liangdui_txt.getPosition().x + liangdui.width + _interval,
                this.liangdui_num_txt.getPosition().y
            );

            let santiao = cr.UIUtil.updateAndMeasureLabel(
                this.santiao_txt.getComponent(cc.Label),
                pf.languageManager.getString('Humanboy_chart_type_three_of_a_kind')
            );
            this.santiao_num_txt.setPosition(
                this.santiao_txt.getPosition().x + santiao.width + _interval,
                this.santiao_num_txt.getPosition().y
            );

            this.shun_num_txt.setPosition(
                this.shun_txt.getPosition().x +
                    cr.UIUtil.updateAndMeasureLabel(
                        this.shun_txt.getComponent(cc.Label),
                        pf.languageManager.getString('Humanboy_chart_type_straight_flush')
                    ).width +
                    _interval,
                this.shun_num_txt.getPosition().y
            );

            let ths = cr.UIUtil.updateAndMeasureLabel(
                this.ths_txt.getComponent(cc.Label),
                pf.languageManager.getString('Humanboy_chart_type_combine')
            );
            this.ths_num_txt.setPosition(
                this.ths_txt.getPosition().x + ths.width + _interval,
                this.ths_num_txt.getPosition().y
            );

            let tong = cr.UIUtil.updateAndMeasureLabel(
                this.tong_txt.getComponent(cc.Label),
                pf.StringUtil.formatC(pf.languageManager.getString('Humanboy_chart_type_tongchi'))
            );
            this.tong_num_txt.setPosition(
                this.tong_txt.getPosition().x + tong.width + _interval,
                this.tong_num_txt.getPosition().y
            );
        }
    }

    updateData() {
        this.updatePage(this._curPage);
        this.updateHistoryResults();

        this.updateBiaoge2(this._curPage);
    }

    updatePage(page: PageMode) {
        if (page < 5) {
            let panelStatistics = cc.find('panelStatistics', this._layoutList[page - 1]);
            let _statistics_text = cc.find('statistics_text', panelStatistics);
            let _win_text = cc.find('win_text', panelStatistics);
            let _lose_text = cc.find('lose_text', panelStatistics);
            let _draw_text = cc.find('draw_text', panelStatistics);
            let _win_img = cc.find('win_img', panelStatistics);
            let _lose_img = cc.find('lose_img', panelStatistics);
            let _draw_img = cc.find('draw_img', panelStatistics);

            _win_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_win');

            _lose_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_lose');

            _draw_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_draw');

            let index = page - 1;

            if (index >= 0 && index < this._roomTrend.trendOption.length) {
                let statistics = this._roomTrend.trendOption[index].stats.capHandNum;
                let win = this._roomTrend.trendOption[index].stats.win;
                let lose = this._roomTrend.trendOption[index].stats.lose;
                let equal = this._roomTrend.trendOption[index].stats.equal;

                _statistics_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                    pf.languageManager.getString('Humanboy_chart_type_statistics'),
                    statistics
                );
                _win_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                    pf.languageManager.getString('Humanboy_chart_type_win'),
                    win
                );
                _lose_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                    pf.languageManager.getString('Humanboy_chart_type_lose'),
                    lose
                );
                _draw_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                    pf.languageManager.getString('Humanboy_chart_type_draw'),
                    equal
                );
            }
        } else {
            let gaopai = this._roomTrend.handLevelStatistics.stats.gaoPai;
            let yidui = this._roomTrend.handLevelStatistics.stats.yuDui;
            let liangdui = this._roomTrend.handLevelStatistics.stats.lianDui;
            let santiao = this._roomTrend.handLevelStatistics.stats.sanTiao;
            let shunziand1 = this._roomTrend.handLevelStatistics.stats.shunZiAnd1;
            let huluand3 = this._roomTrend.handLevelStatistics.stats.huLuAnd3;
            let winall = this._roomTrend.handLevelStatistics.stats.winAll;
            let loseall = this._roomTrend.handLevelStatistics.stats.loseAll;

            // let total = winall + loseall;
            this.gaopai_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                gaopai
            );
            this.yidui_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                yidui
            );
            this.liangdui_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                liangdui
            );
            this.santiao_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                santiao
            );

            this.shun_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                shunziand1
            );
            this.ths_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                huluand3
            );

            this._title_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_max'),
                this._roomTrend.handLevelStatistics.stats.capHandNum
            );
            this._zhuang_text.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_des'),
                this._roomTrend.handLevelStatistics.stats.capHandNum
            );
            this.tong_txt.getComponent(cc.Label).string = pf.languageManager.getString('Humanboy_chart_type_tongchi');
            this.tong_num_txt.getComponent(cc.Label).string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_chart_type_ju'),
                winall
            );
        }

        this.displayPage(page);
    }

    onDestroy() {
        pf.languageManager.removeListener('languageChange', this._boundChangeLanguage);
    }

    setQuestionView(isView: boolean) {
        this.des_spr.node.parent.active = false;
        this.des_btn.node.active = isView;
    }
}
