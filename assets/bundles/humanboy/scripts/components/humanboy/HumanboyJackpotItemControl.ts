/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import type * as network from '../../network/humanboy-network-index';

const { ccclass, property } = cc._decorator;
@ccclass
export class HumanboyJackpotItemControl extends cc.Component {
    @property(cc.Sprite) bg_img: cc.Sprite = null;
    @property(cc.Label) playername_text: cc.Label = null;
    @property(cc.Label) day_text: cc.Label = null;
    @property(cc.Label) award_text: cc.Label = null;
    @property(cc.Label) cardtypename_text: cc.Label = null;

    protected onLoad(): void {}

    setData(playerData: network.IAwardData): void {
        this.playername_text.node.setContentSize(cc.size(120, 60));

        cr.UIUtil.setShrinkString(this.playername_text.node, playerData.name);

        let numStr = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(playerData.amount)
        );
        this.award_text.string = numStr;

        if (playerData.handLevel === 8) {
            cr.UIUtil.setShrinkString(
                this.cardtypename_text.node,
                pf.languageManager.getString('Humanboy_game_card_type_four_of_a_kind')
            );
        } else {
            cr.UIUtil.setShrinkString(
                this.cardtypename_text.node,
                pf.languageManager.getString(pf.StringUtil.formatC('M_UITitle%d', 112 + playerData.handLevel))
            );
        }

        this.day_text.string = pf.TimeUtil.formatTime(playerData.timeStamp, pf.eTimeType.Month_Day);

        this.cardtypename_text.node.setContentSize(cc.size(204, 60));

        this.cardtypename_text.node.opacity = 125;
        this.cardtypename_text.node.color = cc.color(232, 201, 147);

        this.day_text.node.opacity = 125;
        this.day_text.node.color = cc.color(232, 201, 147);
    }

    setFirstData(playerData: network.IAwardData): void {
        this.bg_img.node.active = true;
        this.playername_text.fontSize = 28;
        this.day_text.fontSize = 28;
        this.cardtypename_text.fontSize = 28;

        this.cardtypename_text.node.setContentSize(cc.size(204, 60));
        this.playername_text.node.setContentSize(cc.size(120, 60));

        cr.UIUtil.setShrinkString(this.playername_text.node, playerData.name);

        let numStr = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(playerData.amount)
        );
        this.award_text.string = numStr;

        if (playerData.handLevel === 8) {
            cr.UIUtil.setShrinkString(
                this.cardtypename_text.node,
                pf.languageManager.getString('Humanboy_game_card_type_four_of_a_kind')
            );
        } else {
            cr.UIUtil.setShrinkString(
                this.cardtypename_text.node,
                pf.languageManager.getString(pf.StringUtil.formatC('M_UITitle%d', 112 + playerData.handLevel))
            );
        }

        this.day_text.string = pf.TimeUtil.formatTime(playerData.timeStamp, pf.eTimeType.Month_Day);
        this.day_text.node.x = 266;
    }

    protected start(): void {}
}
