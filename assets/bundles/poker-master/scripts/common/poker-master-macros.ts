export namespace macros {
    export const BUNDLE_NAME = 'poker-master';
    export const SCENE_NANE = 'PokerMasterScene';
    export const RULE_URL = 'user/article/getimage?img=';

    // width of poker master playerlist panel: 200
    // width of icon inside playerlist: 156
    // there is a distance of 22 pixels from edges of icon to edges of playerlist
    export const noFitBuffer = 15;
    export const narrowGamePanelWidth = 1755;
    export const gapBetweenGamePanelAndPlayerlistPanel = 0;
    export const safeAreaSurrenderDistance = 20;

    export const SAFE_AREA_PLAYER_LIST_OFFSET = 38;
    export const SAFE_AREA_PLAYER_LIST_SCALE = 0.9;
    export const SAFE_AREA_BOARD_SCALE = 0.96;

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/poker-master-addressable-assets-zh_cn',
        EN_US = 'configs/poker-master-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph',
        HI_IN = 'languages/string-hi_in'
    }

    export enum Assets {
        POKER_MASTER_ATLAS = 'poker-master.poker-master-atlas',
        LUCK_TURNTABLE_BUTTON = 'common.luck-turntables-button',
        // HUMANBOY_LANGUAGE_CONFIG = 'humanboy.language-strings',
        HUMANBOY_ATLAS = 'common-resource.humanboy-atlas',
        // HUMANBOY_NUMBER_ATLAS = 'humanboy.humanboy-number-atlas',
        HUMANBOY_LANGUAGE_ATLAS = 'common-resource.humanboy-language-atlas',
        HUMANBOY_EXCHANGE_ATLAS = 'common-resource.humanboy-exchange-atlas',
        // HUMANBOY_LIST_BG_1 = 'humanboy.humanboy-list_bg_1-sprite',
        // HUMANBOY_LIST_BG_2 = 'humanboy.humanboy-list_bg_2-sprite',
        // HUMANBOY_HEAD_NONE_CIRCLE = 'humanboy.humanboy-head_none_circle-sprite',
        // HUMANBOY_HEAD_SYSTEM_CIRCLE = 'humanboy.humanboy-head_system_circle-sprite',
        // HUMANBOY_HEAD_SYSTEM_SQUARE = 'humanboy.humanboy-head_system_suqare-sprite',
        // HUMANBOY_HEAD_SYSTEM_BOX_CIRCLE = 'humanboy.humanboy-head_system_box_circle-sprite',
        // HUMANBOY_HEAD_PLAYER_BOX_CIRCLE = 'humanboy.humanboy-head_player_box_circle-sprite',
        // HUMANBOY_HEAD_DEALER_CHOOSE_CIRCLE = 'humanboy.humanboy-head_dealer_choose-sprite',
        HUMANBOY_GAME_RUN_SPRITE_001 = 'common-resource.humanboy-game_round_001-sprite',
        HUMANBOY_GAME_RUN_SPRITE_002 = 'common-resource.humanboy-game_round_002-sprite',
        HUMANBOY_GAME_RUN_SPRITE_003 = 'common-resource.humanboy-game_round_003-sprite',
        HUMANBOY_GAME_RUN_SPRITE_004 = 'common-resource.humanboy-game_round_004-sprite',
        HUMANBOY_GAME_RUN_SPRITE_005 = 'common-resource.humanboy-game_round_005-sprite',
        HUMANBOY_GAME_RUN_SPRITE_006 = 'common-resource.humanboy-game_round_006-sprite',
        // HUMANBOY_SPECIAL_CARD_TYPE_1_PREFAB = 'humanboy.humanboy-special_card_type_1-prefab',
        // HUMANBOY_SPECIAL_CARD_TYPE_2_PREFAB = 'humanboy.humanboy-special_card_type_2-prefab',
        // HUMANBOY_SPECIAL_CARD_TYPE_3_PREFAB = 'humanboy.humanboy-special_card_type_3-prefab',
        // HUMANBOY_SPECIAL_STRAIGHT_SPRITE = 'humanboy.humanboy-special_straight-sprite',
        // HUMANBOY_SPECIAL_STRAIGHT_FLUSH_SPRITE = 'humanboy.humanboy-special_straight_flush-sprite',
        // HUMANBOY_SPECIAL_GOURD_SPRITE = 'humanboy.humanboy-special_gourd-sprite',
        // HUMANBOY_SPECIAL_FOUR_OF_A_KIND_SPRITE = 'humanboy.humanboy-special_four_of_a_kind-sprite',
        // HUMANBOY_SPECIAL_ROYAL_FLUSH_SPRITE = 'humanboy.humanboy-special_royal_flush-sprite',
        // HUMANBOY_SPECIAL_FLUSH_SPRITE = 'humanboy.humanboy-special_flush-sprite',
        // HUMANBOY_SPECIAL_TREE_OF_A_KIND_SPRITE = 'humanboy.humanboy-special_three_of_a_kind-sprite',
        // HUMANBOY_SPECIAL_TWO_PAIRS_SPRITE = 'humanboy.humanboy-special_two_pairs-sprite',
        // HUMANBOY_LIGHT_BOX_1_PREFAB = 'humanboy.humanboy-light_box_1-prefab',
        // HUMANBOY_LIGHT_BOX_2_PREFAB = 'humanboy.humanboy-light_box_2-prefab',
        // HUMANBOY_LIGHT_BOX_3_PREFAB = 'humanboy.humanboy-light_box_3-prefab',
        // HUMANBOY_LIGHT_BOX_4_PREFAB = 'humanboy.humanboy-light_box_4-prefab',
        COWBOY_LANGUAGE_ATLAS = 'common-resource.cowboy-language-atlas',
        POKER_MASTER_CARD_BACK_SPRITE = 'poker-master.poker-master-card-back-sprite',
        POKER_MASTER_TOUCH_SPRITE = 'poker-master.poker-master-touch-sprite',
        POKER_MASTER_NO_TOUCH_SPRITE = 'poker-master.poker-master-no-touch-sprite',
        // COWBOY_TIPS_SPRITE = 'humanboy.cowboy-tips-sprite',
        // COWBOY_HEAD_1_SPRITE = 'humanboy.cowboy-head_1-sprite',
        // COWBOY_WIN_SKELETON = 'humanboy.cowboy-win-skeleton',
        // COWBOY_LOSE_SKELETON = 'humanboy.cowboy-lose-skeleton',
        // COWBOY_ROAD_RULE_SPRITE = 'humanboy.cowboy-road_rule-sprite',

        MINI_GAME_MENU = 'common-resource.mini-game-menu',
        HUMANBOY_ADVANCED_AUTO = 'common-resource.humanboy-advanced-auto',
        MINI_GAME_ADVANCED_AUTO = 'common-resource.mini-game-advanced-auto',
        MINI_GAME_DIALOG = 'common-resource.mini-game-dialog',
        BET_COIN = 'common-resource.bet-coin',
        MINI_GAME_GUIDE = 'common-resource.mini-game-guide',
        HUMANBOY_FLUTTER_SCORE = 'common-resource.humanboy-flutter-score',
        HUMANBOY_TOAST = 'common-resource.humanboy-toast',
        HUMANBOY_REWARD_TIPS = 'common-resource.humanboy-reward-tips',
        WIN_FLAG = 'common-resource.win-flag',
        START_BETS = 'common-resource.start-bets',
        END_BETS = 'common-resource.end-bets',
        WAY_OUT = 'common-resource.way-out',
        // POP_SILENCE = 'common-resource.pop-silence',
        POKER_CARD = 'common-resource.poker-card',
        POKER_CARD_SQUINT = 'common-resource.poker-card',
        AVATAR = 'common-resource.avatar',
        HOLLOW = 'common-resource.hollow',
        SOLID = 'common-resource.solid',
        SPECIAL_CARD_TYPE_ATLAS = 'common-resource.special-card-type-atlas',
        COWBOY_TREND_ANIM_ATLAS = 'common-resource.cowboy-trend-anim-atlas',
        CHART_ATLAS = 'common-resource.chart-atlas',
        CONSUMING_PROMPT = 'common-resource.consuming-prompt',
        CARD_BACK = 'poker-master.card-back',
        WIN_PLAYER_LIGHT = 'common-resource.win-player-light',
        MINI_GAME_RECHARGE = 'poker-master.mini-game-recharge',
        MINI_GAME_MENU_WITHOUT_EXCHANGE = 'common-resource.mini-game-menu_without-exchange',
        REBATE_FLOATING_BUTTON_NEW = 'common-resource.rebate-floating-button-new',
        REBATE_COINS_FLY = 'common-resource.rebate-coins-fly'
    }

    export enum Dynamic_Assets {
        POKER_MASTER_TABLE_FULL_SCREEN_SPRITE = 'poker-master-dynamic.poker-master-table-fs-sprite',
        POKER_MASTER_TABLE_IPAD_SPRITE = 'poker-master-dynamic.poker-master-table-ipad-sprite',
        POKER_MASTER_TABLE_NORMAL_SPRITE = 'poker-master-dynamic.poker-master-table-nomral-sprite',
        POKER_MASTER_SHARK_TIP_SPRITE = 'poker-master-dynamic.poker-master-shark-tip-sprite',
        POKER_MASTER_TIP_SPRITE = 'poker-master-dynamic.poker-master-tip-sprite',
        POKER_MASTER_REVIEW_TITLE_SPRITE = 'poker-master-dynamic.poker-master-review-title-sprite',
        COWBOY_CHART_DESCRIPTION = 'common-resource-dynamic.des-img',
        POKER_MASTER_TITLE_STATISTICS = 'poker-master-dynamic.poker-master-title-statistics-sprite',
        // HUMANBOY_TABLE_SPRITE = 'humanboy-dynamic.humanboy-table-sprite',
        // HUMANBOY_TABLE_BOARD_SPRITE = 'humanboy-dynamic.humanboy-table_broad-sprite',
        // HUMANBOY_TABLE_NARROW_SPRITE = 'humanboy-dynamic.humanboy-table_narrow-sprite',
        // HUMANBOY_JACKPOT_PREFAB = 'humanboy-dynamic.humanboy-jackpot-prefab',
        // HUMANBOY_JACKPOT_BROAD_PREFAB = 'humanboy-dynamic.humanboy-jackpot_broad-prefab',
        // HUMANBOY_JACKPOT_NARROW_PREFAB = 'humanboy-dynamic.humanboy-jackpot_narrow-prefab'
        POKER_MASTER_NB_FLAG_SKILL = 'poker-master-dynamic.poker-master-skill',
        POKER_MASTER_NB_FLAG_RICH_MAN = 'poker-master-dynamic.poker-master-rich-man',
        POKER_MASTER_RING_TIP = 'poker-master-dynamic.poker-master-ring-tip',

        MINI_GAME_EXCHANGE = 'common-resource-dynamic.mini-game-exchange',
        MINI_GAME_RULE = 'common-resource-dynamic.mini-game-rule',
        MINI_GAME_AUDIO_SETTING = 'common-resource-dynamic.mini-game-audio-setting',
        MINI_GAME_ADVANCED_SETTING = 'common-resource-dynamic.mini-game-advanced-setting',
        MINI_GAME_EXIT = 'common-resource-dynamic.mini-game-exit',
        HEAD_POINTS_ANI = 'common-resource-dynamic.head-points-ani',
        MINI_GAME_PLAYER_LIST = 'common-resource-dynamic.mini-game-player-list'
    }

    export enum Audio {
        BGM = 'common-resource-audio.bgm', // 1背景
        Begin_Bet = 'common-resource-audio.begin-bet', // 开始下注
        End_Bet = 'common-resource-audio.end-bet', // 停止下注
        Kaipai = 'common-resource-audio.kaipai', //  开牌
        Fapai = 'common-resource-audio.fapai', // 发牌
        // // Start_Round = 'common-resource-audio.start-round', // 开局提示
        Betin = 'common-resource-audio.bet', // 投少量金币
        Betin_Many = 'common-resource-audio.bet-many', // 投大量金币
        // Win_Lose = 'common-resource-audio.win-lose', // 输赢
        Get_win_coin = 'common-resource-audio.get-win-coin', // 收金币
        Button = 'common-resource-audio.press',
        Time_Tick = 'common-resource-audio.time-tick', // 时间滴答
        // // Dealer_Vd = 'humanboy-audio.delaer_vd', // 庄家完胜完败
        // // Special_Card_Type_Small = 'humanboy-audio.special_card_type_small', // 特殊牌型小牌
        // Special_Card_Type_Middle = 'humanboy-audio.special-card-type-middle', // 特殊牌型中牌
        Special_Card_Type_Big = 'common-resource-audio.special-card-type-big', // 特殊牌型大牌
        // // Silence_Music = 'humanboy-audio.silence-music',
        // Jackpot = 'humanboy-audio.jackpot', // jackpot音效
        // Close = 'common-resource-audio.common-close',
        Tab = 'common-resource-audio.tab'
        // DEAL_CARD = 'common-resource-audio.deal_card'
    }

    export enum AudioSettingKeys {
        MUSIC = 'client_music_key',
        SOUND_EFFECT = 'client_sound_key'
    }
}
