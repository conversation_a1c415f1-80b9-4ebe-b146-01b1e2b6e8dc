import * as pf from '../../../../poker-framework/scripts/pf';
import * as cr from '../../../common-resource/scripts/common-resource';
import * as network from '../network/poker-master-network-index';
// import { ServerError } from '../../../../poker-framework/scripts/pf';

import { RoomParams, OutItem, BetReview, PlayerFortune } from './poker-master-room-params';
import { GameState } from './poker-master-service-types';
import { BetZone } from './poker-master-bet-zone';
import { RoundInfo, RoundResult } from './poker-master-round-info';
import { RoomTrend } from './poker-master-room-trend';

import { CalmDownParams } from '../../../../poker-framework/scripts/services/services-index';

import AutoBetLevel = pf.client.session.AutoBetLevel;
import GamePlayer = pf.services.GamePlayer;
import BetSettings = pf.services.BetSettings;
import PlayerOneBet = pf.services.PlayerOneBet;
import { CardItem } from './poker-master-card-item';
import { PlayerSettle } from './poker-master-player-settle';

export interface PokerMasterGameRoomEvents extends pf.services.IMiniGameRoomEvents {
    dataSync: (service: PokerMasterRoom) => void;
    calmDown: (params: CalmDownParams) => void;
    gameRoundEnd: (service: PokerMasterRoom) => void;

    // game round
    deal: (service: PokerMasterRoom) => void;
    startBet: (service: PokerMasterRoom) => void;
    stopBet: (service: PokerMasterRoom) => void;
    readyGame: (service: PokerMasterRoom) => void;
    showOdds: (service: PokerMasterRoom) => void;

    // bet
    bet: (data: PlayerOneBet) => void;

    mergeAutoBet: (notify: network.IMergeAutoBetNotify) => void;

    // property change
    roomParamChange: (service: PokerMasterRoom) => void;
    betCoinOptionsChange: (data: number[]) => void;

    updateReview: () => void;

    trendNotify: (notify: RoomTrend) => void;

    leftGameCoin: (notify: network.ILeftGameCoinNotify) => void;

    leaveRoom: () => void;

    // error
    kicked: (notify: network.IKickNotify) => void;
    serverError: (code: number) => void;
}

export class PokerMasterRoom extends pf.services.MiniGameRoom<PokerMasterGameRoomEvents> {
    private _gameSession: network.PokerMasterSession;
    private _gameState = new GameState();
    get gameState() {
        return this._gameState;
    }

    private _betSettings: BetSettings = new BetSettings();
    get betSettings(): BetSettings {
        return this._betSettings;
    }

    get userId(): number {
        return this._gameSession.userId;
    }

    // /** 是否可更新world服最新金币 */
    private _canUpdateWorldServerGold = true;
    get canUpdateWorldServerGold() {
        return this._canUpdateWorldServerGold;
    }

    private _otherPlayers: GamePlayer[] = [];
    get otherPlayers(): GamePlayer[] {
        return this._otherPlayers;
    }

    // private _historyResults: network.BetZoneOption[] = [];
    // get historyResults(): network.BetZoneOption[] {
    //     return this._historyResults;
    // }

    private _roomParams: RoomParams = new RoomParams();
    get roomParams(): RoomParams {
        return this._roomParams;
    }

    private _betZones = new Map<network.BetZoneOption, BetZone>();
    get betZones() {
        return this._betZones;
    }

    private _roundInfo = new RoundInfo();
    get roundInfo() {
        return this._roundInfo;
    }

    private _isDataSynced = false;
    get isDataSynced() {
        return this._isDataSynced;
    }

    // for data sync before scene loaded
    private _syncCalmDownParams = new CalmDownParams();
    get syncCalmDownParams() {
        return this._syncCalmDownParams;
    }

    betReview: BetReview[] = [];

    whoIsLeader: number;
    bCanSquint: boolean;
    bSkipSquint: boolean;
    sharkOuts: OutItem[] = [];
    dashiOuts: OutItem[] = [];
    // vPublicHoleCards: CardItem[] = [];
    fLeftFortune: number = 0;
    fRightFortune: number = 0;
    tFortune: PlayerFortune = new PlayerFortune();

    constructor(id: number, uuid: string, session: network.PokerMasterSession) {
        super(id, uuid);
        this._gameSession = session;

        this._gameSession.notification.on('dataSync', this.onDataSync.bind(this));
        this._gameSession.notification.on('startBet', this.onStartBet.bind(this));
        this._gameSession.notification.on('stopBet', this.onStopBet.bind(this));
        this._gameSession.notification.on('readyGame', this._handleReadyGameNofity.bind(this));
        this._gameSession.notification.on('showOdds', this._handleShowOddsNotify.bind(this));
        this._gameSession.notification.on('startSettlement', this.onStartSettlement.bind(this));
        this._gameSession.notification.on('gameRoundEnd', this.onGameRoundEnd.bind(this));
        this._gameSession.notification.on('betResp', this.onBetResp.bind(this));
        this._gameSession.notification.on('bet', this.onBet.bind(this));
        this._gameSession.notification.on('deal', this.onDeal.bind(this));
        this._gameSession.notification.on('mergeAutoBet', this.onMergeAutoBet.bind(this));
        this._gameSession.notification.on('advanceAutoBetCancel', this.onAdvanceAutoBetCancel.bind(this));
        this._gameSession.notification.on('kicked', this.onKicked.bind(this));
        this._gameSession.notification.on('trendNotice', this.onTrendNotify.bind(this));
        this._gameSession.notification.on('serverError', this.onServerError.bind(this));
        this._gameSession.notification.on('leftGameCoin', this.onLeftGameCoin.bind(this));
        this._gameSession.notification.on('betReview', this._handleBetReviewResponse.bind(this));
    }

    protected onStartBet(notify: network.IStartBetNotify): void {
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;
        this._gameState.leftSeconds = notify.leftSeconds;

        this._gameState.roundState = network.RoundState.BET;
        this._canUpdateWorldServerGold = true;

        this.emit('startBet', this);
        cc.log('PokerMasterService on startBet', notify);
    }

    /**
     * 游戏状态变更 - 停止下注
     * @param puf
     */
    private onStopBet(notify: network.IStopBetNotify /* puf: any, msgID: number */): void {
        // let noti: pokermaster_proto.StopBetNotify = this._parseNetMsg("StopBetNotify", puf, msgID);
        // if (!noti) return;
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp ?? 0;
        this._gameState.leftSeconds = notify.leftSeconds ?? 0;
        this.whoIsLeader = notify.whoIsLeader ?? 0;
        this._gameState.roundState = network.RoundState.STOP_BET;
        this.bCanSquint = notify.canSquint ?? false;
        this.bSkipSquint = notify.skipRound ?? false;
        this.roundInfo.roundResult.fisherLevel = notify.fisherLevel ?? 0;
        this.roundInfo.roundResult.sharkLevel = notify.sharkLevel ?? 0;
        pf.DataUtil.clearArray(this.sharkOuts);
        pf.DataUtil.clearArray(this.dashiOuts);

        let fishOutsLen = notify.fisherOuts.length;
        let sharkOutsLen = notify.sharkOuts.length;

        for (let i = 0; i < fishOutsLen; ++i) {
            let oi: OutItem = new OutItem();
            oi.fromProto(notify.fisherOuts[i]);
            this.dashiOuts.push(oi);
        }

        for (let i = 0; i < sharkOutsLen; ++i) {
            let oi: OutItem = new OutItem();
            oi.fromProto(notify.sharkOuts[i]);
            this.sharkOuts.push(oi);
        }

        pf.DataUtil.clearArray(this.roundInfo.publicCards);

        for (let card of notify.cards) {
            this.roundInfo.publicCards.push(CardItem.from(card));
        }

        this.emit('stopBet', this);
        cc.log('PokerMasterService on stopBet', notify);

        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().uWhoIsLeader = noti.whoIsLeader;
        // pokerMasterDataMgr.getPokerMasterRoom().eCurState = pokermaster_proto.RoundState.STOP_BET;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanSquint = noti.canSquint;
        // pokerMasterDataMgr.getPokerMasterRoom().bSkipSquint = noti.skipRound;
        // pokerMasterDataMgr.getPokerMasterRoom().fisherLevel = noti.fisherLevel;
        // pokerMasterDataMgr.getPokerMasterRoom().sharkLevel = noti.sharkLevel;

        // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().sharkOuts);
        // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().dashiOuts);
        // /* let */ fishOutsLen = noti.fisherOuts.length;
        // /* let */ sharkOutsLen = noti.sharkOuts.length;

        // for (let i = 0; i < fishOutsLen; ++i) {
        //     pokerMasterDataMgr.getPokerMasterRoom().dashiOuts.push(pokermaster_proto.OutItem.create(noti.fisherOuts[i]));
        // }

        // for (let i = 0; i < sharkOutsLen; ++i) {
        //     pokerMasterDataMgr.getPokerMasterRoom().sharkOuts.push(pokermaster_proto.OutItem.create(noti.sharkOuts[i]));
        // }

        // 所有公牌
        // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vPublicHoleCards);
        // for (let i = 0; i < noti.cards.length; ++i) {
        //     pokerMasterDataMgr.getPokerMasterRoom().vPublicHoleCards.push(pokermaster_proto.CardItem.create(noti.cards[i]))
        // }

        // this._sendLocalMsg(PokerMasterDef.LocalMsg().STATUS_STOP_BET);
    }

    /**
     * 游戏状态变更 - 清屏准备
     */
    private _handleReadyGameNofity(notify: network.IReadyGameNotify /* puf: any, msgID: number */): void {
        // let noti: pokermaster_proto.ReadyGameNotify = this._parseNetMsg("ReadyGameNotify", puf, msgID);
        // if (!noti) return;
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp ?? 0;
        this._gameState.leftSeconds = notify.leftSeconds ?? 0;
        this._gameState.roundState = network.RoundState.READY_GAME;
        this._canUpdateWorldServerGold = true;
        this.clearMapZoneData(false);

        this.emit('readyGame', this);
        cc.log('PokerMasterService on readyGame', notify);
        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().eCurState = pokermaster_proto.RoundState.READY_GAME;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanUpdateWorldServerGold = true;
        // pokerMasterDataMgr.getPokerMasterRoom().clearMapZoneData(false);

        // this._sendLocalMsg(PokerMasterDef.LocalMsg().STATUS_READY);
    }

    /**
     * 清除区域数据
     * @param bCleanAll
     */
    clearMapZoneData(clearCache: boolean): void {
        this.betZones.forEach((v: BetZone, k: network.BetZoneOption) => {
            v.reset(clearCache);
        });
    }

    /**
     * 游戏状态变更 - 公布赔率
     */
    private _handleShowOddsNotify(notify: network.IShowOddsNotify /* puf: any, msgID: number */): void {
        // let noti: pokermaster_proto.ShowOddsNotify = this._parseNetMsg("ShowOddsNotify", puf, msgID);
        // if (!noti) return;
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp ?? 0;
        this._gameState.leftSeconds = notify.leftSeconds ?? 0;
        this.whoIsLeader = notify.whoIsLeader ?? 0;

        this._parseGameOddsInfo(notify.option_odds);

        this.emit('showOdds', this);
        cc.log('PokerMasterService on readyGame', notify);

        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().uWhoIsLeader = noti.whoIsLeader;
        // console.log('whoIsLeader = ' + notify.whoIsLeader);

        // this._parseGameOddsInfo(noti.option_odds);
        // this._sendLocalMsg(PokerMasterDef.LocalMsg().STATUS_SHOW_ODDS);
    }

    protected onStartSettlement(): void {
        this._canUpdateWorldServerGold = false;
        cc.log('PokerMasterRoom onstartSettlement');
    }

    /**
     * 解析"赔率"信息
     * @param oddsOp
     */
    private _parseGameOddsInfo(oddsData: network.IBetOptionsOdds[]): void {
        for (let odd of oddsData) {
            // for (let i = 0; i < oddsData.length; ++i) {
            let option /* : pokermaster_proto.BetZoneOption */ = odd.option;
            let zoneData /* : PokerMasterZoneData */ = this.betZones.get(option);
            if (!zoneData) {
                zoneData = new BetZone(option);
                this.betZones.set(option, zoneData);
            }

            zoneData.odds = odd.odds;
            zoneData.limitBet = odd.limitRed;
        }
    }

    protected onBet(notify: network.IBetNotify): void {
        this.handleBetNofify(notify);

        this.updatePlayerCoin(notify.uid, notify.curCoin);
        this.updateAreaBet(notify);
        if (notify.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
            this.roundInfo.hasBetInCurRound = true;
        }

        const bet = new PlayerOneBet(notify.uid, notify.detail.option, notify.detail.betAmount);

        this.emit('bet', bet);

        // 原版
        // let noti: pokermaster_proto.BetNotify = this._parseNetMsg("BetNotify", puf, msgID);
        // if (!noti) return;

        // pokerMasterDataMgr.getPokerMasterRoom().updatePlayerCoin(noti.uid, noti.curCoin);
        // pokerMasterDataMgr.getPokerMasterRoom().updateAreaBet(noti);
        // if (noti.uid === cv.dataHandler.getUserData().u32Uid) {
        //     pokerMasterDataMgr.getPokerMasterRoom().bHasBetInCurRound = true;
        // }

        // this._sendLocalMsg(PokerMasterDef.LocalMsg().BET, noti);
    }

    /**
     * 更新某个选项的下注信息
     * @param notify
     */
    updateAreaBet(notify: network.IBetNotify): void {
        let option /* : pokermaster_proto.BetZoneOption */ = notify.detail.option;

        if (!this.betZones.has(option)) {
            this.betZones.set(option, new BetZone(option));
        }

        // 自己的下注
        if (notify.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
            this.betZones.get(option).optionInfo.selfBet = notify.selfBet;
        }

        // 总共的下注
        this.betZones.get(option).optionInfo.totalBet = notify.totalBet;

        // 原版
        // let option: pokermaster_proto.BetZoneOption = data.detail.option;

        // if (!this.mapZoneData.has(option)) {
        //     this.mapZoneData.add(option, new PokerMasterZoneData());
        // }

        // // 自己的下注
        // if (data.uid === this.u32Uid) {
        //     this.mapZoneData.get(option).selfBet = data.selfBet;
        // }

        // // 总共的下注
        // this.mapZoneData.get(option).totalBet = data.totalBet;
    }

    private handleBetNofify(notify: network.IBetNotify) {
        cc.log('PokerMasterRoom handleBetNofify', notify);

        this.updatePlayerCoin(notify.uid, notify.curCoin);

        const option = this._betZones.get(notify.detail.option).optionInfo;
        option.totalBet = notify.totalBet;

        if (this._selfPlayer.uid === notify.uid) {
            option.selfBet = notify.selfBet;
            this._roundInfo.hasBetInCurRound = true;
        }
    }

    private updatePlayerCoin(uid: number, coin: number): void {
        const player = this.getPlayer(uid);
        if (player) {
            player.curCoin = coin;
        }
    }
    /* eslint complexity: ["error", { max: 100 }] */
    /* eslint-disable no-fallthrough */
    protected onDataSync(notify: network.IGameDataSynNotify) {
        cc.log('PokerMasterService on DataSync', notify);

        this._roomParams.fromProto(notify.param);
        // 這一段抄過來的
        // this._llCoinUICritical = cr.CurrencyUtil.clientGoldByServer(notify.BetButtonLimitAmount);

        // // this._isOpenRoads = notify.openRoads;

        // this._betSettings.autoBetLevel = notify.autoLevel;
        // this._betSettings.betCoinOptions =
        //     notify.betCoinOption?.length > 0 ? notify.betCoinOption.slice() : this._roomParams.amountLevel.slice();
        // this._betSettings.selectAutoBetCount = notify.selectAutoBetCount;
        // this._betSettings.usedAutoBetCount = notify.usedAutoBetCount;
        // this._betSettings.autoBetCountList = [...notify.AutoBetCountList];
        // this._betSettings.canAdvanceAutoBet = notify.canAdvanceAuto;
        // this._betSettings.canAutoBet = notify.canAuto;
        // this._betSettings.reachLimitBet = notify.reachLimitBet;

        // this._gameState.leftSeconds = notify.leftSeconds;
        // this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;

        // this._gameState.roundState = notify.curState;

        // // this._jackpot.leftMoney = notify.jackpotLeftMoney;
        // // this.showDealerInvitation = notify.showMiddleUpDealerBtn;
        // // this._totalStockNum = notify.totalStockNum;

        // // // 庄家信息
        // // this._onDealerList = notify.onDealerList === 1;
        // // this._dealers = DealerPlayer.createDealers(notify.dealer);

        // // 玩家信息
        // this.clonePlayers(notify.players);

        // // 胜负记录
        // this._historyResults = notify.lastResult.slice();

        // notify.optionResults.forEach((result) => {
        //     // create bet zones
        //     const betZone = new BetZone(result.option);
        //     betZone.optionResult.from(result);

        //     this._betZones.set(result.option, betZone);
        // });

        // notify.optionInfo.forEach((info) => {
        //     const betZone = this._betZones.get(info.option);
        //     betZone.optionInfo.from(info);

        //     if (info.selfBet > 0) {
        //         this._roundInfo.hasBetInCurRound = true;
        //     }
        // });

        // if (notify.curState === network.RoundState.WAIT_NEXT_ROUND) {
        //     this.onGameRoundEnd(notify.cachedNotifyMsg);
        // }

        // this._isDataSynced = true;

        // this.emit('dataSync', this);

        // // debug purpose
        // // notify.CalmDownDeadLineTimeStamp = Date.now() + 100000;
        // // notify.CalmDownLeftSeconds = 66;
        // this._syncCalmDownParams.fromProto(notify);
        // const calmDownParams = new CalmDownParams();
        // calmDownParams.fromProto(notify);
        // this.emit('calmDown', calmDownParams);

        // 這一段pkw搬過來的
        // this.resetRound ();
        this._betSettings.canAutoBet = notify.canAuto;
        this._betSettings.canAdvanceAutoBet = notify.canAdvanceAuto;
        this._gameState.roundState = notify.curState;
        this._gameState.leftSeconds = notify.leftSeconds;
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;
        this._llCoinUICritical = cr.CurrencyUtil.convertToClientAmount(notify.BetButtonLimitAmount);
        this.whoIsLeader = notify.whoIsLeader ?? 0;
        this._betSettings.reachLimitBet = notify.reachLimitBet;
        if (notify.squintMsg) {
            this.whoIsLeader = notify.squintMsg.whoIsLeader;
            this.bCanSquint = notify.squintMsg.canSquint;
            this.bSkipSquint = notify.squintMsg.skipRound;
            this.roundInfo.roundResult.fisherLevel = notify.squintMsg.fisherLevel ?? 0;
            this.roundInfo.roundResult.sharkLevel = notify.squintMsg.sharkLevel ?? 0;
            pf.DataUtil.clearArray(this.sharkOuts);
            pf.DataUtil.clearArray(this.dashiOuts);
            let fishOutsLen = notify.squintMsg.fisherOuts.length;
            let sharkOutsLen = notify.squintMsg.sharkOuts.length;

            for (let i = 0; i < fishOutsLen; ++i) {
                let oi: OutItem = new OutItem();
                oi.fromProto(notify.squintMsg.fisherOuts[i]);
                this.dashiOuts.push(oi);
            }

            for (let i = 0; i < sharkOutsLen; ++i) {
                let oi: OutItem = new OutItem();
                oi.fromProto(notify.squintMsg.sharkOuts[i]);
                this.sharkOuts.push(oi);
            }
        }
        this.fLeftFortune = notify.fortune.fisherFortune;
        this.fRightFortune = notify.fortune.sharkFortune;
        pf.DataUtil.clearArray(this.roundInfo.blueHandCards);
        pf.DataUtil.clearArray(this.roundInfo.redHandCards);
        pf.DataUtil.clearArray(this.roundInfo.publicCards);
        // 左 - 渔夫
        for (let card of notify.fisherHoleCards) {
            let cardItem /* : pokermaster_proto.CardItem */ = CardItem.from(card);
            this.roundInfo.blueHandCards.push(cardItem);
            // pokerMasterDataMgr.getPokerMasterRoom().vLeftHandCards.push(card_item);
        }

        // 右 - 鲨鱼
        for (let card of notify.sharkHoleCards) {
            let cardItem /* : pokermaster_proto.CardItem */ = CardItem.from(card);
            this.roundInfo.redHandCards.push(cardItem);
        }

        // 公共牌
        for (let card of notify.publicCards) {
            let cardItem /* : pokermaster_proto.CardItem */ = CardItem.from(card);
            this.roundInfo.publicCards.push(cardItem);
        }

        // 赔率信息
        this._parseGameOddsInfo(notify.oddsOp);

        // 对应房间级别的下注金额选项(优先取下发的默认值, 若无默认值则取房间参数里面的amountLevel值)
        pf.DataUtil.clearArray(this.betSettings.betCoinOptions);
        let betcoinoptionSize: number = pf.DataUtil.getArrayLength(notify.betCoinOption);
        if (betcoinoptionSize > 0) {
            for (let i = 0; i < betcoinoptionSize; ++i) {
                this.betSettings.betCoinOptions.push(notify.betCoinOption[i]);
            }
        } else {
            let amountlevelSize: number = pf.DataUtil.getArrayLength(notify.param.amountLevel);
            for (let i = 0; i < amountlevelSize; ++i) {
                this.betSettings.betCoinOptions.push(notify.param.amountLevel[i]);
            }
        }

        // 续投
        this._betSettings.autoBetLevel = notify.autoLevel;
        this._betSettings.usedAutoBetCount = notify.usedAutoBetCount;
        this._betSettings.selectAutoBetCount = notify.selectAutoBetCount;
        pf.DataUtil.clearArray(this._betSettings.autoBetCountList);
        let autobetcountlistSize: number = pf.DataUtil.getArrayLength(notify.AutoBetCountList);
        for (let i = 0; i < autobetcountlistSize; ++i) {
            this._betSettings.autoBetCountList.push(notify.AutoBetCountList[i]);
        }

        for (let betOptionInfo of notify.optionInfo) {
            // 过滤有效选项
            // let bet: pokermaster_proto.IBetOptionInfo = betOptionInfo;
            if (
                betOptionInfo.option === network.BetZoneOption.FISHER_WIN ||
                betOptionInfo.option === network.BetZoneOption.SHARK_WIN ||
                betOptionInfo.option === network.BetZoneOption.FIVE_NONE_1DUI ||
                betOptionInfo.option === network.BetZoneOption.FIVE_2DUI ||
                betOptionInfo.option === network.BetZoneOption.FIVE_SAN_SHUN_TONG ||
                betOptionInfo.option === network.BetZoneOption.FIVE_GOURD ||
                betOptionInfo.option === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4
            ) {
                let zoneData /* : PokerMasterZoneData */ = this.betZones.get(betOptionInfo.option); // pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.get(bet.option);
                if (!zoneData) {
                    zoneData = new BetZone(betOptionInfo.option);
                    this.betZones.set(betOptionInfo.option, zoneData);
                }

                zoneData.optionInfo.selfBet = betOptionInfo.selfBet;
                zoneData.optionInfo.totalBet = betOptionInfo.totalBet;

                // 所有人下的金额
                pf.DataUtil.clearArray(zoneData.optionInfo.amounts);
                for (let j = 0; j < pf.DataUtil.getArrayLength(betOptionInfo.amount); ++j) {
                    zoneData.optionInfo.amounts.push(betOptionInfo.amount[j]);
                }

                // 若该区域自己有下过注,则标记已下注
                if (betOptionInfo.selfBet > 0) {
                    // pokerMasterDataMgr.getPokerMasterRoom().bHasBetInCurRound = true;
                    this.roundInfo.hasBetInCurRound = true;
                }
            }
        }

        pf.DataUtil.clearArray(this._otherPlayers);
        this.clonePlayers(notify.players);
        // for (let i = 0; i < pf.CollectionUtil.getArrayLength(notify.players); ++i) {
        //     // let player/* : pokermaster_proto.GamePlayer */ = GamePlayer.create(notify.players[i]);
        //     let player = new GamePlayer ();
        //     player.fromProto (notify.players[i]);
        //     if (i === 0) {
        //         if (player.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
        //             this._selfPlayer = player;
        //         }
        //         else {
        //             console.error('PokerMasterSocket.CMD.GAME_DATA_SYN, data error!!!, players[0] must be self!!!');
        //         }
        //     }
        //     else {
        //         this._otherPlayers.push(player);
        //     }
        // }

        let vLastResult: number[] = this.roundInfo.vLastResult; // this._historyResults;
        let maxHistoryResultsRetention: number = this.roundInfo.nMaxLastResultRetention;

        pf.DataUtil.clearArray(vLastResult);
        for (let betOptionInfo of notify.lastResult) {
            vLastResult.push(betOptionInfo);
            if (pf.DataUtil.getArrayLength(vLastResult) > maxHistoryResultsRetention) {
                vLastResult.splice(0, 1);
            }
        }

        for (let i = 0; i < pf.DataUtil.getArrayLength(notify.optionResults); ++i) {
            let eOption /* : pokermaster_proto.BetZoneOption */ = notify.optionResults[i].option;
            let vResult: number[] = notify.optionResults[i].results;
            let iLoseHand: number = notify.optionResults[i].loseHand;

            let zoneData /* : PokerMasterZoneData */ = this.betZones.get(eOption);
            if (!zoneData) {
                zoneData = new BetZone(eOption);
                this.betZones.set(eOption, zoneData);
            }

            zoneData.optionResult.luckLoseHand = iLoseHand;
            let vHistoryResults: number[] = zoneData.optionResult.historyResults;
            let maxHistoryResultsRetention: number = zoneData.maxHistoryResultsRetention;

            pf.DataUtil.clearArray(vHistoryResults);
            for (let i = 0; i < pf.DataUtil.getArrayLength(vResult); ++i) {
                vHistoryResults.push(vResult[i]);
            }

            if (pf.DataUtil.getArrayLength(vHistoryResults) > maxHistoryResultsRetention) {
                vHistoryResults.splice(maxHistoryResultsRetention, vHistoryResults.length - maxHistoryResultsRetention);
            }
        }

        // 单局已结束的重连 後續的onGameRoundEnd會執行
        // if (notify.curState === network.RoundState.WAIT_NEXT_ROUND) {
        //     this._parseGameRoundEndInfo(notify, false);
        // }

        if (notify.curState === network.RoundState.WAIT_NEXT_ROUND) {
            this.executeGameRoundEnd(notify.cachedNotifyMsg, false);
        }

        this._isDataSynced = true;

        this.emit('dataSync', this);

        // debug purpose
        // notify.CalmDownDeadLineTimeStamp = Date.now() + 100000;
        // notify.CalmDownLeftSeconds = 66;
        this._syncCalmDownParams.fromProto(notify);
        const calmDownParams = new CalmDownParams();
        calmDownParams.fromProto(notify);
        this.emit('calmDown', calmDownParams);
        // pokerMasterDataMgr.getPokerMasterRoom().reset();
        // ?????
        // pokerMasterDataMgr.getPokerMasterRoom().tCurRoom = pokermaster_proto.RoomParam.create(noti.param);
        // ?????
        // pokerMasterDataMgr.getPokerMasterRoom().u32RoomId = noti.param.roomid;
        // ????? 看不到哪邊要用，直接取就好了。
        // pokerMasterDataMgr.getPokerMasterRoom().u32Uid = pf.serviceManager.get(pf.services.AuthService).currentUser.userId;// cv.dataHandler.getUserData().u32Uid;

        // pokerMasterDataMgr.getPokerMasterRoom().bCanAuto = noti.canAuto;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanAdvanceAuto = noti.canAdvanceAuto;
        // pokerMasterDataMgr.getPokerMasterRoom().eCurState = noti.curState;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llCoinUICritical = cv.StringTools.clientGoldByServer(noti.BetButtonLimitAmount);
        // pokerMasterDataMgr.getPokerMasterRoom().uWhoIsLeader = noti.whoIsLeader;
        // pokerMasterDataMgr.getPokerMasterRoom().reachLimitBet = noti.reachLimitBet;
        // if (noti.squintMsg) {
        // pokerMasterDataMgr.getPokerMasterRoom().uWhoIsLeader = noti.squintMsg.whoIsLeader;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanSquint = noti.squintMsg.canSquint;
        // pokerMasterDataMgr.getPokerMasterRoom().bSkipSquint = noti.squintMsg.skipRound;

        // pokerMasterDataMgr.getPokerMasterRoom().fisherLevel = noti.squintMsg.fisherLevel;
        // pokerMasterDataMgr.getPokerMasterRoom().sharkLevel = noti.squintMsg.sharkLevel;
        // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().sharkOuts);
        // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().dashiOuts);
        // let fishOutsLen = noti.squintMsg.fisherOuts.length;
        // let sharkOutsLen = noti.squintMsg.sharkOuts.length;

        // for (let i = 0; i < fishOutsLen; ++i) {
        //     pokerMasterDataMgr.getPokerMasterRoom().dashiOuts.push(pokermaster_proto.OutItem.create(noti.squintMsg.fisherOuts[i]));
        // }

        // for (let i = 0; i < sharkOutsLen; ++i) {
        //     pokerMasterDataMgr.getPokerMasterRoom().sharkOuts.push(pokermaster_proto.OutItem.create(noti.squintMsg.sharkOuts[i]));
        // }
        // }

        // // 运势
        // do {
        //     pokerMasterDataMgr.getPokerMasterRoom().fLeftFortune = noti.fortune.fisherFortune;
        //     pokerMasterDataMgr.getPokerMasterRoom().fRightFortune = noti.fortune.sharkFortune;
        // } while (false);

        // 底牌
        // do {
        //     // 左 - 渔夫
        //     for (let i = 0; i < noti.fisherHoleCards.length; ++i) {
        //         let card_item: pokermaster_proto.CardItem = pokermaster_proto.CardItem.create(noti.fisherHoleCards[i]);
        //         pokerMasterDataMgr.getPokerMasterRoom().vLeftHandCards.push(card_item);
        //     }

        //     // 右 - 鲨鱼
        //     for (let i = 0; i < noti.sharkHoleCards.length; ++i) {
        //         let card_item: pokermaster_proto.CardItem = pokermaster_proto.CardItem.create(noti.sharkHoleCards[i]);
        //         pokerMasterDataMgr.getPokerMasterRoom().vRightHandCards.push(card_item);
        //     }

        //     // 公共牌
        //     for (let i = 0; i < noti.publicCards.length; ++i) {
        //         let card_item: pokermaster_proto.CardItem = pokermaster_proto.CardItem.create(noti.publicCards[i]);
        //         pokerMasterDataMgr.getPokerMasterRoom().vPublicHoleCards.push(card_item);
        //     }
        // } while (false);

        // 赔率信息
        // this._parseGameOddsInfo(notify.oddsOp);

        // // 高级设置/续投
        // do {
        //     // 对应房间级别的下注金额选项(优先取下发的默认值, 若无默认值则取房间参数里面的amountLevel值)
        //     cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vBetCoinOption);
        //     let betcoinoption_size: number = cv.StringTools.getArrayLength(noti.betCoinOption);
        //     if (betcoinoption_size > 0) {
        //         for (let i = 0; i < betcoinoption_size; ++i) {
        //             pokerMasterDataMgr.getPokerMasterRoom().vBetCoinOption.push(noti.betCoinOption[i]);
        //         }
        //     }
        //     else {
        //         let amountlevel_size: number = cv.StringTools.getArrayLength(noti.param.amountLevel);
        //         for (let i = 0; i < amountlevel_size; ++i) {
        //             pokerMasterDataMgr.getPokerMasterRoom().vBetCoinOption.push(noti.param.amountLevel[i]);
        //         }
        //     }

        //     // 续投
        //     pokerMasterDataMgr.getPokerMasterRoom().eAutoLevel = noti.autoLevel;
        //     pokerMasterDataMgr.getPokerMasterRoom().iUsedAutoBetCount = noti.usedAutoBetCount;
        //     pokerMasterDataMgr.getPokerMasterRoom().iSelectAutoBetCount = noti.selectAutoBetCount;

        //     cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vAutoBetCountList);
        //     let autobetcountlist_size: number = cv.StringTools.getArrayLength(noti.AutoBetCountList);
        //     for (let i = 0; i < autobetcountlist_size; ++i) {
        //         pokerMasterDataMgr.getPokerMasterRoom().vAutoBetCountList.push(noti.AutoBetCountList[i]);
        //     }
        // } while (0);

        // 下注区域金币信息
        // do {
        //     for (let i = 0; i < noti.optionInfo.length; ++i) {
        //         // 过滤有效选项
        //         let bet: pokermaster_proto.IBetOptionInfo = noti.optionInfo[i];
        //         if (bet.option === pokermaster_proto.BetZoneOption.FISHER_WIN
        //             || bet.option == pokermaster_proto.BetZoneOption.SHARK_WIN
        //             || bet.option == pokermaster_proto.BetZoneOption.FIVE_NONE_1DUI
        //             || bet.option == pokermaster_proto.BetZoneOption.FIVE_2DUI
        //             || bet.option == pokermaster_proto.BetZoneOption.FIVE_SAN_SHUN_TONG
        //             || bet.option == pokermaster_proto.BetZoneOption.FIVE_GOURD
        //             || bet.option == pokermaster_proto.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {

        //             let zoneData: PokerMasterZoneData = pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.get(bet.option);
        //             if (!zoneData) {
        //                 zoneData = new PokerMasterZoneData();
        //                 pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.add(bet.option, zoneData);
        //             }

        //             zoneData.selfBet = bet.selfBet;
        //             zoneData.totalBet = bet.totalBet;

        //             // 所有人下的金额
        //             cv.StringTools.clearArray(zoneData.vTotalBetDetail);
        //             for (let j = 0; j < cv.StringTools.getArrayLength(bet.amount); ++j) {
        //                 zoneData.vTotalBetDetail.push(bet.amount[j]);
        //             }

        //             // 若该区域自己有下过注,则标记已下注
        //             if (bet.selfBet > 0) {
        //                 pokerMasterDataMgr.getPokerMasterRoom().bHasBetInCurRound = true;
        //             }
        //         }
        //     }
        // } while (0);

        // 玩家信息 - 主界面8个人的游戏信息以及自己(自己永远是第一个，富豪1和神算子排第二和第三)
        // do {
        //     cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vOtherPlayer);
        //     for (let i = 0; i < cv.StringTools.getArrayLength(noti.players); ++i) {
        //         let player: pokermaster_proto.GamePlayer = pokermaster_proto.GamePlayer.create(noti.players[i]);
        //         if (i === 0) {
        //             if (player.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
        //                 pokerMasterDataMgr.getPokerMasterRoom().tSelfPlayer = player;
        //             }
        //             else {
        //                 console.error("PokerMasterSocket.CMD.GAME_DATA_SYN, data error!!!, players[0] must be self!!!");
        //             }
        //         }
        //         else {
        //             pokerMasterDataMgr.getPokerMasterRoom().vOtherPlayer.push(player);
        //         }
        //     }
        // } while (0);

        // 顶栏胜负记录
        // do {
        //     let vLastResult: number[] = pokerMasterDataMgr.getPokerMasterRoom().vLastResult;
        //     let maxHistoryResultsRetention: number = pokerMasterDataMgr.getPokerMasterRoom().nMaxLastResultRetention;

        //     cv.StringTools.clearArray(vLastResult);
        //     for (let i = 0; i < noti.lastResult.length; ++i) {
        //         vLastResult.push(noti.lastResult[i]);
        //         if (cv.StringTools.getArrayLength(vLastResult) > maxHistoryResultsRetention) {
        //             vLastResult.splice(0, 1);
        //         }
        //     }
        // } while (false);

        // 各个区域胜负记录(按时间降序)
        // do {
        //     for (let i = 0; i < cv.StringTools.getArrayLength(noti.optionResults); ++i) {
        //         let eOption: pokermaster_proto.BetZoneOption = noti.optionResults[i].option;
        //         let vResult: number[] = noti.optionResults[i].results;
        //         let iLoseHand: number = noti.optionResults[i].loseHand;

        //         let zoneData: PokerMasterZoneData = pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.get(eOption);
        //         if (!zoneData) {
        //             zoneData = new PokerMasterZoneData();
        //             pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.add(eOption, zoneData);
        //         }

        //         zoneData.luckLoseHand = iLoseHand;
        //         let vHistoryResults: number[] = zoneData.vHistoryResults;
        //         let maxHistoryResultsRetention: number = zoneData.maxHistoryResultsRetention;

        //         cv.StringTools.clearArray(vHistoryResults);
        //         for (let i = 0; i < cv.StringTools.getArrayLength(vResult); ++i) {
        //             vHistoryResults.push(vResult[i]);
        //         }

        //         if (cv.StringTools.getArrayLength(vHistoryResults) > maxHistoryResultsRetention) {
        //             vHistoryResults.splice(maxHistoryResultsRetention, vHistoryResults.length - maxHistoryResultsRetention);
        //         }
        //     }
        // } while (0);

        // // 单局已结束的重连
        // if (noti.curState === pokermaster_proto.RoundState.WAIT_NEXT_ROUND) {
        //     this._parseGameRoundEndInfo(pokermaster_proto.GameRoundEndNotify.create(noti.cachedNotifyMsg), false);
        // }
    }

    private onBetResp(notify: network.IBetResponse) {
        // humanboy cowboy抄來的
        cc.log('CowboyRoom onBetResp', notify);

        // TODO: its a missing feature we did not migrate
        // need to refer to PostCowboyError in CowboyNetWork.ts

        // we will handle the bet 31007 for now
        this.emit('serverError', notify.code);

        // ????? 原版，沒概念...
        // let resp: pokermaster_proto.BetResp = this._parseNetMsg("BetResp", puf, msgID);
        // if (!resp) return;

        // this._postError(resp.code, resp.bill);
        // if(resp.code === pokermaster_proto.ErrorCode.IN_CALM_DOWN){
        //     cv.MessageCenter.send("onCalmDownMsg", resp);
        // }
    }

    getOtherPlayer(uid: number): GamePlayer | undefined {
        return this._otherPlayers.find((palyer) => palyer.uid === uid);
    }

    getPlayer(uid: number): GamePlayer | undefined {
        if (uid === this._selfPlayer.uid) {
            return this._selfPlayer;
        }
        return this._otherPlayers.find((palyer) => palyer.uid === uid);
    }

    protected onKicked(notify: network.IKickNotify) {
        this.emit('kicked', notify);
    }

    protected onTrendNotify(notify: network.IRoomTrendNotice) {
        // console.log('CowboyRoom onTrendNotify', notify);
        const trend = new RoomTrend();
        trend.fromProto(notify);
        this.tFortune.fromProto(notify.fortune); // = PlayerFortune.create(notify.fortune);
        this.emit('trendNotify', trend);

        // 原版
        // let resp: pokermaster_proto.RoomTrendNotice = this._parseNetMsg("RoomTrendNotice", puf, msgID);
        // if (resp) {
        //     let roomData: PokerMasterRoomData = pokerMasterDataMgr.getPokerMasterRoom();
        //     roomData.iLastRow = resp.lastRow;
        //     roomData.iLastCol = resp.lastCol;

        //     let vTrendRoad: any[] = resp.road;
        //     let vTrendData: any[] = resp.trend;
        //     cv.StringTools.clearArray(roomData.vTrendRoad);
        //     cv.StringTools.clearArray(roomData.vTrendData);

        //     for (let i = 0; i < cv.StringTools.getArrayLength(vTrendData); ++i) {
        //         let data: pokermaster_proto.TrendData = vTrendData[i];
        //         roomData.vTrendData.push(data);
        //     }

        //     for (let i = 0; i < cv.StringTools.getArrayLength(vTrendRoad); ++i) {
        //         let road: pokermaster_proto.TrendRoad = vTrendRoad[i];
        //         roomData.vTrendRoad.push(road);
        //     }

        //     roomData.tFortune = pokermaster_proto.PlayerFortune.create(resp.fortune);

        //     this._sendLocalMsg(PokerMasterDef.LocalMsg().UPDATE_TREND);
        // }
    }

    protected onServerError(code: number) {
        this.emit('serverError', code);
    }

    /**

     * 游戏状态变更 - 新开一局
     */
    protected onDeal(notify: network.IDealNotify): void {
        cc.log('CowboyRoom onDeal', notify);
        //  抄來的版本。
        // this.resetRound();
        // this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;
        // this._gameState.leftSeconds = notify.leftSeconds;

        // this._gameState.roundState = network.RoundState.NEW_ROUND;
        // this._betSettings.canAutoBet = notify.canAuto;
        // this._canUpdateWorldServerGold = true;

        // // 更新第一张公共牌
        // // if (notify.card) {
        // //     console.log(notify.card);
        // //     this._roundInfo.addPublicCard(notify.card);
        // // }

        // // 玩家信息
        // this.clonePlayers(notify.players);
        // // 胜负记录
        // this._historyResults = notify.lastResult.slice();

        // // // 判断房间参数变更
        // if (notify.changed) {
        //     this._roomParams.fromProto(notify.param);
        //     this.emit('roomParamChange', this);
        // }

        // this.emit('deal', this);

        // pkw搬過來的版本。
        // let noti: pokermaster_proto.DealNotify = this._parseNetMsg("DealNotify", puf, msgID);
        // ????? 只有看到發送沒看到註冊?
        // cv.MessageCenter.send("onPokerMasterDealNoti");
        // if (!noti) return;

        this.resetRound();
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;
        this._gameState.leftSeconds = notify.leftSeconds;
        this._gameState.roundState = network.RoundState.NEW_ROUND;
        this._betSettings.canAutoBet = notify.canAuto;
        this._canUpdateWorldServerGold = true;
        pf.DataUtil.clearArray(this.roundInfo.blueHandCards);
        pf.DataUtil.clearArray(this.roundInfo.redHandCards);
        // 手牌
        for (let holeCard of notify.playerHoleCard) {
            switch (holeCard.name) {
                // 左 - 渔夫
                case network.RoleName.Fisher:
                    for (let card of holeCard.Cards) {
                        let cardItem: CardItem = CardItem.from(card);
                        this.roundInfo.blueHandCards.push(cardItem);
                    }
                    break;

                // 右 - 鲨鱼
                case network.RoleName.Shark:
                    for (let card of holeCard.Cards) {
                        let cardItem: CardItem = CardItem.from(card);
                        this.roundInfo.redHandCards.push(cardItem);
                    }
                    break;
            }
        }
        // 玩家信息(主界面8个人的游戏信息以及自己(富豪1和神算子排第二和第三))
        pf.DataUtil.clearArray(this._otherPlayers);
        this.clonePlayers(notify.players);
        // for (let i = 0; i < notify.players.length; ++i) {
        //     // let player/* : pokermaster_proto.GamePlayer */ = GamePlayer.create(notify.players[i])
        //     let player = new GamePlayer ();
        //     player.fromProto (notify.players[i]);
        //     if (i === 0) {
        //         if (player.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
        //             this._selfPlayer = player;
        //         }
        //         else {
        //             console.error('PokerMasterSocket.CMD.DEAL_NOTIFY, data error!, players[0] must be self!');
        //         }
        //     }
        //     else {
        //         this._otherPlayers.push(player);
        //     }
        // }
        // 判断房间参数变更
        if (notify.changed) {
            // ?????
            // pokerMasterDataMgr.getPokerMasterRoom().tCurRoom = pokermaster_proto.RoomParam.create(notify.param);
            // this._sendLocalMsg(PokerMasterDef.LocalMsg().ROOM_PARAM_CHANGE);
            this._roomParams.fromProto(notify.param);
            this.emit('roomParamChange', this);
        }

        this.emit('deal', this);

        // pokerMasterDataMgr.getPokerMasterRoom().resetRound();
        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().eCurState = pokermaster_proto.RoundState.NEW_ROUND;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanAuto = noti.canAuto;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanUpdateWorldServerGold = true;

        // 手牌
        // do {
        //     for (let i = 0; i < noti.playerHoleCard.length; ++i) {
        //         switch (noti.playerHoleCard[i].name) {
        //             // 左 - 渔夫
        //             case pokermaster_proto.RoleName.Fisher: {
        //                 for (let j = 0; j < noti.playerHoleCard[i].Cards.length; ++j) {
        //                     let card_item: pokermaster_proto.CardItem = pokermaster_proto.CardItem.create(noti.playerHoleCard[i].Cards[j]);
        //                     pokerMasterDataMgr.getPokerMasterRoom().vLeftHandCards.push(card_item);
        //                 }
        //             } break;

        //             // 右 - 鲨鱼
        //             case pokermaster_proto.RoleName.Shark: {
        //                 for (let j = 0; j < noti.playerHoleCard[i].Cards.length; ++j) {
        //                     let card_item: pokermaster_proto.CardItem = pokermaster_proto.CardItem.create(noti.playerHoleCard[i].Cards[j]);
        //                     pokerMasterDataMgr.getPokerMasterRoom().vRightHandCards.push(card_item);
        //                 }
        //             } break;
        //         }
        //     }
        // } while (false);

        // 玩家信息(主界面8个人的游戏信息以及自己(富豪1和神算子排第二和第三))
        // do {
        //     cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vOtherPlayer);
        //     for (let i = 0; i < noti.players.length; ++i) {
        //         let player: pokermaster_proto.GamePlayer = pokermaster_proto.GamePlayer.create(noti.players[i])
        //         if (i == 0) {
        //             if (player.uid === cv.dataHandler.getUserData().u32Uid) {
        //                 pokerMasterDataMgr.getPokerMasterRoom().tSelfPlayer = player;
        //             }
        //             else {
        //                 console.error("PokerMasterSocket.CMD.DEAL_NOTIFY, data error!, players[0] must be self!");
        //             }
        //         }
        //         else {
        //             pokerMasterDataMgr.getPokerMasterRoom().vOtherPlayer.push(player);
        //         }
        //     }
        // } while (0);

        // // 判断房间参数变更
        // if (noti.changed) {
        //     pokerMasterDataMgr.getPokerMasterRoom().tCurRoom = pokermaster_proto.RoomParam.create(noti.param);
        //     this._sendLocalMsg(PokerMasterDef.LocalMsg().ROOM_PARAM_CHANGE);
        // }
        // this._sendLocalMsg(PokerMasterDef.LocalMsg().STATUS_DEAL);
    }

    private clonePlayers(players: pf.client.session.IGamePlayer[]) {
        players.forEach((item, index) => {
            const player = pf.ValueObject.fromProto(GamePlayer, item);
            if (item.uid === this.userId && index === 0) {
                this._selfPlayer = player;
            } else {
                this._otherPlayers.push(player);
            }
        });
    }
    // ????? humanboy cowboy做法不同，先參考cowboy
    async queryTrend(): Promise<void> {
        return await this._gameSession.queryTrend();
    }

    resetRound(): void {
        // 左右玩家列表
        // this._otherPlayers = [];
        // this._players = [];

        // this._betZones.forEach((zone) => {
        //     zone.reset(false);
        // });

        // 胜负纪录
        // this._historyResults = [];

        this._roundInfo.reset();
        this._canUpdateWorldServerGold = false;

        this._gameState.roundState = network.RoundState.RoundState_DUMMY;
        this._gameState.leftSeconds = 0;
        this._gameState.nextRoundEndStamp = 0;

        this.whoIsLeader = 0;
        this.bCanSquint = false;
        this.bSkipSquint = false;

        this.clearMapZoneData(false);
        // 無人引用拿掉
        // this.nSkipInsureCode = pokermaster_proto.ErrorCode.ErrorCode_DUMMY;

        pf.DataUtil.clearArray(this.sharkOuts);
        pf.DataUtil.clearArray(this.dashiOuts);
    }

    _playerBeforeSettlementCoins = new Map<number, number>();

    private _llCoinUICritical = 0;
    get llCoinUICritical() {
        return this._llCoinUICritical;
        // return null;
    }

    async joinRoom(id?: number): Promise<void> {
        if (id) {
            this._id = id;
        }

        if (this._id > 0) {
            await this._gameSession.joinRoom(this._id, true);
        }
    }

    async leaveRoom(): Promise<void> {
        if (this._gameSession.roomId > 0) {
            this._gameSession
                .leaveRoom()
                .then((resp) => {
                    // ROOM_WITHOUT_YOU means player not in this room already
                    if (resp.code === network.ErrorCode.OK || resp.code === network.ErrorCode.ROOM_WITHOUT_YOU) {
                        this.emit('leaveRoom');
                        this._id = 0;
                    } else {
                        this.emit('serverError', resp.code);
                        this.cancelAdavnceAutoBet();
                    }
                })
                .catch((error) => {
                    cc.warn('[3in1] catch in leaveRoom', error);
                    // rejected because of cleanup...leave room anyway
                    this.emit('leaveRoom');
                    this._id = 0;
                });
        } else {
            this.emit('leaveRoom');
        }
    }

    async getPlayerList(): Promise<pf.services.IGamePlayerList> {
        const resp = await this._gameSession.getPlayerList();
        const players: pf.services.GamePlayer[] = [];
        pf.ValueObjectArray.cloneFromProto(GamePlayer, players, resp.gamePlayers);
        return {
            players,
            playerNum: resp.playerNum
        };
    }

    async setGameOption(autoBetLevel: AutoBetLevel, betCoinOptions: number[]): Promise<void> {
        const resp = await this._gameSession.setGameOption(autoBetLevel, betCoinOptions);
        this._betSettings.autoBetLevel = resp.autoLevel;
        this._betSettings.betCoinOptions = resp.betCoinOption.slice();
        this.emit('betCoinOptionsChange', this._betSettings.betCoinOptions);
    }

    async bet(option: network.BetZoneOption, betAmount: number): Promise<void> {
        return await this._gameSession.bet(option, betAmount);
    }

    async autoBet(): Promise<void> {
        const resp = await this._gameSession.autoBet();
        // // debug purpose
        // // resp.CalmDownDeadLineTimeStamp = Date.now() + 100000;
        // // resp.CalmDownLeftSeconds = 99;
        const calmDownParams = new CalmDownParams();
        calmDownParams.fromProto(resp);
        this.emit('calmDown', calmDownParams);
    }

    /**
     * 请求高级续投
     * ????? 原版
     */
    // reqAdvanceAutoBet(): void {
    //     let data: pokermaster_proto.AdvanceAutoBetReq = pokermaster_proto.AdvanceAutoBetReq.create();
    //     this._sendNetMsg("AdvanceAutoBetReq", data, pokermaster_proto.CMD.ADVANCE_AUTO_BET_REQ, cv.roomManager.getCurrentRoomID());
    // }
    // private _handleAdvanceAutoBetResponse(puf: any, msgID: number): void {
    //     let resp: pokermaster_proto.AdvanceAutoBetRsp = this._parseNetMsg("AdvanceAutoBetRsp", puf, msgID);
    //     if (resp) {
    //         if (resp.code === pokermaster_proto.ErrorCode.OK) {
    //             pokerMasterDataMgr.getPokerMasterRoom().iUsedAutoBetCount = resp.usedAutoBetCount;
    //         }

    //         if(resp.code ==pokermaster_proto.ErrorCode.IN_CALM_DOWN){
    //             cv.MessageCenter.send("onCalmDownMsg", resp);
    //         }else{
    //             cv.MessageCenter.send(PokerMasterDef.LocalMsg().ADVANCE_AUTOBET, resp.code);
    //         }
    //     }
    // }
    async advanceAutoBet(): Promise<void> {
        return new Promise((resolve, reject) => {
            this._gameSession
                .adavnceAutoBet()
                .then((resp) => {
                    this._betSettings.usedAutoBetCount = resp.usedAutoBetCount;
                    this.emit('advanceAutoBet', resp.code);

                    // debug purpose
                    // resp.CalmDownDeadLineTimeStamp = Date.now() + 100000;
                    // resp.CalmDownLeftSeconds = 88;
                    const calmDownParams = new CalmDownParams();
                    calmDownParams.fromProto(resp);
                    this.emit('calmDown', calmDownParams);
                    resolve();
                })
                .catch((err: pf.ServerError) => {
                    this.emit('advanceAutoBet', err.errorCode);
                    reject(err);
                });
        });
    }

    // 投注回顾
    async reqBetReview(): Promise<void> {
        // let data: pb.BetReviewReq = pb.BetReviewReq.create();
        // this._sendNetMsg("BetReviewReq", data, pokermaster_proto.CMD.BET_REVIEW_REQ, cv.roomManager.getCurrentRoomID());
        await this._gameSession.reqBetReview();
    }
    private _handleBetReviewResponse(notify: network.IBetReviewRsp /* puf: any, msgID: number */): void {
        // let resp: pokermaster_proto.BetReviewRsp = this._parseNetMsg("BetReviewRsp", puf, msgID);
        if (notify) {
            // this._postError(resp.code);
            if (notify.code === network.ErrorCode.OK) {
                let betreview /* : any[] */ = notify.reviewed;
                // let roomData: PokerMasterRoomData = pokerMasterDataMgr.getPokerMasterRoom();
                pf.DataUtil.clearArray(this.betReview);

                for (let i = 0; i < pf.DataUtil.getArrayLength(betreview); ++i) {
                    // let reviewed: pokermaster_proto.BetReview = betreview[i];
                    // roomData.vBetReview.push(reviewed);
                    let br = new BetReview();
                    br.fromProto(betreview[i]);
                    this.betReview.push(br);
                }

                // this._sendLocalMsg(PokerMasterDef.LocalMsg().UPDATE_REVIEW);
                this.emit('updateReview');
            } else {
                this.emit('serverError', notify.code);
            }
        }
    }

    async setAdavnceAutoBetCount(count: number): Promise<void> {
        const resp = await this._gameSession.setAdavnceAutoBetCount(count);
        this._betSettings.usedAutoBetCount = 0;
        this._betSettings.selectAutoBetCount = resp.count;
        this.emit('advanceAutoBetCountSet', resp.count);

        const calmDownParams = new CalmDownParams();
        calmDownParams.fromProto(resp);
        this.emit('calmDown', calmDownParams);
    }

    protected onAdvanceAutoBetCancel(notify: network.IAdvanceAutoBetCancelNotify) {
        this._betSettings.usedAutoBetCount = 0;
        this._betSettings.selectAutoBetCount = 0;
        this._betSettings.reachLimitBet = false;

        this.emit('advanceAutoBetCancel', notify.code, notify.is_manual);
    }

    async cancelAdavnceAutoBet(): Promise<void> {
        await this._gameSession.cancelAdavnceAutoBet();
    }

    async addAdavnceAutoBetCount(count: number): Promise<void> {
        const resp = await this._gameSession.addAdavnceAutoBetCount(count);

        switch (resp.code) {
            case network.ErrorCode.OK:
                this._betSettings.usedAutoBetCount = resp.usedAutoBetCount;
                this._betSettings.selectAutoBetCount = resp.autoBetCount;
                this.emit('advanceAutoBetCountAdd', resp.usedAutoBetCount, resp.autoBetCount);
                break;
            case network.ErrorCode.REACH_LIMIT_BET:
                this._betSettings.usedAutoBetCount = resp.usedAutoBetCount;
                this._betSettings.selectAutoBetCount = resp.autoBetCount;
                this._betSettings.reachLimitBet = true;
                this.emit('advanceAutoBetCountAdd', resp.usedAutoBetCount, resp.autoBetCount);
                this.emit('advanceAutoBetLimitReached', resp.numberHandAdded);
                console.log('advanceAutoBetLimitReached');
                break;

            case network.ErrorCode.IN_CALM_DOWN:
                this.emit('calmDown', new CalmDownParams());
                break;

            default:
                break;
        }
    }

    /**
     * 获取玩家结算前的总金币
     * @param uid
     */
    getPlayerBeforeSettlementGold(uid: number): number {
        let amount = 0;
        if (this._playerBeforeSettlementCoins.has(uid)) {
            amount = this._playerBeforeSettlementCoins.get(uid);
        }
        return amount;
    }

    /**
     * 设置玩家结算前的总金币
     * @param SEUInt32
     * @param uid
     * @param SEInt64
     * @param amount
     */
    setPlayerBeforeSettlementGold(uid: number, amount: number): void {
        this._playerBeforeSettlementCoins.set(uid, amount);
    }

    /**
     * 转化获取指定金额(超过 10 的 exp 次幂, 显示 xxx 万, 百万, 十亿)
     * @param gold          数值
     * @param exp           底数为10的指数
     */
    transGoldShortString(gold: number, exp = 4): string {
        let retValue = '';
        let bNegative = false;

        let _gold = gold;

        if (_gold < 0) {
            bNegative = true;
            _gold = -_gold;
        }

        let formatCoin: number = cr.CurrencyUtil.convertServerAmountToDisplayNumber(_gold);
        let fMillion = Math.pow(10, 6);
        let fBillion = Math.pow(10, 9);
        let fTenThousand = Math.pow(10, 4);

        if (formatCoin >= Math.pow(10, exp)) {
            // 100亿
            if (formatCoin >= 10 * fBillion) {
                retValue =
                    cr.CurrencyUtil.convertNumberToPrecisionString(_gold / fBillion) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_billion');
            }
            // 亿
            else if (formatCoin >= 100 * fMillion) {
                retValue =
                    cr.CurrencyUtil.convertNumberToPrecisionString(_gold / fMillion) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_million');
            }
            // 万
            else if (formatCoin >= fTenThousand) {
                retValue =
                    cr.CurrencyUtil.convertNumberToPrecisionString(_gold / fTenThousand) +
                    pf.languageManager.getString('Humanboy_game_gold_short_suffix_w');
            } else {
                retValue = cr.CurrencyUtil.convertNumberToPrecisionString(_gold);
            }
        } else {
            retValue = cr.CurrencyUtil.convertNumberToPrecisionString(_gold);
        }

        if (bNegative) {
            retValue = '-' + retValue;
        }

        return retValue;
    }

    protected onGameRoundEnd(notify: network.IGameRoundEndNotify) {
        this.executeGameRoundEnd(notify, true);
    }

    protected executeGameRoundEnd(notify: network.IGameRoundEndNotify, isRoundEnd: boolean) {
        // this.parseGameRoundInfo(notify);

        // this._historyResults.push(RoundResult.mapToBetZoneOption(notify.roundResult.result));
        // this._historyResults.push(notify.roundResult.result);

        // 更新本局区域输赢结果
        notify.optionResult?.forEach((optionResult) => {
            const zone = this._betZones.get(optionResult.option);
            zone.optionResult.luckLoseHand = optionResult.loseHand;
            zone.optionResult.result = optionResult.result;
        });

        this._gameState.leftSeconds = notify.leftSeconds;
        this._gameState.nextRoundEndStamp = notify.nextRoundEndStamp;

        this._gameState.roundState = network.RoundState.WAIT_NEXT_ROUND;

        this._canUpdateWorldServerGold = false;

        this._parseGameRoundEndInfo(notify, isRoundEnd);

        cc.log('PokerMasterService onGameRoundEnd', notify);

        this.emit('gameRoundEnd', this);

        // 原版
        // let noti: pokermaster_proto.GameRoundEndNotify = this._parseNetMsg("GameRoundEndNotify", puf, msgID);
        // if (!noti) return;

        // pokerMasterDataMgr.getPokerMasterRoom().llNextRoundEndStamp = noti.nextRoundEndStamp;
        // pokerMasterDataMgr.getPokerMasterRoom().llLeftSeconds = noti.leftSeconds;
        // pokerMasterDataMgr.getPokerMasterRoom().eCurState = pokermaster_proto.RoundState.WAIT_NEXT_ROUND;
        // pokerMasterDataMgr.getPokerMasterRoom().bCanUpdateWorldServerGold = false;
        // ????? 後面這三個不確定
        // this._parseGameRoundEndInfo(noti, true);
        // this._sendLocalMsg(PokerMasterDef.LocalMsg().STATUS_ROUND_END);
        // cv.MessageCenter.send('PokerStarGameRoundEnd');
    }

    /**
     * 解析"结算"信息
     * @param noti
     * @param isRoundEnd
     */
    private _parseGameRoundEndInfo(noti: network.IGameRoundEndNotify, isRoundEnd: boolean): void {
        this.roundInfo.changePoints = noti.change_points;
        this.fLeftFortune = noti.fortune.fisherFortune;
        this.fRightFortune = noti.fortune.sharkFortune;

        this.roundInfo.roundResult = RoundResult.from(noti.roundResult);
        this.roundInfo.playerSettles.clear();
        for (let ps of noti.playerSettle) {
            let t: PlayerSettle = new PlayerSettle();
            t.fromProto(ps);
            // pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles.push(t);
            this.roundInfo.playerSettles.set(ps.uid, t);
        }

        this.updatePlayerSettleKeepWinCountAndCoin();
        let other = new PlayerSettle();
        other.fromProto(noti.otherPlayers);
        this.roundInfo.otherPlayersSettle = other;

        // 一手结束, 非结束阶段进入房间才push
        if (isRoundEnd) {
            // 顶栏输赢结果
            let vLastResult: network.BetZoneOption[] = this.roundInfo.vLastResult;
            let maxHistoryResultsRetention: number = this.roundInfo.nMaxLastResultRetention;
            vLastResult.push(noti.roundResult.winOp);
            if (pf.DataUtil.getArrayLength(vLastResult) > maxHistoryResultsRetention) {
                vLastResult.splice(0, 1);
            }

            // 各个区域输赢结果
            for (let or of noti.optionResult) {
                // for (let i = 0; i < noti.optionResult.length; ++i) {
                let eOption: network.BetZoneOption = or.option;
                let iResult: number = or.result;
                let iLoseHand: number = or.loseHand;

                let zoneData /* : PokerMasterZoneData */ = this.betZones.get(eOption); // pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.get(eOption);
                if (!zoneData) {
                    zoneData = new BetZone(eOption); // new PokerMasterZoneData();
                    this.betZones.set(eOption, zoneData); // pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.add(eOption, zoneData);
                }

                // 更新区域输赢结果
                // zoneData.optionResult.result = iResult;// zoneData.result = iResult;
                zoneData.optionResult.luckLoseHand = iLoseHand; // zoneData.luckLoseHand = iLoseHand;

                // 插入历史胜负记录(按时间降序)
                let vHistoryResults: number[] = zoneData.optionResult.historyResults; // zoneData.vHistoryResults;
                let maxHistoryResultsRetention: number = zoneData.maxHistoryResultsRetention;

                vHistoryResults.unshift(iResult);
                if (vHistoryResults.length > maxHistoryResultsRetention) {
                    vHistoryResults.splice(
                        maxHistoryResultsRetention,
                        vHistoryResults.length - maxHistoryResultsRetention
                    );
                }
            }
        }

        this.roundInfo.stopWorld = noti.stopWorld;
        this.roundInfo.idleRoomId = noti.idle_roomid;
        // pokerMasterDataMgr.getPokerMasterRoom().change_points = noti.change_points;
        // 运势
        // do {
        //     pokerMasterDataMgr.getPokerMasterRoom().fLeftFortune = noti.fortune.fisherFortune;
        //     pokerMasterDataMgr.getPokerMasterRoom().fRightFortune = noti.fortune.sharkFortune;
        // } while (false);

        // 比牌结果
        // do {
        //     pokerMasterDataMgr.getPokerMasterRoom().tRoundresult = pokermaster_proto.RoundResult.create(noti.roundResult);
        //     console.log("boob =>");
        // } while (false);

        // 主界面8个人的输赢情况和自己的结算输赢(如果在8个人里面只下发一个)
        // do {
        //     // cv.StringTools.clearArray(pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles);
        //     for (let i = 0; i < noti.playerSettle.length; ++i) {
        //         let t: pokermaster_proto.PlayerSettle = pokermaster_proto.PlayerSettle.create(noti.playerSettle[i]);
        //         pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles.push(t);
        //     }

        //     // 把结算数据更新到玩家数据中
        //     pokerMasterDataMgr.getPokerMasterRoom().updatePlayerSettleKeepWinCountAndCoin();
        // } while (0);

        // 除主界面8个人输赢外其它玩家列表的输赢
        // do {
        //     pokerMasterDataMgr.getPokerMasterRoom().tOtherPlayerSettle = pokermaster_proto.PlayerSettle.create(noti.otherPlayers);
        // } while (0);

        // 一手结束, 非结束阶段进入房间才push
        // if (isRoundEnd) {
        //     // 顶栏输赢结果
        //     let vLastResult: pokermaster_proto.BetZoneOption[] = pokerMasterDataMgr.getPokerMasterRoom().vLastResult;
        //     let maxHistoryResultsRetention: number = pokerMasterDataMgr.getPokerMasterRoom().nMaxLastResultRetention;
        //     vLastResult.push(noti.roundResult.winOp);
        //     if (cv.StringTools.getArrayLength(vLastResult) > maxHistoryResultsRetention) {
        //         vLastResult.splice(0, 1);
        //     }

        //     // 各个区域输赢结果
        //     for (let i = 0; i < noti.optionResult.length; ++i) {
        //         let eOption: pokermaster_proto.BetZoneOption = noti.optionResult[i].option;
        //         let iResult: number = noti.optionResult[i].result;
        //         let iLoseHand: number = noti.optionResult[i].loseHand;

        //         let zoneData: PokerMasterZoneData = pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.get(eOption);
        //         if (!zoneData) {
        //             zoneData = new PokerMasterZoneData();
        //             pokerMasterDataMgr.getPokerMasterRoom().mapZoneData.add(eOption, zoneData);
        //         }

        //         // 更新区域输赢结果
        //         zoneData.result = iResult;
        //         zoneData.luckLoseHand = iLoseHand;

        //         // 插入历史胜负记录(按时间降序)
        //         let vHistoryResults: number[] = zoneData.vHistoryResults;
        //         let maxHistoryResultsRetention: number = zoneData.maxHistoryResultsRetention;

        //         vHistoryResults.unshift(iResult);
        //         if (vHistoryResults.length > maxHistoryResultsRetention) {
        //             vHistoryResults.splice(maxHistoryResultsRetention, vHistoryResults.length - maxHistoryResultsRetention);
        //         }
        //     }
        // }

        // 非0代表系统即将维护
        // pokerMasterDataMgr.getPokerMasterRoom().nStopWorld = noti.stopWorld;
        // pokerMasterDataMgr.getPokerMasterRoom().idle_roomid = noti.idle_roomid;
    }

    /**
     * 更新单据结束连赢情况和金币数量
     */
    updatePlayerSettleKeepWinCountAndCoin(): void {
        this.roundInfo.playerSettles.forEach((value, key) => {
            // let t: pokermaster_proto.PlayerSettle = this.vPlayerSettles[i];
            if (value.uid === this.selfPlayer.uid) {
                this.selfPlayer.curCoin = value.curCoin;
                this.selfPlayer.keepWinCount = value.keepWinCount;
            }

            this._otherPlayers.forEach((it) => {
                if (value.uid === it.uid) {
                    it.curCoin = value.curCoin;
                    it.keepWinCount = value.keepWinCount;
                }
            });
        });
        // for (let i = 0; i < this.vPlayerSettles.length; ++i) {
        //     let t: pokermaster_proto.PlayerSettle = this.vPlayerSettles[i];
        //     if (t.uid == this.tSelfPlayer.uid) {
        //         this.tSelfPlayer.curCoin = t.curCoin;
        //         this.tSelfPlayer.keepWinCount = t.keepWinCount;
        //     }

        //     this.vOtherPlayer.forEach(it => {
        //         if (t.uid == it.uid) {
        //             it.curCoin = t.curCoin;
        //             it.keepWinCount = t.keepWinCount;
        //         }
        //     });
        // }
    }

    private parseGameRoundInfo(notify: network.IGameRoundEndNotify) {
        this._roundInfo.from(notify);

        // this.redHandCards = [];
        // this.blueHandCards = [];
        // notify.playerHoleCard.forEach((playerHandCard) => {
        //     if (playerHandCard.name === network.RoleName.Red) {
        //         this.redHandCards = CardItem.map(playerHandCard.Cards);
        //     } else if (playerHandCard.name === network.RoleName.Blue) {
        //         this.blueHandCards = CardItem.map(playerHandCard.Cards);
        //     }
        // });

        // this.publicCards = [];
        // this.publicCards = CardItem.map(notify.publicCards);

        // 更新单据结束连赢情况和金币数量
        notify.playerSettle.forEach((item) => {
            this.updatePlayerKeepWinCount(item.uid, item.curCoin, item.keepWinCount);
        });
    }

    // 更新连赢次数
    private updatePlayerKeepWinCount(uid: number, curCoin: number, keepWinCount: number): void {
        if (uid === this._selfPlayer.uid) {
            this._selfPlayer.curCoin = curCoin;
            this._selfPlayer.keepWinCount = keepWinCount;
        }

        this._otherPlayers.forEach((player) => {
            if (player.uid === uid) {
                player.curCoin = curCoin;
                player.keepWinCount = keepWinCount;
            }
        });
    }

    protected onMergeAutoBet(notify: network.IMergeAutoBetNotify) {
        const bets: PlayerOneBet[] = [];
        notify.notify.forEach((betNotify) => {
            this.handleBetNofify(betNotify);

            const bet = new PlayerOneBet(betNotify.uid, betNotify.detail.option, betNotify.detail.betAmount);
            bets.push(bet);
        });

        this.emit('autoBet', bets);
        cc.log('PokerMasterService on autoBet');

        // 原版
        // let noti: pokermaster_proto.MergeAutoBetNotify = this._parseNetMsg("MergeAutoBetNotify", puf, msgID);
        // if (!noti) return;

        // for (let not of notify.notify) {

        //     let oneBetNotify: pb.BetNotify = new pb.BetNotify(not);
        //     this.updatePlayerCoin(oneBetNotify.uid, oneBetNotify.curCoin);
        //     this.updateAreaBet(oneBetNotify);
        //     if (oneBetNotify.uid === pf.serviceManager.get(pf.services.AuthService).currentUser.userId) {
        //         this.roundInfo.hasBetInCurRound = true;
        //     }
        // }// 'mergeAutoBet'

        // this.emit('mergeAutoBet', notify);
        // this._sendLocalMsg(PokerMasterDef.LocalMsg().AUTO_BET_MERGE, noti);
    }

    protected onLeftGameCoin(notify: network.ILeftGameCoinNotify) {
        this.emit('leftGameCoin', notify);
    }
}
