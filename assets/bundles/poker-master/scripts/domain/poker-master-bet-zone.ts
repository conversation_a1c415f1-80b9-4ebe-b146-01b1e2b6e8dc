import * as pf from '../../../../poker-framework/scripts/pf';
import * as network from '../network/poker-master-network-index';

import BetZoneOption = network.BetZoneOption;

export class BetOptionInfo {
    selfBet: number;
    totalBet: number;
    amounts: number[];

    constructor(other?: network.IBetOptionInfo) {
        this.from(other);
    }

    from(other?: network.IBetOptionInfo) {
        this.selfBet = other?.selfBet ?? 0;
        this.totalBet = other?.totalBet ?? 0;
        this.amounts = other?.amount?.slice() ?? [];
    }

    reset(): void {
        this.from();
    }

    static form(other?: network.IBetOptionInfo): BetOptionInfo {
        return new BetOptionInfo(other);
    }
}

export class BetOptionResult {
    static readonly MAX_HISTORY_RESULTS_RETENTION = 30; // 最大保留的历史记录数量

    luckLoseHand: number; // 幸运一击区, 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
    historyResults: number[]; // 该区域最近的胜负记录
    private _result: number; // 该区域输赢(0 - 未击中, 1 - 击中)

    constructor(other?: network.IOptionResults) {
        this.from(other);
    }

    from(other?: network.IOptionResults) {
        this._result = 0;
        this.luckLoseHand = other?.loseHand ?? 0;
        this.historyResults =
            other?.results?.slice(0, Math.min(BetOptionResult.MAX_HISTORY_RESULTS_RETENTION, other.results.length)) ??
            [];
    }
    get result(): number {
        return this._result;
    }
    set result(value: number) {
        this._result = value;
        // this.historyResults.unshift(value);
        // if (this.historyResults.length > BetOptionResult.MAX_HISTORY_RESULTS_RETENTION) {
        //     this.historyResults.pop();
        // }
    }

    static from(other: network.IOptionResults): BetOptionResult {
        return new BetOptionResult(other);
    }
}

export class BetZone {
    odds: number = 0;
    option: BetZoneOption = BetZoneOption.BetZoneOption_DUMMY;
    optionInfo: BetOptionInfo;
    optionResult: BetOptionResult;
    // option: pokermaster_proto.BetZoneOption = pokermaster_proto.BetZoneOption.BetZoneOption_DUMMY;                      // 该区域对应枚举
    // result: number = 0;				=== BetOptionResult.result								                        // 该区域输赢(1 赢 0 输)
    // totalBet: number = 0;			=== BetOptionInfo.totalBet								                        // 该区域总下注
    // selfBet: number = 0;				=== BetOptionInfo.selfBet								                        // 该区域自己下注
    // odds: number = 0;                                                                                                   // 该区域赔率
    limitBet: number = 0; // 该区域下注限红
    maxHistoryResultsRetention: number = 30; // 该区域最大保留的历史记录数量
    // luckLoseHand: number = 0;		=== BetOptionResult.luckLoseHand						                        // 该区域连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
    // vTotalBetDetail: number[] = [];  === BetOptionInfo.amounts                                                       // 该区域总下注详情
    // vHistoryResults: number[] = [];	=== BetOptionResult.historyResults

    constructor(option: BetZoneOption) {
        this.odds = 0;
        this.option = option;
        this.optionInfo = new BetOptionInfo();
        this.optionResult = new BetOptionResult();
    }

    reset(clearCache: boolean): void {
        this.optionInfo.reset();
        // this.optionResult.result = 0;
        this.odds = 0;
        this.limitBet = 0;
        this.option = BetZoneOption.BetZoneOption_DUMMY;
        pf.DataUtil.clearArray(this.optionInfo.amounts);
        // cv.StringTools.clearArray(this.vTotalBetDetail);

        if (clearCache) {
            this.optionResult.luckLoseHand = 0;
            // pf.CollectionUtil.clearArray(this.optionResult.historyResults);
        }
    }
}
