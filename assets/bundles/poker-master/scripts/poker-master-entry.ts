import * as pf from '../../../poker-framework/scripts/pf';
import registerEntry = pf.registerEntry;
import * as network from './network/poker-master-network-index';
import { macros } from './common/poker-master-macros';
import { PokerMasterService } from './domain/poker-master-service';
import type { PokerMasterRoom } from './domain/poker-master-room';

@registerEntry(macros.BUNDLE_NAME)
export class PokerMasterEntry extends pf.BundleEntryBase {
    private _socket: pf.Nullable<pf.client.ISocket> = null;
    private _pokerMasterService: pf.Nullable<PokerMasterService> = null;
    private _room: pf.Nullable<PokerMasterRoom> = null;

    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_GAME;
    }

    protected getLanguageStringPath() {
        let path = macros.Language_String_Path.ZH_CN;
        switch (pf.languageManager.currentLanguage) {
            case pf.LANGUAGE_GROUPS.en_US:
                path = macros.Language_String_Path.EN_US;
                break;
            case pf.LANGUAGE_GROUPS.yn_TH:
                path = macros.Language_String_Path.YN_TH;
                break;
            case pf.LANGUAGE_GROUPS.th_PH:
                path = macros.Language_String_Path.TH_PH;
                break;
            case pf.LANGUAGE_GROUPS.hi_IN:
                path = macros.Language_String_Path.HI_IN;
                break;
        }
        return path;
    }

    protected getAddressableConfigPath() {
        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
            ? macros.Addressable_Config_Path.ZH_CN
            : macros.Addressable_Config_Path.EN_US;
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        try {
            cc.log(`bundle ${macros.BUNDLE_NAME} onEnter`);

            await this.loadConfigs();

            const context = pf.app.getGameContext<pf.services.MiniGameContext>();

            context.bundle = macros.BUNDLE_NAME;

            this._socket = context?.socket;

            if (!this._socket) {
                return Promise.reject(new pf.InvalidParameterError('options.socket is null or undefined!'));
            }

            if (options.roomId === undefined) {
                return Promise.reject(new pf.InvalidParameterError('options.roomId is undefined!'));
            }
            context.gameId = pf.client.GameId.PokerMaster;
            cc.log('create pkw pokerMaster session');
            const pokerMasterSession = this._socket.createGameSession(
                network.PKWPokerMasterSession,
                pf.client.GameId.PokerMaster
            );

            this._pokerMasterService = new PokerMasterService(pokerMasterSession);
            pf.serviceManager.register(this._pokerMasterService);

            // store mini game state to game context
            // context.gameId = pf.client.GameId.PokerMaster;
            context.session = pokerMasterSession;
            context.service = this._pokerMasterService;

            cc.log(`login ${macros.BUNDLE_NAME}`);
            await this._pokerMasterService.login();

            this._room = (await this._pokerMasterService.joinRoom(options.roomId)) as PokerMasterRoom;
            cc.log(`${macros.BUNDLE_NAME} join room`);
            context.room = this._room;
            context.roomId = this._room.id;

            // download assets
            const assetLoader = new pf.AddressalbeAssetLoader();
            // no need to load assets while startup...comment out for now
            assetLoader.addLoadAddressableGroupTask('poker-master');

            // NOTE:
            // make address progress not over 20%
            let addressalbeTotalCount = 0;

            await assetLoader.start((finish, total) => {
                if (addressalbeTotalCount === 0) {
                    addressalbeTotalCount = total * 5;
                }
                options?.onProgress(finish, addressalbeTotalCount);
            });

            const asyncOp = new pf.AsyncOperation<void>();

            cc.log('load scene PokerMasterScene');
            this.bundle.loadScene(
                'PokerMasterScene',
                (finish, total) => {
                    options?.onProgress(
                        finish + assetLoader.totalCount,
                        Math.max(total + assetLoader.totalCount, addressalbeTotalCount)
                    );
                },
                (err, scene) => {
                    if (err) {
                        cc.warn(err);
                        asyncOp.reject(err);
                    } else {
                        cc.log('run scene PokerMasterScene');
                        cc.director.runScene(
                            scene,
                            () => {
                                if (this.onBeforeLoadScene) {
                                    this.onBeforeLoadScene();
                                }
                            },
                            () => {
                                asyncOp.resolve();
                            }
                        );
                    }
                }
            );

            return asyncOp.promise;
        } catch (err) {
            cc.warn(`login ${macros.BUNDLE_NAME} failed: ${(err as Error).message})`);

            const context = pf.app.getGameContext<pf.services.MiniGameContext>();

            if (this._pokerMasterService) {
                pf.serviceManager.unregister(PokerMasterService);
                this._socket.removeGameSession(network.PKWPokerMasterSession);
                this._pokerMasterService = null;
            }

            throw err;
        }
    }

    protected async onPreload(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload`);
        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('poker-master-dynamic');
        await assetLoader.startPreload();
        cc.log(`bundle ${macros.BUNDLE_NAME} onPreload done`);
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${macros.BUNDLE_NAME} onExit`);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        // moved to PokerMasterControl::tryLeaveRoom()
        // try {
        //     await this._room.leaveRoom();
        // } catch (err) {
        //     cc.warn(err);
        // }
        pf.serviceManager.unregister(PokerMasterService);

        this._room = null;
        this._pokerMasterService = null;

        this._socket?.removeGameSession(network.PKWPokerMasterSession);

        this._socket = null;

        context.bundle = '';
        context.session = null;
        context.service = null;
        context.room = null;
        context.exitCallback = null;
    }

    onUnload(): void {
        cc.log(`bundle ${macros.BUNDLE_NAME} onUnload`);
    }

    // protected async loginGame(context: pf.services.MiniGameContext, roomId: number): Promise<void> {
    //     try {
    //         // const pokerMasterSession = this._socket.createGameSession(network.PokerMasterSession);
    //         let pokerMasterSession;
    //         if (context.platform === 'pkw') {
    //             console.log('create pkw PokerMaster session');
    //             pokerMasterSession = this._socket.createGameSession(network.PKWPokerMasterSession);
    //         } else if (context.platform === 'wpk') {
    //             console.log('create wpk PokerMaster session');
    //             pokerMasterSession = this._socket.createGameSession(network.WPKPokerMasterSession);
    //         }

    //         this._pokerMasterService = new PokerMasterService(pokerMasterSession);
    //         pf.serviceManager.register(this._pokerMasterService);

    //         cc.log(`login ${macros.BUNDLE_NAME}`);
    //         await this._pokerMasterService.login();

    //         cc.log(`${macros.BUNDLE_NAME} join room`);
    //         this._room = (await this._pokerMasterService.joinRoom(roomId)) as PokerMasterRoom;

    //         // store mini game state to game context
    //         context.bundle = macros.BUNDLE_NAME;
    //         context.gameId = pf.client.GameId.PokerMaster;
    //         context.session = pokerMasterSession;
    //         context.service = this._pokerMasterService;
    //         context.room = this._room;
    //         context.roomId = this._room.id;
    //     } catch (err) {
    //         // NOTE:
    //         // Do not throw error to prevent exit bundle when login fail.
    //         // Let GameSession re join room when socket reconnect
    //         cc.warn(`login ${macros.BUNDLE_NAME} failed: ${(err as Error).message})`);

    //         if (this._pokerMasterService) {
    //             pf.serviceManager.unregister(PokerMasterService);
    //             // this._socket.removeGameSession(network.PokerMasterSession);
    //             if (context.platform === 'pkw') {
    //                 this._socket.removeGameSession(network.PKWPokerMasterSession);
    //             } else if (context.platform === 'wpk') {
    //                 this._socket.removeGameSession(network.WPKPokerMasterSession);
    //             }
    //             this._pokerMasterService = null;
    //         }

    //         throw err;
    //     }
    // }
}
