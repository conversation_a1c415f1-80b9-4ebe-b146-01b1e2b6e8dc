import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/poker-master-macros';
import { PokerMasterDef } from '../../common/poker-master-define';
import { HumanboyPlayerInfo, PokerMasterControl } from './PokerMasterControl';
import * as common from 'common';

import AvatarControl = common.components.AvatarControl;
import { DialogHubControl } from '../../../../common-resource/scripts/components/DialogHubControl';
import { CommonDialogControl } from '../../../../common-resource/scripts/components/CommonDialogControl';

const { ccclass, property } = cc._decorator;

@ccclass
export default class PokerMasterControlForWPK extends PokerMasterControl {
    private _dialogHub: DialogHubControl;
    private _commonDialog: CommonDialogControl;
    /**
     * 充值
     * @param event
     */
    protected recharge(): void {
        let PokerMasterNodeRecharge = this.node.getChildByName('PokerMaster_nodeRecharge');
        if (PokerMasterNodeRecharge) {
            PokerMasterNodeRecharge.active = true;
        } else {
            const nodeRecharge: cc.Node = cc.instantiate(
                pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_RECHARGE)
            );
            nodeRecharge.name = 'PokerMaster_nodeRecharge';
            this.node.addChild(nodeRecharge, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING);
        }
    }

    /**
     * 更新所有路单
     * @param reduce
     */
    protected _updateAllWayOut(reduce: number = 0): void {
        super._updateAllWayOut(reduce);

        this._updateTopWayOut();
    }

    protected _getCoinRandomPos(coin: cc.Node, nAreaIdx: number, bWorldPos: boolean): cc.Vec2 {
        if (!coin || nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return cc.Vec2.ZERO;
        let szPanel: cc.Size = this._vAreasInfo[nAreaIdx].panelCoin.getContentSize();
        let szCoin: cc.Size = cc.size(cc.Size.ZERO);
        szCoin.width = coin.width * coin.scaleX;
        szCoin.height = coin.height * coin.scaleY;

        let halfW: number = szPanel.width / 2;
        let halfH: number = szPanel.height / 2;

        let offsetX: number = Math.floor(halfW - szCoin.width / 2);
        let offsetY: number = Math.floor(halfH - szCoin.height / 2);

        // 以中心锚点为原点, 按照方圆随机位置
        let signX: number = pf.RandomUtil.range(0, 2) < 1 ? -0.95 : 0.95;
        let signY: number = pf.RandomUtil.range(0, 2) < 1 ? -1 : 1;
        let x: number = signX * pf.RandomUtil.range(0, offsetX);
        let y: number = signY * pf.RandomUtil.range(0, offsetY);

        let retPos: cc.Vec2 = cc.v2(x, y);
        if (bWorldPos) {
            this._vAreasInfo[nAreaIdx].panelCoin.convertToWorldSpaceAR(retPos, retPos);
        }
        retPos.x += 12;
        return retPos;
    }

    /**
     * 初始化玩家列表信息
     */
    protected _initPlayersInfo(): void {
        // 自己
        do {
            this._txtSelfName = this._panelSelf.getChildByName('txt_name').getComponent(cc.Label);
            this._txtSelfGold = this._panelSelf.getChildByName('txt_gold').getComponent(cc.Label);
            this._imgSelfGold = this._panelSelf.getChildByName('img_gold').getComponent(cc.Sprite);
            this._imgSelfHead = this._panelSelf.getChildByName('img_head').getComponent(cc.Sprite);
            this._selfAvatar = this._panelSelf.getChildByName('Avatar').getComponent(AvatarControl);

            // 设置默认头像框
            // cv.resMgr.setSpriteFrame(this._img_self_head.node, 'zh_CN/game/humanboy/head/head_player_box_circle');

            // 充值
            let btnRecharge: cc.Node = this._panelSelf.getChildByName('btn_recharge');
            btnRecharge.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                // cv.MessageCenter.send(PokerMasterDef.LocalMsg().MsgPrefix + PokerMasterDef.LocalMsg().RECHARGE);
                this.recharge();
            });
        } while (false);
        this.setLeftAndRightList();
        // 其他玩家
        do {
            // let panelLeftPlayerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
            // let panelRightPlayerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');
            let listLen: number = this.ispad ? 5 : 4;
            for (let i = 0; i < listLen; ++i) {
                // 左列表(富豪榜)
                do {
                    let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                    player.imgBg = this._panelLeftPlayerlist
                        .getChildByName(pf.StringUtil.formatC('img_bg_%d', i))
                        .getComponent(cc.Sprite);
                    player.nodeHead = this._panelLeftPlayerlist.getChildByName(
                        pf.StringUtil.formatC('node_head_%d', i)
                    );
                    player.txtCoin = this._panelLeftPlayerlist
                        .getChildByName(pf.StringUtil.formatC('text_coin_%d', i))
                        .getComponent(cc.Label);
                    const avatar = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.AVATAR));
                    player.nodeHead.addChild(avatar);
                    player.avatarControl = avatar.getComponent(AvatarControl);

                    if (i === 0) {
                        let nbFlagNode = player.nodeHead.getChildByName('nb_flag');
                        player.imgFlag = nbFlagNode.getComponent(cc.Sprite);
                        let nbFlagDesc: cc.Label = player.imgFlag.node
                            .getChildByName('nb_flag_desc')
                            .getComponent(cc.Label);
                        nbFlagDesc.string = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_fuhao_no_text'),
                            1
                        );
                        // 把 nbFlagNode 的 siblingIndex 设置为 1, 使得头像在 nbFlagNode 的下面
                        player.nodeHead.insertChild(nbFlagNode, 1);
                    }

                    if (this.ispad) {
                        if (i === listLen - 1) {
                            player.imgBg.node.active = true;
                            player.nodeHead.active = true;
                            player.txtCoin.node.active = true;
                        }
                    }

                    this._vOtherPlayerInfo.push(player);
                } while (false);

                // 右列表(神算子)
                do {
                    let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                    player.imgBg = this._panelRightPlayerlist
                        .getChildByName(pf.StringUtil.formatC('img_bg_%d', i))
                        .getComponent(cc.Sprite);
                    player.nodeHead = this._panelRightPlayerlist.getChildByName(
                        pf.StringUtil.formatC('node_head_%d', i)
                    );
                    player.txtCoin = this._panelRightPlayerlist
                        .getChildByName(pf.StringUtil.formatC('text_coin_%d', i))
                        .getComponent(cc.Label);
                    const avatar = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.AVATAR));
                    player.nodeHead.addChild(avatar);
                    player.avatarControl = avatar.getComponent(AvatarControl);

                    if (i === 0) {
                        let nbFlagNode = player.nodeHead.getChildByName('nb_flag');
                        player.imgFlag = nbFlagNode.getComponent(cc.Sprite);
                        let nbFlagDesc: cc.Label = player.imgFlag.node
                            .getChildByName('nb_flag_desc')
                            .getComponent(cc.Label);
                        nbFlagDesc.string = pf.languageManager.getString('Cowboy_shensuanzi_text');
                        // 把 nbFlagNode 的 siblingIndex 设置为 1, 使得头像在 nbFlagNode 的下面
                        player.nodeHead.insertChild(nbFlagNode, 1);
                    }

                    if (this.ispad) {
                        if (i === listLen - 1) {
                            player.imgBg.node.active = true;
                            player.nodeHead.active = true;
                            player.txtCoin.node.active = true;
                        }
                    }

                    this._vOtherPlayerInfo.push(player);
                } while (false);
            }

            // 头像
            for (let playerInfo of this._vOtherPlayerInfo) {
                playerInfo.imgBg.spriteFrame = this._atlasHbHumanboy.getSpriteFrame('humanboy_icon_seat_bg_1');
                playerInfo.nodeHead.getChildByName('img').getComponent(cc.Sprite).spriteFrame = null;
                playerInfo.txtCoin.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_IMG_HEAD_TXT;
                if (playerInfo.imgFlag) playerInfo.imgFlag.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_IMG_HEAD_FLAG;
            }
        } while (false);
    }

    protected initDialogHub(): void {
        const dialogHubPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.dialog-hub');
        const dialogHub = cc.instantiate(dialogHubPrefab);
        // cc.game.addPersistRootNode(dialogHub);
        this.node.addChild(dialogHub);
        this._dialogHub = dialogHub.getComponent(DialogHubControl);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        let commonDialogPrefabName = 'common-resource.common-dialog';
        // if (context.platform === 'wpk') commonDialogPrefabName = 'common-resource.wpk-common-dialog';
        const dlgPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(commonDialogPrefabName);
        const dlg = cc.instantiate(dlgPrefab);
        // cc.game.addPersistRootNode(dlg);
        this.node.addChild(dlg);
        this._commonDialog = dlg.getComponent(CommonDialogControl);
    }
}
