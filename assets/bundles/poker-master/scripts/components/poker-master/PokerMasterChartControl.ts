import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/poker-master-macros';
// import cv from '../../lobby/cv';
// import PokerMasterDataMgr from '../pokerMaster/PokerMasterDataMgr'
// import { PokerMasterDef } from './PokerMasterDef';
// import cb from '../cowboy/cb';
import * as domain from '../../domain/poker-master-domain-index';
// import { LANGUAGE_TYPE } from '../../../common/tools/Enum';

/**
 * 扑克大师路单
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class PokerMasterChartControl extends cc.Component {
    @property(cc.Node) _btnClose: cc.Node = null;
    @property(cc.Sprite) chartbg: cc.Sprite = null;

    @property(cc.Node) layout1: cc.Node = null;
    @property(cc.Node) layout2: cc.Node = null;

    @property(cc.Node) _fortuneTxt: cc.Node = null;

    @property(cc.Node) _panelBiaoge1: cc.Node = null;
    @property(cc.Node) _panelBiaoge2: cc.Node = null;

    @property(cc.Node) roadStatisticsTxt: cc.Node = null; // 路单统计
    @property(cc.Node) _fishermanTxt: cc.Node = null; // 渔夫
    @property(cc.Node) _sharkTxt: cc.Node = null; // 鲨鱼

    // @property(cc.Prefab) solid: cc.Prefab = null;
    // @property(cc.Prefab) hollow: cc.Prefab = null;

    @property(cc.Node) _fishermanBar: cc.Node = null;
    @property(cc.Node) _sharkBar: cc.Node = null;

    @property(cc.Node) _fishermanHuo: cc.Node = null;
    @property(cc.Node) _sharkHuo: cc.Node = null;

    @property(cc.Node) _fishermanPoints: cc.Node = null;
    @property(cc.Node) _sharkPoints: cc.Node = null;

    // @property(cc.SpriteAtlas) chartPlist: cc.SpriteAtlas = null;
    languagePlist: cc.SpriteAtlas = null;
    private _atlasChart: cc.SpriteAtlas = null;

    @property index1: number = -1;
    @property index2: number = -1;
    @property index3: number = -1;

    @property _entityDots: cc.Node[] = [];

    @property _hollowDots: cc.Node[][] = [];
    @property(cc.Button) desBtn: cc.Button = null;
    @property(cc.Sprite) desSpr: cc.Sprite = null;

    @property hollowOffset = -10;

    private _pokerMasterRoom: pf.Nullable<domain.PokerMasterRoom> = null;
    private _trend: domain.RoomTrend = new domain.RoomTrend();

    start(): void {
        // ????? 不曉得作用
        // cb.addPlist('chart_PLIST', this._atlasChart);
        // cb.addPlist('language_PLIST', this.languagePlist);
    }

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._pokerMasterRoom = context.room as domain.PokerMasterRoom;

        this._atlasChart = pf.addressableAssetManager.getAsset(macros.Assets.CHART_ATLAS);
        this.languagePlist = pf.addressableAssetManager.getAsset(
            macros.Assets.COWBOY_LANGUAGE_ATLAS /* cv.config.getLanguagePath('game/cowboyPlist/language') */
        );
        this._btnClose = cc.find('btn_close', this.chartbg.node);
        this._btnClose.on(
            'click',
            (event: cc.Event): void => {
                this.node.active = false;
                this.desSpr.node.parent.active = false;
            },
            this
        );

        // this._road_statistics_txt = cc.find('title_txt', this.chartbg.node);
        this._fishermanTxt = cc.find('fisherman_txt', this.layout1);
        this._sharkTxt = cc.find('shark_txt', this.layout1);

        if (pf.LANGUAGE_GROUPS.zh_CN !== pf.languageManager.currentLanguage) {
            this._fishermanTxt.getComponent(cc.Label).fontSize = this._fishermanTxt.getComponent(cc.Label).fontSize - 6;
            this._sharkTxt.getComponent(cc.Label).fontSize = this._sharkTxt.getComponent(cc.Label).fontSize - 6;
            // cv.resMgr.setSpriteFrame(this.roadStatisticsTxt, 'en_US/game/pokermaster/title_statistics');
            // this.roadStatisticsTxt.getComponent (cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
            //     macros.Dynamic_Assets.POKER_MASTER_TITLE_STATISTICS
            // );
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.POKER_MASTER_TITLE_STATISTICS)
                .then((spriteFrame: cc.SpriteFrame) => {
                    this.roadStatisticsTxt.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                });
        }

        this._fortuneTxt = cc.find('fortune_txt', this.layout1);

        this._fishermanHuo = cc.find('fisherman_huo', this.layout1);
        this._sharkHuo = cc.find('shark_huo', this.layout1);

        this._fishermanPoints = cc.find('fisherman_points', this.layout1);
        this._sharkPoints = cc.find('shark_points', this.layout1);

        this._fishermanBar = cc.find('fisherman_progressBar', this.layout1);
        this._sharkBar = cc.find('shark_progressBar', this.layout1);

        this._fishermanHuo = cc.find('fisherman_huo', this.layout1);
        this._sharkHuo = cc.find('shark_huo', this.layout1);

        for (let i = 0; i < 6; i++) {
            this._hollowDots[i] = [];
        }

        this.initFortune();
        this.initBiaoge1();
        this.initBiaoge2();

        this.registerMsg();
        this.onChangeLanguage();

        this.desSpr.node.parent.active = false;
        this.node.getChildByName('zhezhao_panel').on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                this.desSpr.node.parent.active = false;
                event.stopPropagation();
            },
            this
        );
        this.desSpr.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                event.stopPropagation();
            },
            this
        );
        this.desBtn.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event) => {
                // cv.AudioMgr.playButtonSound('tab');
                pf.audioManager.playSoundEffect(macros.Audio.Tab);
                this.desSpr.node.parent.active = true;
            },
            this
        );

        if (pf.LANGUAGE_GROUPS.zh_CN !== pf.languageManager.currentLanguage) {
            // cv.resMgr.setSpriteFrame(this.desSpr.node, 'en_US/game/cowboy/des_img');
            // this.desSpr.spriteFrame = pf.addressableAssetManager.getAsset(
            //     macros.Dynamic_Assets.COWBOY_CHART_DESCRIPTION
            // );
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.COWBOY_CHART_DESCRIPTION)
                .then((spriteFrame: cc.SpriteFrame) => {
                    this.desSpr.spriteFrame = spriteFrame;
                });
        }
    }

    private updateFortune(): void {
        this._fishermanPoints.getComponent(cc.Label).string = cr.CurrencyUtil.convertToClientAmount(
            this._pokerMasterRoom.tFortune.fisherFortune
        ).toString();
        this._sharkPoints.getComponent(cc.Label).string = cr.CurrencyUtil.convertToClientAmount(
            this._pokerMasterRoom.tFortune.sharkFortune
        ).toString();

        let res = this._pokerMasterRoom.tFortune.fisherFortune + this._pokerMasterRoom.tFortune.sharkFortune;
        let radio =
            res === 0
                ? 0
                : this._pokerMasterRoom.tFortune.fisherFortune /
                  (this._pokerMasterRoom.tFortune.fisherFortune + this._pokerMasterRoom.tFortune.sharkFortune);
        this._fishermanBar.getComponent(cc.ProgressBar).progress = radio;
        this._sharkBar.getComponent(cc.ProgressBar).progress = res === 0 ? 0 : 1 - radio;

        this._fishermanHuo.setPosition(
            this._fishermanBar.getPosition().x -
                this._fishermanBar.getContentSize().width / 2 +
                this._fishermanBar.getComponent(cc.ProgressBar).progress * this._fishermanBar.getContentSize().width +
                30,
            this._fishermanHuo.getPosition().y
        );
        this._fishermanPoints.setPosition(
            this._fishermanHuo.getPosition().x + 30,
            this._fishermanPoints.getPosition().y
        );

        this._sharkHuo.setPosition(
            this._sharkBar.getPosition().x -
                this._sharkBar.getContentSize().width / 2 +
                this._sharkBar.getComponent(cc.ProgressBar).progress * this._sharkBar.getContentSize().width +
                30,
            this._sharkHuo.getPosition().y
        );
        this._sharkPoints.setPosition(this._sharkHuo.getPosition().x + 30, this._sharkPoints.getPosition().y);
    }

    private onChangeLanguage(): void {
        // this._road_statistics_txt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_road_statistics');
        this._fishermanTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_fisherman');
        this._sharkTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_shark');
        this._fortuneTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_fortune');
    }

    private registerMsg(): void {
        // let MsgPrefix: string = PokerMasterDef.LocalMsg().MsgPrefix;
        // cv.MessageCenter.register(MsgPrefix + PokerMasterDef.LocalMsg().UPDATE_TREND, this._onTrendUpdate.bind(this), this.node);
        this._pokerMasterRoom.addListener('trendNotify', this._onTrendUpdate.bind(this));
    }

    private unregisterMsg(): void {
        // let MsgPrefix: string = PokerMasterDef.LocalMsg().MsgPrefix;
        // cv.MessageCenter.unregister(MsgPrefix + PokerMasterDef.LocalMsg().UPDATE_TREND, this.node);
    }

    onDestroy() {
        this.unregisterMsg();
    }

    private initFortune(): void {
        this.updateFortune();
    }

    private initBiaoge1(): void {
        this._panelBiaoge1 = cc.find('panelBiaoGe1', this.layout2);
        let sp = cc.find('recordDot', this._panelBiaoge1);
        sp.active = false;
        let pos = sp.getPosition();
        let offset = cc.v2(434 / 8, -326 / 6);
        for (let i = 0; i < 48; i++) {
            let soliditem = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.SOLID));
            this._panelBiaoge1.addChild(soliditem);

            soliditem.setPosition(cc.v2(pos.x + Math.floor(i / 6) * offset.x, pos.y + (i % 6) * offset.y));
            this._entityDots.push(soliditem);
        }
    }

    private initBiaoge2(): void {
        this._panelBiaoge2 = cc.find('panelBiaoGe2', this.layout2);
        let sp = cc.find('item_image', this._panelBiaoge2);
        sp.active = false;
        let pos = sp.getPosition();
        let offset = cc.v2(812 / 15, -326 / 6);
        for (let i = 0; i < 6; i++) {
            for (let j = 0; j < 15; j++) {
                let hollowitem = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HOLLOW));
                this._panelBiaoge2.addChild(hollowitem);
                hollowitem.setPosition(cc.v2(pos.x + j * offset.x, pos.y + i * offset.y));
                this._hollowDots[i].push(hollowitem);
            }
        }
    }

    /* eslint complexity: ["error", { max: 33 }] */
    /* eslint-disable no-fallthrough */
    private updateBiaoge1(): void {
        for (let i = 0; i < 48; i++) {
            this._entityDots[i].stopAllActions();
            this._entityDots[i].active = false;
            this._entityDots[i].destroyAllChildren();
            this._entityDots[i].removeAllChildren(true);
        }

        if (this._trend.trend.length <= 48) {
            for (let i = 0; i < this._trend.trend.length; i++) {
                this._entityDots[i].active = true;
                this._entityDots[i].opacity = 255;

                if (this._trend.trend[i].win === 101) {
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_13'); // cb.getTextureByName('chart_PLIST', 'chart_13');
                } else if (this._trend.trend[i].win === 102) {
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_12'); // cb.getTextureByName('chart_PLIST', 'chart_12');
                } else if (this._trend.trend[i].win === 103) {
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_11'); // cb.getTextureByName('chart_PLIST', 'chart_11');
                }

                let px = new cc.Node();
                let sp = px.addComponent(cc.Sprite);
                let size = this._entityDots[i].getContentSize();
                // px.setPosition(cc.v2(size.width / 2, size.height / 2));
                this._entityDots[i].addChild(px);

                if (i + 1 === this._trend.trend.length) {
                    let znew = new cc.Node();
                    znew.name = 'new';
                    znew.addComponent(cc.Sprite);
                    znew.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('chart_new'); // cb.getTextureByName('chart_PLIST', 'chart_new');
                    znew.setPosition(cc.v2(22, 22));
                    this._entityDots[i].addChild(znew);

                    let callback = cc.callFunc(() => {
                        this._entityDots[i].active = true;
                        this._entityDots[i].opacity = 255;
                    });
                    this.index1 = i;
                    this._entityDots[i].runAction(cc.sequence(cc.blink(2, 2), callback));
                }

                switch (this._trend.trend[i].win_patterns) {
                    case 1:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_gaopai'); // cb.getTextureByName('language_PLIST', 'chart_gaopai');
                        break;
                    case 2:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_yidui'); // cb.getTextureByName('language_PLIST', 'chart_yidui');
                        break;
                    case 3:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_liangdui'); // cb.getTextureByName('language_PLIST', 'chart_liangdui');
                        break;
                    case 4:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_santiao'); // cb.getTextureByName('language_PLIST', 'chart_santiao');
                        break;
                    case 5:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_shunzi'); // cb.getTextureByName('language_PLIST', 'chart_shunzi');
                        break;
                    case 6:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_tonghua'); // cb.getTextureByName('language_PLIST', 'chart_tonghua');
                        break;
                    case 7:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_hulu'); // cb.getTextureByName('language_PLIST', 'chart_hulu');
                        break;
                    case 8:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_jingang'); // cb.getTextureByName('language_PLIST', 'chart_jingang');
                        break;
                    case 9:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_tonghuashun'); // cb.getTextureByName('language_PLIST', 'chart_tonghuashun');
                        break;
                    case 10:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_huangtong'); // cb.getTextureByName('language_PLIST', 'chart_huangtong');
                        break;
                }
            }
        } else {
            let j = this._trend.trend.length - 48;
            for (let i = 0; i < 48; i++) {
                this._entityDots[i].active = true;
                this._entityDots[i].opacity = 255;
                if (this._trend.trend[i + j].win === 101) {
                    // 红色
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_13'); // cb.getTextureByName('chart_PLIST', 'chart_13');
                } else if (this._trend.trend[i + j].win === 102) {
                    // 蓝色
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_12'); // cb.getTextureByName('chart_PLIST', 'chart_12');
                } else if (this._trend.trend[i + j].win === 103) {
                    // 平局
                    this._entityDots[i].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_11'); // cb.getTextureByName('chart_PLIST', 'chart_11');
                }

                let size = this._entityDots[i].getContentSize();
                if (i === 47) {
                    let znew = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.SOLID));
                    znew.getComponent(cc.Sprite).spriteFrame = this._atlasChart.getSpriteFrame('chart_new'); // cb.getTextureByName('chart_PLIST', 'chart_new');
                    znew.setPosition(cc.v2(22, 22));
                    this._entityDots[i].addChild(znew);
                    this._entityDots[i].active = true;

                    let callback = cc.callFunc(() => {
                        this._entityDots[i].active = true;
                        this._entityDots[i].opacity = 255;
                    });
                    this.index1 = i;
                    this._entityDots[i].runAction(cc.sequence(cc.blink(2, 2), callback));
                }

                let px = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.SOLID));
                // px.setPosition(size.width / 2, size.height / 2);
                this._entityDots[i].addChild(px);

                switch (this._trend.trend[i + j].win_patterns) {
                    case 1:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_gaopai'); // cb.getTextureByName('language_PLIST', 'chart_gaopai');
                        break;
                    case 2:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_yidui'); // cb.getTextureByName('language_PLIST', 'chart_yidui');
                        break;
                    case 3:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_liangdui'); // cb.getTextureByName('language_PLIST', 'chart_liangdui');
                        break;
                    case 4:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_santiao'); // cb.getTextureByName('language_PLIST', 'chart_santiao');
                        break;
                    case 5:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_shunzi'); // cb.getTextureByName('language_PLIST', 'chart_shunzi');
                        break;
                    case 6:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_tonghua'); // cb.getTextureByName('language_PLIST', 'chart_tonghua');
                        break;
                    case 7:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_hulu'); // cb.getTextureByName('language_PLIST', 'chart_hulu');
                        break;
                    case 8:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_jingang'); // cb.getTextureByName('language_PLIST', 'chart_jingang');
                        break;
                    case 9:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_tonghuashun'); // cb.getTextureByName('language_PLIST', 'chart_tonghuashun');
                        break;
                    case 10:
                        px.getComponent(cc.Sprite).spriteFrame = this.languagePlist.getSpriteFrame('chart_huangtong'); // cb.getTextureByName('language_PLIST', 'chart_huangtong');

                        break;
                }
            }
        }
    }

    private updateBiaoge2(): void {
        for (let i = 0; i < 6; i++) {
            for (let j = 0; j < 15; j++) {
                this._hollowDots[i][j].stopAllActions();
                this._hollowDots[i][j].active = false;

                if (this._hollowDots[i][j].getChildByName('ping') !== null) {
                    this._hollowDots[i][j].destroyAllChildren();
                    this._hollowDots[i][j].removeAllChildren(true);
                }
            }
        }

        let trendLen = this._trend.road.length;
        for (let i = 0; i < trendLen; i++) {
            let row = this._trend.road[i];
            let length = row.roadRow.length;
            for (let j = 0; j < length; j++) {
                this._hollowDots[i][j].opacity = 255;
                if (row.roadRow[j].win === 'r') {
                    this._hollowDots[i][j].active = true;
                    this._hollowDots[i][j].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_03'); // cb.getTextureByName('chart_PLIST', 'chart_03');
                } else if (row.roadRow[j].win === 'b') {
                    this._hollowDots[i][j].active = true;
                    this._hollowDots[i][j].getComponent(cc.Sprite).spriteFrame =
                        this._atlasChart.getSpriteFrame('chart_02'); // cb.getTextureByName('chart_PLIST', 'chart_02');
                }

                if (row.roadRow[j].eqc > 0) {
                    let hecount = pf.StringUtil.formatC('%d', row.roadRow[j].eqc);

                    let node = new cc.Node('text');
                    node.name = 'ping';
                    node.addComponent(cc.Label);
                    node.getComponent(cc.Label).string = hecount;
                    node.getComponent(cc.Label).fontSize = 24;
                    node.color = cc.color(23, 130, 82);
                    node.setPosition(cc.v2(0, this.hollowOffset));
                    node.opacity = 255;
                    this._hollowDots[i][j].addChild(node);
                }

                if (i === this._trend.lastRow && j === this._trend.lastCol) {
                    this._hollowDots[i][j].active = true;
                    let callback = cc.callFunc(() => {
                        this._hollowDots[i][j].active = true;
                        this._hollowDots[i][j].opacity = 255;
                    });
                    this.index2 = i;
                    this.index3 = j;
                    this._hollowDots[i][j].runAction(cc.sequence(cc.blink(2, 2), callback));
                }
            }
        }
    }

    private _onTrendUpdate(trend: domain.RoomTrend): void {
        this._trend = trend;
        this.setData();
        this.layout1.active = true;
        this.layout2.active = true;
    }

    private setData(): void {
        this.updateFortune();
        this.updateBiaoge1();
        this.updateBiaoge2();
    }

    close(): void {
        this.desSpr.node.parent.active = false;
        this.node.active = false;
    }
}
