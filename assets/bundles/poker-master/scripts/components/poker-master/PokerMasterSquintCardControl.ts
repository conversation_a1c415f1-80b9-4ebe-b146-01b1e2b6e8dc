import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/poker-master-macros';
import type * as domain from '../../domain/poker-master-domain-index';
import { RubCardControl } from './RubCardControl';
import PeekCardMiniGameControl from './PeekCardMiniGameControl';
import { PokerMasterDef } from '../../common/poker-master-define';
import { CardSuit, type OutItem } from '../../domain/poker-master-domain-index';
import PokerCardControl = cr.components.MiniGamePokerCardControl;

/**
 * 扑克大师 眯牌面板预制件
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class PokerMasterSquintCardControl extends cc.Component {
    @property(cc.Prefab) prefabRubCard: cc.Prefab = null; // 眯牌shader预制件
    @property(cc.Prefab) prefabPeekCard: cc.Prefab = null; // 眯牌动画预制件
    @property(cc.SpriteAtlas) atlasPmPokermaster: cc.SpriteAtlas = null; // 扑克大师图集
    @property(cc.Sprite) imgShield: cc.Sprite = null; // 屏蔽层
    @property(cc.Sprite) imgToast: cc.Sprite = null; // 计时提示
    @property(cc.Sprite) imgFace: cc.Sprite = null; // 底牌正面
    @property(cc.Sprite) imgBack: cc.Sprite = null; // 底牌背面
    // @property(cc.Sprite) img_suit_lu: cc.Sprite = null;                                                                         // 左上角花色
    // @property(cc.Sprite) img_suit_rd: cc.Sprite = null;                                                                         // 右下角花色
    @property(cc.Label) txtToast: cc.Label = null; // 计时提示文本
    @property(cc.Node) outsBg: cc.Node = null;
    @property(cc.Animation) zhiyinAni: cc.Animation = null;
    @property(cc.Node) aniTouch: cc.Node = null;

    // 從humanboy抄來的
    // @property(cc.Prefab) prefabCard: cc.Prefab = null;
    @property(cc.SpriteFrame) cardbackSpriteFrame: cc.SpriteFrame = null;

    private _toastTip: string = '';
    private _leftTime: number = 0;
    private _squintShader: RubCardControl = null;
    private _isSquintFinish: boolean = false;

    private _srcScale: number = 1;
    private _srcAngle: number = 0;
    private _srcPos: cc.Vec2 = cc.Vec2.ZERO;

    private _isViewLuckTurnTables: boolean = false;
    private _cardNum: number = 0;
    private _cardSuit: number = 0;
    private _isCreateShade: boolean = false;
    private _bCanSquint: boolean = false;

    private _sharkOuts: OutItem[] = [];
    private _dashiOuts: OutItem[] = [];
    private outMap: Map<cc.Node, PokerCardControl[]> = new Map();
    private isIphoneXArea: boolean = false;

    private _PeekCard: PeekCardMiniGameControl = null;

    private _pokerMasterRoom: pf.Nullable<domain.PokerMasterRoom> = null;
    private _luckTurntableService: pf.services.LuckTurntableService = null;
    // eslint-disable-next-line max-params
    show(
        bCanSquint: boolean,
        cardNum: number,
        cardSuit: number,
        leftTime: number,
        sharkOuts: OutItem[],
        dashiOuts: OutItem[],
        isIphoneXArea: boolean
    ): void {
        this.isIphoneXArea = isIphoneXArea;
        this._bCanSquint = bCanSquint;
        this.node.active = true;
        this._resetView();
        this._cardNum = cardNum;
        this._cardSuit = cardSuit;
        this._isCreateShade = false;
        // 启动定时器
        this._leftTime = leftTime;

        // 自己是否可以眯牌
        this.imgShield.node.active = false;
        this.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_COUNT_DOWN;
        this.node.getComponent(cc.BlockInputEvents).enabled = false;
        this._sharkOuts = sharkOuts;
        this._dashiOuts = dashiOuts;

        this.showSquintCard();

        if (bCanSquint) {
            this._toastTip = pf.languageManager.getString('PokerMaster_tips_insure_self_squint');
        } else {
            this._toastTip = pf.languageManager.getString(
                this._pokerMasterRoom.whoIsLeader === 1
                    ? 'PokerMaster_tips_insure_other_squint_1'
                    : 'PokerMaster_tips_insure_other_squint_0'
            );
            // this._toastTip = pf.languageManager.getString('PokerMaster_tips_insure_other_squint_1');
        }

        if (this._leftTime > 0) this._startClock();
    }

    hide(): void {
        this._resetView();
        this.node.active = false;
    }

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._pokerMasterRoom = context.room as domain.PokerMasterRoom;
        cr.UIUtil.adaptWidget(this.node, true);
        this._srcScale = this.imgBack.node.scale;
        this._srcAngle = this.imgBack.node.angle;
        this._srcPos = cc.v2(this.imgBack.node.position);
        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);
        this._luckTurntableService.addListener('luckTurntableIsView', this.sendLuckTurnTablesView.bind(this));
        // cv.MessageCenter.register('LuckTurntables_isView', this.sendLuckTurnTablesView.bind(this), this.node);           // 开启或关闭透传到扑克大师，影响眯牌是否显示
        this.outsBg.active = false;
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            this.txtToast.fontSize = 36;
        }
    }

    protected start() {}

    onDestroy() {
        // cv.MessageCenter.unregister('LuckTurntables_isView', this.node);
    }

    private _createShade(autoShow: boolean = false): void {
        if (this._squintShader) return;
        this.imgShield.node.active = true;
        this.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_SQUINT;
        this.node.getComponent(cc.BlockInputEvents).enabled = true;
        this._isCreateShade = true;

        this.imgFace.node.active = false;
        this.imgBack.node.active = false;
        let frameName: string = this._getSquintCardFrameName(this._cardNum, this._cardSuit);
        let bundleName = this._getBundleName();
        let faceCardPath = this._getSquintCardFacePath() + frameName;
        let backCardPath = this._getSquintCardBackPath();
        if (autoShow) {
            if (!this._PeekCard) {
                this._PeekCard = cc.instantiate(this.prefabPeekCard).getComponent(PeekCardMiniGameControl);
                this.node.addChild(this._PeekCard.node);
                this._PeekCard.node.setPosition(this.imgBack.node.position);
                this._PeekCard.node.setContentSize(400 + 40, 560 + 40);
                this._PeekCard.setTopNodeRadioNum(-90);
                this._PeekCard.setCardSize(cc.size(400, 560));

                this._PeekCard._moveSpeed = 0.1;
                this._PeekCard.angleFixed = 0;
            }
            this._PeekCard.setBundleName(bundleName);
            this._PeekCard.setCardBack(backCardPath);
            this._PeekCard.setCardFace(faceCardPath);
            // x.setShadow(this.shadow);
            // x.setFinger(this.HelloWorld, 1);
            // x.setFinger(this.HelloWorld, 2);
            this._PeekCard.init();
            this._PeekCard.setAutoPlay(false);
            this._PeekCard.node.active = true;

            let time = this._leftTime - 4.5;
            if (time > 0) {
                this.scheduleOnce(() => {
                    if (this._PeekCard && this._PeekCard.node.active) {
                        this._PeekCard.setAutoPlay(true);
                    }
                }, time);
            } else {
                if (this._PeekCard && this._PeekCard.node.active) {
                    this._PeekCard.setAutoPlay(true);
                }
            }

            return;
        }

        this._squintShader = cc.instantiate(this.prefabRubCard).getComponent(RubCardControl);
        this._squintShader.setBundleName(bundleName);
        this._squintShader.setFaceTextureFileName(faceCardPath);
        this._squintShader.setBackTextureFileName(backCardPath);
        this._squintShader.setScale(this.imgBack.node.scale);
        this._squintShader.setPosition(this.imgBack.node.position.x, this.imgBack.node.position.y);
        this._squintShader.setCallBack((dir: number, angle: number, faceSpritFrame: cc.SpriteFrame): void => {
            this._squintShader = null;
            this._isSquintFinish = true;

            // 已经眯开, 移除眯牌shader节点后, 恢复底牌
            this._showFace(faceSpritFrame, angle);
        });

        this._squintShader.node.active = true;
        this.node.addChild(this._squintShader.node);
        if (autoShow) {
            this._squintShader.showAuto();
            this.scheduleOnce(() => {
                if (this._squintShader) {
                    this._squintShader.beginFlip();
                }
            }, 2.0);
        }
    }

    private _resetView(): void {
        this.resetZhiyinAni();
        this.resetOut();
        this.node.stopAllActions();
        cc.director.getScheduler().unscheduleAllForTarget(this.node);

        this.imgToast.node.active = false;
        this.imgShield.node.active = true;
        this._isSquintFinish = false;

        this.imgFace.node.active = false;
        this.imgFace.node.stopAllActions();
        this.imgFace.node.setScale(this._srcScale);
        this.imgFace.node.angle = this._srcAngle;
        this.imgFace.node.setPosition(this._srcPos);

        this.imgBack.node.active = false;
        this.imgBack.node.stopAllActions();
        this.imgBack.node.setScale(this._srcScale);
        this.imgBack.node.angle = this._srcAngle;
        this.imgBack.node.setPosition(this._srcPos);

        // 清除shader
        if (this._squintShader) {
            this._squintShader.node.removeFromParent(true);
            this._squintShader.destroy();
            this._squintShader = null;
        }

        if (this._PeekCard) {
            this._PeekCard.node.active = false;
            this._PeekCard.setAutoPlay(false);
        }
    }

    private async _showFace(faceSpritFrame: cc.SpriteFrame, angle: number) {
        this.imgFace.node.active = true;
        this.imgBack.node.active = false;
        this.imgFace.node.angle = angle;

        let sprite: cc.Sprite = this.imgFace.node.getComponent(cc.Sprite);
        if (!cr.UIUtil.isValidNode(sprite)) return;

        // 保留原有若干属性
        if (cr.UIUtil.isValidNode(sprite.spriteFrame)) {
            faceSpritFrame.insetLeft = sprite.spriteFrame.insetLeft;
            faceSpritFrame.insetTop = sprite.spriteFrame.insetTop;
            faceSpritFrame.insetRight = sprite.spriteFrame.insetRight;
            faceSpritFrame.insetBottom = sprite.spriteFrame.insetBottom;
        }
        this.imgFace.spriteFrame = faceSpritFrame;
    }

    private _showBack(): void {
        this.imgFace.node.active = false;
        this.imgBack.node.active = true;
    }

    private _getSquintCardFrameName(cardNumber: number, cardSuit: number): string {
        let suit = '';
        switch (cardSuit) {
            case CardSuit.CARD_SPADE:
                suit = 'Bhm_';
                break; // 3 黑桃
            case CardSuit.CARD_HEART:
                suit = 'Rhm_';
                break; // 2 紅心
            case CardSuit.CARD_CLUB:
                suit = 'Bcm_';
                break; // 1 梅花
            case CardSuit.CARD_DIAMOND:
                suit = 'Rbm_';
                break; // 0 方塊
            default:
                suit = 'Bhm_';
                break;
        }
        return `${suit}${cardNumber + 1}`;
    }

    private _getBundleName(): string {
        return macros.BUNDLE_NAME;
    }

    /**
     * 获取眯牌资源
     * @param cardNumber
     * @param cardSuit
     */
    private _getSquintCardFacePath(): string {
        return 'textures/card_type/'; // 'zh_CN/game/pokermaster/rubcard/';
    }

    /**
     * 获取眯牌资源
     * @param cardNumber
     * @param cardSuit
     */
    private _getSquintCardBackPath(): string {
        return 'textures/card_type/card_back'; // 'zh_CN/game/pokermaster/rubcard/card_back';
    }

    private _startClock(): void {
        this._updateToastTip();
        this.unschedule(this._onTimeClock);
        if (this._leftTime > 0) {
            this.schedule(this._onTimeClock, 1.0);
        }
    }

    private _stopClock(): void {
        this.unschedule(this._onTimeClock);

        // 隐藏屏蔽层, 提示语
        this.imgToast.node.active = false;
        this.imgShield.node.active = false;

        // 清除shader
        if (this._squintShader) {
            this._squintShader.node.removeFromParent(true);
            this._squintShader.destroy();
            this._squintShader = null;
        }

        // 若眯牌shader没有翻开, 则显示底牌牌背
        if (!this._isSquintFinish) {
            this._showBack();
        }
    }

    private _onTimeClock(): void {
        if (--this._leftTime >= 0) {
            this._updateToastTip();
            this.showSquintCard();
        } else {
            this._stopClock();
        }
    }

    private _updateToastTip(): void {
        this.imgToast.node.active = !this._isViewLuckTurnTables;
        this.txtToast.string = pf.StringUtil.formatC(this._toastTip, this._leftTime);
    }

    sendLuckTurnTablesView(isView: boolean): void {
        this._isViewLuckTurnTables = isView;
    }

    private createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.POKER_CARD_SQUINT))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(this.cardbackSpriteFrame);
        return ctrl;
    }

    private showOuts(sharkOuts: OutItem[], dashiOuts: OutItem[]) {
        let sharkLen = sharkOuts.length;
        let dashiLen = dashiOuts.length;
        if (sharkLen <= 0 && dashiLen <= 0) {
            this.resetOut();
            return;
        }

        let midLen0 = this.isIphoneXArea ? 25 : 21;
        let midLen1 = this.isIphoneXArea ? 8 : 6;
        let oneLen0 = (midLen0 - 1) / 2;
        let tempwidth = this.outsBg.width;

        let showOutFunc = (msg: OutItem[], showLen: number, startIndex: number, outBg: cc.Node) => {
            let arr = this.outMap.get(outBg);
            outBg.active = true;
            // let cardPath: Readonly<string> = 'zh_CN/game/pokermaster/card_type_0/';
            let outLen = arr.length;
            let tempCard = outBg.getChildByName('card');

            if (showLen < outLen) {
                for (let i = outLen - 1; i >= showLen; --i) {
                    arr[i].node.removeFromParent(true);
                    arr[i].node.destroy();
                    arr.pop();
                }
            } else if (showLen > outLen) {
                for (let i = outLen; i < showLen; ++i) {
                    // let card: CowboyCard = CowboyCard.create(cardPath, cardPath);
                    let card: PokerCardControl = this.createPokerCard();
                    card.ResetFromNode(tempCard);
                    card.node.name = 'card_map_' + i;
                    arr.push(card);
                }
            }

            outBg.setContentSize(tempwidth + (showLen - 1) * 74, 186);
            outBg.getComponent(cc.Layout).updateLayout();

            for (let i = 0; i < showLen; ++i) {
                arr[i].SetContent(msg[startIndex + i].card.number, msg[startIndex + i].card.suit);
                arr[i].SetFace(true);
            }
            this.outMap.set(outBg, arr);
        };

        let setOutBgPos = (bg: cc.Node, index: number, direction: number) => {
            if (index === 0) {
                if (direction === 0) {
                    bg.setAnchorPoint(0.5, 0.5);
                    bg.setPosition(this.outsBg.position);
                } else if (direction === -1) {
                    bg.setAnchorPoint(1, 0.5);
                    bg.setPosition(tempwidth / 2 - 35.5 - 7, this.outsBg.y);
                } else if (direction === 1) {
                    bg.setAnchorPoint(0, 0.5);
                    bg.setPosition(-tempwidth / 2 + 35.5 + 7, this.outsBg.y);
                }
            } else {
                if (direction === 0) {
                    // 中心
                    bg.setAnchorPoint(1, 0.5);
                    bg.setPosition(this.outsBg.x + (tempwidth + (midLen0 - 1) * 74) / 2, this.outsBg.y - index * 105);
                } else if (direction === -1) {
                    // 左侧
                    bg.setAnchorPoint(0, 0.5);
                    bg.setPosition(
                        this.outsBg.x - (tempwidth + (midLen0 - 1) * 74) / 2 + tempwidth / 2 - 35.5 - 7 - 3,
                        this.outsBg.y - index * 105
                    );
                } else if (direction === 1) {
                    // 右侧
                    bg.setAnchorPoint(1, 0.5);
                    bg.setPosition(
                        this.outsBg.x + (tempwidth + (midLen0 - 1) * 74) / 2 - (tempwidth / 2 - 35.5 - 7) + 3,
                        this.outsBg.y - index * 105
                    );
                }
            }
        };

        let setOuts = (bgNum: number, msg: OutItem[], msgLen: number, direction: number): number => {
            let _bgNum = bgNum;
            let tempResult = _bgNum;
            for (let i = 0; i < msgLen; ) {
                let outbg = this.node.getChildByName('outbg_map_' + _bgNum);
                if (!outbg) {
                    outbg = cc.instantiate(this.outsBg);
                    outbg.name = 'outbg_map_' + _bgNum;
                    this.outsBg.parent.addChild(outbg);
                    this.outMap.set(outbg, []);
                }

                let tempLen = direction === 0 ? midLen0 : oneLen0;
                if (i > 0) {
                    tempLen = midLen1;
                }

                setOutBgPos(outbg, _bgNum - tempResult, direction);

                if (i + tempLen >= msgLen) {
                    showOutFunc(msg, msgLen - i, i, outbg);
                } else {
                    showOutFunc(msg, tempLen, i, outbg);
                }
                i += tempLen;
                _bgNum = _bgNum + 1;
            }
            return _bgNum;
        };

        let msg: OutItem[] = [];
        if (sharkLen <= 0 || dashiLen <= 0) {
            let msgLen = 0;
            if (sharkLen === 0) {
                msgLen = dashiLen;
                msg = dashiOuts;
            } else {
                msgLen = sharkLen;
                msg = sharkOuts;
            }

            setOuts(0, msg, msgLen, 0);
        } else {
            let result = setOuts(0, dashiOuts, dashiLen, -1);
            setOuts(result, sharkOuts, sharkLen, 1);
        }
    }

    private resetOut() {
        this.outMap.forEach((value: PokerCardControl[], key: cc.Node) => {
            key.active = false;
        });
    }

    private showZhiyinAni() {
        let storeGuideKey = 'master_squintCard_guide';
        if (pf.localStorage.getItem(storeGuideKey) !== 'true') {
            this._isCreateShade = true;
            this._showBack();
            this.zhiyinAni.node.active = true;
            this.zhiyinAni.play();
            this.aniTouch.on(
                cc.Node.EventType.TOUCH_START,
                (event: cc.Event.EventTouch) => {
                    let hasShowGuide = 'true';
                    pf.localStorage.setItem(storeGuideKey, hasShowGuide);

                    this._createShade();
                    if (this._squintShader.unregisterEvent) {
                        this.aniTouch.targetOff(this);
                        return;
                    }
                    this._squintShader.node.emit(cc.Node.EventType.TOUCH_START, event);
                    this.zhiyinAni.stop();
                    this.zhiyinAni.node.active = false;
                },
                this
            );

            this.aniTouch.on(
                cc.Node.EventType.TOUCH_MOVE,
                (event: cc.Event.EventTouch) => {
                    this._squintShader.node.emit(cc.Node.EventType.TOUCH_MOVE, event);
                    if (this._squintShader.unregisterEvent) {
                        this.aniTouch.targetOff(this);
                        return;
                    }
                },
                this
            );

            this.aniTouch.on(
                cc.Node.EventType.TOUCH_END,
                (event: cc.Event.EventTouch) => {
                    this._squintShader.node.emit(cc.Node.EventType.TOUCH_END, event);
                    if (this._squintShader.unregisterEvent) {
                        this.aniTouch.targetOff(this);
                        return;
                    }
                },
                this
            );

            this.aniTouch.on(
                cc.Node.EventType.TOUCH_CANCEL,
                (event: cc.Event.EventTouch) => {
                    this._squintShader.node.emit(cc.Node.EventType.TOUCH_CANCEL, event);
                    if (this._squintShader.unregisterEvent) {
                        this.aniTouch.targetOff(this);
                        return;
                    }
                },
                this
            );
        } else {
            this._createShade();
        }
    }

    private resetZhiyinAni() {
        this.zhiyinAni.stop();
        this.zhiyinAni.node.active = false;

        this.aniTouch.targetOff(this);
    }

    private showSquintCard() {
        if (this._isCreateShade) return;
        if (!this._isViewLuckTurnTables) {
            this.showOuts(this._sharkOuts, this._dashiOuts);

            if (this._bCanSquint) {
                this.showZhiyinAni();
            } else {
                this._createShade(true);
            }
        }
    }
}
