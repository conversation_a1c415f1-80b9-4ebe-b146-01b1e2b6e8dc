/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/poker-master-macros';
import { CowboyItemControl } from '../../../../common-resource/scripts/components/mini-game-player-list/CowboyItemControl';

const { ccclass, property } = cc._decorator;

@ccclass
export default class PokerMasterItemControl extends CowboyItemControl {
    protected initAtlas(): void {
        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_LANGUAGE_ATLAS);
    }

    updateSVReuseData(index: number, dataArray: any[]): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;

        const player = dataArray[index] as pf.services.GamePlayer;

        this.tz_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_icon_bet');
        this.hs_img.getComponent(cc.Sprite).spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_icon_hs');

        this.num_text.string = pf.StringUtil.formatC(pf.languageManager.getString('Cowboy_fuhao_no_text_wpk'), index);
        this.num_text.node.active = true;

        if (index > 8) {
            this.rank_img.node.active = true;
            this.rank_img.spriteFrame = this.playerlist_PLIST.getSpriteFrame('fuwen9');
        } else {
            this.rank_img.node.active = true;
            if (index === 0) {
                this.rank_img.getComponent(cc.Sprite).spriteFrame =
                    this.playerlist_PLIST.getSpriteFrame('playerlist_ssz');
                this.num_text.string = ''; // pf.languageManager.getString('Cowboy_shensuanzi_text');
            } else {
                this.rank_img.getComponent(cc.Sprite).spriteFrame = this.playerlist_PLIST.getSpriteFrame('fuwen');
            }
        }

        this.jushu_text.node.opacity = 127;
        this.jushu_text.node.color = cc.color(255, 255, 255, 127);

        this.jushu_text.node.getComponent(cc.Label).string = pf.languageManager.getString('Cowboy_last20_text');

        this.name_text.string = player.name;
        this.bet_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(player.totalBetAmount)
        );
        this.win_text.string = cr.CurrencyUtil.clientAmountToDisplayString(player.winCount);
        this.money_text.string = cr.CurrencyUtil.clientAmountToDisplayString(
            cr.CurrencyUtil.convertToClientAmount(player.curCoin)
        );

        const playerInfo = player.uid === this._gameRoom.selfPlayer.uid ? this._gameRoom.selfPlayer : player;
        const headPath = cr.CommonUtil.getHeadPath(playerInfo, this._gameRoom.selfPlayer.uid);
        this.avatarControl?.loadHeadImage(headPath, player.plat);
    }
}
