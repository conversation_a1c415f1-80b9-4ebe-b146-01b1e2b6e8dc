import * as pf from '../../../../../poker-framework/scripts/pf';
import { macros } from '../../common/poker-master-macros';
import type * as domain from '../../domain/poker-master-domain-index';
import * as network from '../../network/poker-master-network-index';
import { CardItem } from '../../domain/poker-master-card-item';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { PokerMasterControl } from './PokerMasterControl';
// import cv from '../../lobby/cv';
// import PokerMasterDataMgr from './PokerMasterDataMgr';
// import { pokermaster_proto } from '../../../../Script/common/pb/pokermaster';
// import { PokerMasterDef } from './PokerMasterDef';
// import CowboyCard from '../cowboy/CowboyCard';
// import { LANGUAGE_TYPE } from '../../../common/tools/Enum';

import PokerCardControl = cr.components.MiniGamePokerCardControl;

/**
 *  投注回顾逻辑
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class PokerMasterReviewControl extends cc.Component {
    @property(cc.Node) layout1: cc.Node = null;
    @property(cc.Node) layout2: cc.Node = null;
    @property(cc.Node) layout3: cc.Node = null;
    @property(cc.Node) bottom: cc.Node = null;

    @property(cc.Node) _totalbetTxt: cc.Node = null;
    @property(cc.Node) _totalbetText: cc.Node = null;
    @property(cc.Node) _profitTxt: cc.Node = null;

    @property(cc.Node) card0: cc.Node = null;
    @property(cc.Node) card1: cc.Node = null;
    @property(cc.Node) card2: cc.Node = null;
    @property(cc.Node) card3: cc.Node = null;
    @property(cc.Node) card4: cc.Node = null;
    @property(cc.Node) card5: cc.Node = null;
    @property(cc.Node) card6: cc.Node = null;
    @property(cc.Node) card7: cc.Node = null;
    @property(cc.Node) card8: cc.Node = null;

    @property(cc.Node) panelPublicCard: cc.Node = null;
    @property(cc.Node) panelFishermanCard: cc.Node = null;
    @property(cc.Node) panelSharkCard: cc.Node = null;

    @property(cc.Node) _firstBetTxt: cc.Node = null;
    @property(cc.Node) _fishermanTxt: cc.Node = null;
    @property(cc.Node) _fishermanOddsTxt: cc.Node = null;
    @property(cc.Node) _fishermanBetTxt: cc.Node = null;
    @property(cc.Node) _fishermanNum: cc.Node = null;

    @property(cc.Node) _sharkBetTxt: cc.Node = null;
    @property(cc.Node) _sharkOddsTxt: cc.Node = null;
    @property(cc.Node) _sharkTxt: cc.Node = null;
    @property(cc.Node) _sharkNum: cc.Node = null;

    @property(cc.Node) _secondBetTxt: cc.Node = null;

    @property(cc.Button) btnFirst: cc.Button = null;
    @property(cc.Button) btnLast: cc.Button = null;
    @property(cc.Button) btnBefore: cc.Button = null;
    @property(cc.Button) btnNext: cc.Button = null;

    @property(cc.Sprite) titleSpr: cc.Sprite = null;
    paixingTxtArr: cc.Node[] = [];
    paixingPeilvArr: cc.Label[] = [];
    paixingXiazhuArr: cc.Label[] = [];
    paixingWinArr: cc.Label[] = [];

    @property(cc.Node) panelMain: cc.Node = null; // 主面板
    @property(cc.Sprite) imgShield: cc.Sprite = null; // 遮罩底图
    @property(cc.Label) txtPaixing: cc.Label = null; // 牌型
    @property(cc.Label) txtPage0: cc.Label = null; // 页数
    @property(cc.Label) txtPage1: cc.Label = null; // 页数
    private _curPage: number = 19; // 当前页（0-19）
    private _vFishermanCard: PokerCardControl[] = []; // 渔夫
    private _vPubsCard: PokerCardControl[] = []; // 公共牌
    private _vSharkCard: PokerCardControl[] = []; // 鲨鱼

    // private _strCardFacePath: Readonly<string> = 'zh_CN/game/cowboy/card_type_0/';									            // 牌正面资源路径
    // private _strCardBackPath: Readonly<string> = 'zh_CN/game/pokermaster/card_type_0/';								            // 牌背面资源路径
    @property(cc.Color) loseColor: cc.Color = new cc.Color(153, 153, 153);
    @property(cc.Color) winColor: cc.Color = new cc.Color(255, 106, 7);
    @property(cc.Color) profitTxtColor: cc.Color = new cc.Color(255, 106, 7);
    @property({
        tooltip: '刷新ProfitTxt0顏色'
    })
    bRefreshProfitTxt0Color: boolean = false;
    private startPos: cc.Vec2 = cc.v2(0, 0);

    @property(cc.Label) sharkBetTxt0: cc.Label = null;
    @property(cc.Label) fishermanBetTxt0: cc.Label = null;
    @property(cc.Label) fishermanOddsTxt0: cc.Label = null;
    @property(cc.Label) sharkOddsTxt0: cc.Label = null;
    @property(cc.Label) profitTxt0: cc.Label = null;

    private _pokerMasterRoom: pf.Nullable<domain.PokerMasterRoom> = null;
    // 從humanboy抄來的
    // @property(cc.Prefab) prefabCard: cc.Prefab = null;
    @property(cc.SpriteFrame) cardbackSpriteFrame: cc.SpriteFrame = null;

    @property(cc.Sprite) bgFishWinBg: cc.Sprite = null; // 渔夫胜背景
    @property(cc.Sprite) bgSharkWinBg: cc.Sprite = null; // 鲨鱼胜背景

    NUM_0: number = 6;
    NUM_1: number = 4;
    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._pokerMasterRoom = context.room as domain.PokerMasterRoom;

        cr.UIUtil.adaptWidget(this.node, true);
        let isCH = pf.LANGUAGE_GROUPS.zh_CN === pf.languageManager.currentLanguage;
        this._totalbetText = cc.find('totalbet_text', this.layout2);
        this._totalbetTxt = cc.find('totalbet_txt', this.layout2);
        this._profitTxt = cc.find('profit_txt', this.layout2);
        this._profitTxt.color = this.profitTxtColor;
        this.profitTxt0.node.color = new cc.Color(255, 106, 7);

        this._firstBetTxt = cc.find('first_bet_txt', this.layout2);
        this._fishermanTxt = cc.find('fisherman_txt', this.layout2);
        this._fishermanBetTxt = cc.find('fisherman_bet_txt', this.layout2);
        this._fishermanOddsTxt = cc.find('fisherman_odds_txt', this.layout2);

        // this._equal_txt = cc.find('equal_txt', this.layout2);
        // this._equal_bet_txt = cc.find('equal_bet_txt', this.layout2);
        // this._equal_bet_text = cc.find('equal_bet_text', this.layout2);
        // this._equal_odds_txt = cc.find('equal_odds_txt', this.layout2);
        // this._equal_odds_text = cc.find('equal_odds_text', this.layout2);

        this._sharkTxt = cc.find('shark_txt', this.layout2);
        this._sharkBetTxt = cc.find('shark_bet_txt', this.layout2);
        this._sharkOddsTxt = cc.find('shark_odds_txt', this.layout2);

        this._secondBetTxt = cc.find('second_bet_txt', this.layout2);

        for (let i = 0; i < 5; i++) {
            let tempTxt = this.layout2.getChildByName('paixin_txt_' + i);
            let tempNum0 = this.layout2.getChildByName(pf.StringUtil.formatC('paixin_num_%d_0', i));
            let tempNum1 = this.layout2.getChildByName(pf.StringUtil.formatC('paixin_num_%d_1', i));
            let tempNum2 = this.layout2.getChildByName(pf.StringUtil.formatC('paixin_num_%d_2', i));
            if (!isCH) {
                tempTxt.getComponent(cc.Label).fontSize = tempTxt.getComponent(cc.Label).fontSize - this.NUM_1;
            }
            this.paixingTxtArr.push(tempTxt);
            this.paixingPeilvArr.push(tempNum0.getComponent(cc.Label));
            this.paixingXiazhuArr.push(tempNum1.getComponent(cc.Label));
            this.paixingWinArr.push(tempNum2.getComponent(cc.Label));
        }

        this._fishermanNum = cc.find('fisherman_num', this.layout2);
        this._sharkNum = cc.find('shark_num', this.layout2);
        // this._equal_num = cc.find('equal_num', this.layout2);

        let vFishermanChildrens: cc.Node[] = this.panelFishermanCard.children;
        for (let i = 0; i < vFishermanChildrens.length; ++i) {
            let leftCard: cc.Node = this.panelFishermanCard.getChildByName(`card_${i}`);
            if (leftCard) {
                // let card: CowboyCard = CowboyCard.create(this._strCardFacePath, this._strCardBackPath);
                let card: PokerCardControl = this.createPokerCard();
                card.ResetFromNode(leftCard);
                card.SetFace(false);
                this._vFishermanCard.push(card);
            }
        }

        let vPubChildrens: cc.Node[] = this.panelPublicCard.children;
        for (let i = 0; i < vPubChildrens.length; ++i) {
            let pubCard: cc.Node = this.panelPublicCard.getChildByName(`card_${i}`);
            if (pubCard) {
                // let card: CowboyCard = CowboyCard.create(this._strCardFacePath, this._strCardBackPath);
                let card: PokerCardControl = this.createPokerCard();
                card.ResetFromNode(pubCard);
                card.SetFace(false);
                this._vPubsCard.push(card);
            }
        }

        let vSharkChildrens: cc.Node[] = this.panelSharkCard.children;
        for (let i = 0; i < vSharkChildrens.length; ++i) {
            let sharkCard: cc.Node = this.panelSharkCard.getChildByName(`card_${i}`);
            if (sharkCard) {
                // let card: CowboyCard = CowboyCard.create(this._strCardFacePath, this._strCardBackPath);
                let card: PokerCardControl = this.createPokerCard();
                card.ResetFromNode(sharkCard);
                card.SetFace(false);
                this._vSharkCard.push(card);
            }
        }

        if (pf.system.view.isFullScreen()) {
            let offsetx = pf.system.view.isScreenLandscape() ? pf.system.view.iphoneXOffset : 0;
            this.panelMain.setPosition(
                pf.system.view.width / 2 - this.panelMain.width / 2 - offsetx + this.panelMain.width / 2,
                this.panelMain.getPosition().y
            );
        }
        // 原版
        // if (cv.config.IS_FULLSCREEN) {
        //     let offsetx = cv.native.isScreenLandscape() ? cv.viewAdaptive.IPHONEX_OFFSETY : 0;
        //     this.panelMain.setPosition(cc.winSize.width / 2 - this.panelMain.width / 2 - offsetx + this.panelMain.width / 2, this.panelMain.getPosition().y);
        // }

        this.startPos = this.node.getPosition();
        // 翻页
        this.btnFirst.node.on(
            'click',
            (event: cc.Event): void => {
                this.firstPage();
            },
            this
        );
        this.btnLast.node.on(
            'click',
            (event: cc.Event): void => {
                this.lastPage();
            },
            this
        );
        this.btnBefore.node.on(
            'click',
            (event: cc.Event): void => {
                this.beforePage();
            },
            this
        );
        this.btnNext.node.on(
            'click',
            (event: cc.Event): void => {
                this.nextPage();
            },
            this
        );
        this.node.on(cc.Node.EventType.TOUCH_END, this.hide, this);

        // cv.resMgr.setSpriteFrame(this.titleSpr.node, cv.config.getLanguagePath('game/pokermaster/review_title'));
        // this.titleSpr.spriteFrame = pf.addressableAssetManager.getAsset(
        //     macros.Dynamic_Assets.POKER_MASTER_REVIEW_TITLE_SPRITE
        // );
        pf.addressableAssetManager
            .loadAsset(macros.Dynamic_Assets.POKER_MASTER_REVIEW_TITLE_SPRITE)
            .then((spriteFrame: cc.SpriteFrame) => {
                this.titleSpr.spriteFrame = spriteFrame;
            });

        if (!isCH) {
            this.txtPaixing.fontSize = this.txtPaixing.fontSize - this.NUM_0;
            let labArr = [
                this._totalbetTxt,
                this._profitTxt,
                this._firstBetTxt,
                this._fishermanTxt,
                this._fishermanBetTxt,
                this._fishermanOddsTxt,
                this._sharkTxt,
                this._sharkBetTxt,
                this._sharkOddsTxt,
                this._secondBetTxt
            ];
            let labLen = labArr.length;
            for (let i = 0; i < labLen; ++i) {
                let lab = labArr[i].getComponent(cc.Label);
                if (lab.fontSize > 24) {
                    lab.fontSize = lab.fontSize - this.NUM_0;
                } else {
                    lab.fontSize = lab.fontSize - this.NUM_1;
                }
            }
        }

        this.registerMsg();
        this.onChangeLanguage();
        cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
    }

    private createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(this.cardbackSpriteFrame);
        return ctrl;
    }

    private OnAppEnterForeground() {
        if (!this || !this.node || !cc.isValid(this.node, true)) return;
        this._pokerMasterRoom.reqBetReview();
    }

    private onChangeLanguage(): void {
        let totalBetS = cr.UIUtil.updateAndMeasureLabel(
            this._totalbetTxt.getComponent(cc.Label),
            pf.languageManager.getString('PokerMaster_totalbet_txt')
        );
        this._profitTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_profit_txt');

        this._fishermanTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_fishermanwin_txt');
        this._sharkTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_shark_txt');
        // this._equal_txt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_equal_txt');

        let fishermanBetS = cr.UIUtil.updateAndMeasureLabel(
            this._fishermanBetTxt.getComponent(cc.Label),
            pf.languageManager.getString('PokerMaster_bet_txt')
        );
        this._sharkBetTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_bet_txt');
        // this._equal_bet_txt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_bet_txt');

        let fishermanOddsS = cr.UIUtil.updateAndMeasureLabel(
            this._fishermanOddsTxt.getComponent(cc.Label),
            pf.languageManager.getString('PokerMaster_odds_txt')
        );
        this._sharkOddsTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_odds_txt');
        // this._equal_odds_txt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_odds_txt');
        for (let i = 0; i < 5; i++) {
            this.paixingTxtArr[i].getComponent(cc.Label).string = pf.languageManager.getString(
                'PokerMaster_paixin_txt_' + i
            );
        }

        this._firstBetTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_first_bet_txt');
        this._secondBetTxt.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_second_bet_txt');

        this._totalbetText.setPosition(this._totalbetTxt.x + totalBetS.width, this._totalbetTxt.y);
        this.fishermanBetTxt0.node.setPosition(this._fishermanBetTxt.x + fishermanBetS.width, this._fishermanBetTxt.y);
        this.fishermanOddsTxt0.node.setPosition(
            this._fishermanOddsTxt.x + fishermanOddsS.width,
            this._fishermanOddsTxt.y
        );
    }

    private registerMsg(): void {
        // let MsgPrefix: string = PokerMasterDef.LocalMsg().MsgPrefix;
        // cv.MessageCenter.register(MsgPrefix + PokerMasterDef.LocalMsg().UPDATE_REVIEW, this._onUpdateReview.bind(this), this.node);
        this._pokerMasterRoom.addListener('updateReview', this._onUpdateReview.bind(this)); // 更新回顧
    }

    private unregisterMsg(): void {
        // let MsgPrefix: string = PokerMasterDef.LocalMsg().MsgPrefix;
        // cv.MessageCenter.unregister(MsgPrefix + PokerMasterDef.LocalMsg().UPDATE_REVIEW, this.node);
    }

    onDestroy() {
        this.unregisterMsg();
        cc.game.off(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
    }

    /**
     * 前一页
     */
    private beforePage(): void {
        let page = this._curPage - 1 >= 0 ? this._curPage - 1 : this._curPage;
        this.updatePage(page);
    }

    /**
     * 后一页
     */
    private nextPage(): void {
        let page = this._curPage + 1 < this._pokerMasterRoom.betReview.length ? this._curPage + 1 : this._curPage;
        this.updatePage(page);
    }

    /**
     * 第一页
     */
    private firstPage(): void {
        this.updatePage(0);
    }

    /**
     * 最后一页
     */
    private lastPage(): void {
        this.updatePage(this._pokerMasterRoom.betReview.length - 1);
    }

    show(anim: boolean = true): void {
        this._autoAnimFunc(true, anim);
        // this.setViewType(eType);
        // 打开请求
        this._pokerMasterRoom.reqBetReview();
    }

    hide(anim: boolean = true): void {
        this._autoAnimFunc(false, anim);
    }

    private _autoAnimFunc(bOpen: boolean, bAnim: boolean): void {
        this.node.active = true;
        this.panelMain.active = true;
        this.panelMain.stopAllActions();

        let duration = 0.3;
        let seq: cc.Action = null;

        // 全面屏横向偏移量
        let offsetX: number = PokerMasterControl.gFullScreenOffset.x;

        if (bOpen) {
            let startPos: cc.Vec2 = cc.v2(cc.winSize.width / 2 + this.panelMain.width / 2, 0);
            let endPos: cc.Vec2 = cc.v2(cc.winSize.width / 2 - this.panelMain.width / 2 - offsetX, 0);

            this.panelMain.setPosition(startPos);
            let cb: cc.ActionInstant = cc.callFunc((): void => {
                this.panelMain.setPosition(endPos);
                this.imgShield.getComponent(cc.BlockInputEvents).enabled = false;
            });

            if (bAnim) {
                let mt: cc.ActionInterval = cc.moveTo(duration, endPos);
                let ebo: cc.ActionInterval = mt.easing(cc.easeBackOut());
                seq = cc.sequence(ebo, cb);
            } else {
                seq = cb;
            }
        } else {
            let startPos: cc.Vec2 = cc.v2(cc.winSize.width / 2 - this.panelMain.width / 2 - offsetX, 0);
            let endPos: cc.Vec2 = cc.v2(cc.winSize.width / 2 + this.panelMain.width / 2, 0);

            this.panelMain.setPosition(startPos);
            let cb: cc.ActionInstant = cc.callFunc((): void => {
                this.panelMain.setPosition(endPos);
                this.imgShield.getComponent(cc.BlockInputEvents).enabled = false;
                this.node.active = false;
            });

            if (bAnim) {
                let mt: cc.ActionInterval = cc.moveTo(duration, endPos);
                let ebi: cc.ActionInterval = mt.easing(cc.easeBackIn());
                seq = cc.sequence(ebi, cb);
            } else {
                seq = cb;
            }
        }

        if (seq) {
            this.panelMain.runAction(seq);
            this.imgShield.getComponent(cc.BlockInputEvents).enabled = true;
        }
    }

    /**
     * 显示
     */
    // autoShow(zorder?: number, bAnim: boolean = true): void {
    //     this.node.active = true;
    //     this.unscheduleAllCallbacks();
    //     this.node.stopAllActions();
    //     // 用意應該是遮蔽操作?看別處似乎是直接用 sprite 上掛cc.BlockInputEvents 處理
    //     // this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
    //     // cv.action.removeShieldLayer(cc.director.getScene(), `shieldLayer-showAction_${this.node.name}`);
    //     if (bAnim) {
    //         // 沒得抄先拿掉
    //         // cv.action.showAction(this.node, cv.action.eMoveActionDir.EMAD_TO_LEFT, cv.action.eMoveActionType.EMAT_FADE_IN);
    //     }

    //     // 打开请求
    //     this._pokerMasterRoom.reqBetReview ();
    // }

    /**
     * 隐藏
     */
    // autoHide(bAnim: boolean = true): void {
    //     if (this.node.active) {
    //         this.node.stopAllActions();
    //         // 用意應該是遮蔽操作?看別處似乎是直接用 sprite 上掛cc.BlockInputEvents 處理
    //         // this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
    //         // cv.action.removeShieldLayer(cc.director.getScene(), `shieldLayer-showAction_${this.node.name}`);
    //         this.node.setPosition(this.startPos);
    //         if (bAnim) {
    //             // 沒得抄先拿掉
    //             // cv.action.showAction(this.node, cv.action.eMoveActionDir.EMAD_TO_RIGHT, cv.action.eMoveActionType.EMAT_FADE_OUT, cv.action.delay_type.NORMAL);
    //         }
    //         else {
    //             this.node.active = false;
    //         }
    //     }
    // }

    private _onUpdateReview(): void {
        this._curPage = this._pokerMasterRoom.betReview.length;

        this.updatePage(this._curPage - 1);
    }

    /* eslint complexity: ["error", { max: 38 }] */
    /* eslint-disable no-fallthrough */
    private updatePage(index: number): void {
        let bHide = index < 0 || this._pokerMasterRoom.betReview[index] === null;
        for (let i = 0; i < 2; ++i) {
            this._vFishermanCard[i].node.active = !bHide;
        }

        for (let i = 0; i < 5; ++i) {
            this._vPubsCard[i].node.active = !bHide;
        }

        for (let i = 0; i < 2; ++i) {
            this._vSharkCard[i].node.active = !bHide;
        }

        cc.find('panel_main/paixing_bg', this.node).active = !bHide;
        if (bHide) return;
        this._curPage = index;
        this._totalbetText.getComponent(cc.Label).string = cr.CurrencyUtil.convertServerAmountToDisplayString(
            this._pokerMasterRoom.betReview[index].totalBet
        );

        let bTotalWin = this._pokerMasterRoom.betReview[index].totalWin > 0;
        if (this.bRefreshProfitTxt0Color) this.profitTxt0.node.color = bTotalWin ? this.winColor : this.loseColor;
        let profitTxtS = cr.UIUtil.updateAndMeasureLabel(
            this.profitTxt0,
            (bTotalWin ? '+' : '') +
                cr.CurrencyUtil.convertServerAmountToDisplayString(this._pokerMasterRoom.betReview[index].totalWin)
        );
        this._profitTxt.setPosition(this.profitTxt0.node.x - profitTxtS.width, this._profitTxt.y);

        // 显示页数
        let len = pf.DataUtil.getArrayLength(this._pokerMasterRoom.betReview);
        this.txtPage0.string = pf.TypeUtil.toSafeString(index + 1);
        this.txtPage1.string = '/' + pf.TypeUtil.toSafeString(len);
        let changeBtn = (btn: cc.Button, canTouch: boolean): void => {
            btn.interactable = canTouch;
            btn.enabled = canTouch;
            let str = canTouch ? macros.Assets.POKER_MASTER_TOUCH_SPRITE : macros.Assets.POKER_MASTER_NO_TOUCH_SPRITE; // 'zh_CN/game/pokermaster/ui/' + (canTouch ? 'touch' : 'no_touch');
            // cv.resMgr.setButtonFrame(btn.node, str, str, str, str);
            pf.addressableAssetManager
                .loadAsset<cc.SpriteFrame>(str /* macros.Dynamic_Assets.SPIN_NORMAL_SPRITE */)
                .then((spriteFrame) => {
                    btn.normalSprite = spriteFrame;
                    btn.pressedSprite = spriteFrame;
                    btn.hoverSprite = spriteFrame;
                });

            pf.addressableAssetManager
                .loadAsset<cc.SpriteFrame>(str /* macros.Dynamic_Assets.SPIN_DISABLE_SPRITE */)
                .then((spriteFrame) => {
                    btn.disabledSprite = spriteFrame;
                });
        };
        let btnArr = [true, true, true, true];
        if (index === 0) {
            btnArr[0] = false;
            btnArr[1] = false;
            if (len === 1) {
                btnArr[2] = false;
                btnArr[3] = false;
            }
        } else if (index > 0 && index + 1 === len) {
            btnArr[2] = false;
            btnArr[3] = false;
        }

        changeBtn(this.btnFirst, btnArr[0]);
        changeBtn(this.btnBefore, btnArr[1]);
        changeBtn(this.btnNext, btnArr[2]);
        changeBtn(this.btnLast, btnArr[3]);

        let bet = this._pokerMasterRoom.betReview[index];
        let isEqual = false;
        let winOpsLen = bet.winOps.length;
        for (let i = 0; i < winOpsLen; i++) {
            if (bet.winOps[i] === network.BetZoneOption.EQUAL) {
                isEqual = true;
            }
        }
        let str = bet.level !== 8 ? `M_UITitle${bet.level + 112}` : 'Humanboy_game_card_type_four_of_a_kind';
        this.txtPaixing.string = pf.languageManager.getString(str);
        for (let i = 0; i < 2; ++i) {
            // fisherman
            let card = new CardItem(bet.fisherCard[i].number, bet.fisherCard[i].suit); // pokermaster_proto.CardItem.create(bet.fisherCard[i]);
            this._vFishermanCard[i].SetContent(card.number, card.suit);
            this._vFishermanCard[i].SetFace(true);
        }

        for (let i = 0; i < 5; ++i) {
            let card = new CardItem(bet.pubCard[i].number, bet.pubCard[i].suit); // pokermaster_proto.CardItem.create(bet.pubCard[i]);
            this._vPubsCard[i].SetContent(card.number, card.suit);
            this._vPubsCard[i].SetFace(true);
        }

        for (let i = 0; i < 2; ++i) {
            let card = new CardItem(bet.sharkCard[i].number, bet.sharkCard[i].suit); // pokermaster_proto.CardItem.create(bet.sharkCard[i]);
            this._vSharkCard[i].SetContent(card.number, card.suit);
            this._vSharkCard[i].SetFace(true);
        }

        let tempArr = [
            network.BetZoneOption.FIVE_NONE_1DUI,
            network.BetZoneOption.FIVE_2DUI,
            network.BetZoneOption.FIVE_SAN_SHUN_TONG,
            network.BetZoneOption.FIVE_GOURD,
            network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4
        ];
        // 区域信息
        for (let d of this._pokerMasterRoom.betReview[index].detail) {
            // for (let i = 0; i < this._pokerMasterRoom.betReview[index].detail.length; ++i) {
            let detail = d;
            if (detail.option === network.BetZoneOption.FISHER_WIN) {
                this._fishermanTxt.color = this.loseColor;
                this._fishermanOddsTxt.color = this.loseColor;
                this.fishermanOddsTxt0.node.color = this.loseColor;
                this._fishermanBetTxt.color = this.loseColor;
                this.fishermanBetTxt0.node.color = this.loseColor;
                this._fishermanNum.color = this.loseColor;
                if (this.bgFishWinBg !== null) this.bgFishWinBg.node.active = false;
                // 渔夫胜
                if (detail.winAmt > 0 && !isEqual) {
                    this._fishermanTxt.color = this.winColor;
                    this._fishermanOddsTxt.color = this.winColor;
                    this.fishermanOddsTxt0.node.color = this.winColor;
                    this._fishermanBetTxt.color = this.winColor;
                    this.fishermanBetTxt0.node.color = this.winColor;
                    this._fishermanNum.color = this.winColor;
                    if (this.bgFishWinBg !== null) this.bgFishWinBg.node.active = true;
                    // if (this.bgSharkWinBg !== null) this.bgSharkWinBg.node.active = false;
                }

                this.fishermanOddsTxt0.string =
                    cr.CurrencyUtil.convertServerAmountToDisplayString(detail.odds) +
                    (pf.LANGUAGE_GROUPS.zh_CN === pf.languageManager.currentLanguage ? '倍' : '');
                this.fishermanBetTxt0.string = cr.CurrencyUtil.convertServerAmountToDisplayString(detail.betAmount);
                let isWin = detail.winAmt > 0;
                this._fishermanNum.getComponent(cc.Label).string =
                    (isWin ? '+' : '') + cr.CurrencyUtil.convertServerAmountToDisplayString(detail.winAmt);
            } else if (detail.option === network.BetZoneOption.SHARK_WIN) {
                this._sharkTxt.color = this.loseColor;
                this._sharkOddsTxt.color = this.loseColor;
                this.sharkOddsTxt0.node.color = this.loseColor;
                this._sharkBetTxt.color = this.loseColor;
                this.sharkBetTxt0.node.color = this.loseColor;
                this._sharkNum.color = this.loseColor;
                if (this.bgSharkWinBg !== null) this.bgSharkWinBg.node.active = false;
                // 鲨鱼胜
                if (detail.winAmt > 0 && !isEqual) {
                    this._sharkTxt.color = this.winColor;
                    this._sharkOddsTxt.color = this.winColor;
                    this.sharkOddsTxt0.node.color = this.winColor;
                    this._sharkBetTxt.color = this.winColor;
                    this.sharkBetTxt0.node.color = this.winColor;
                    this._sharkNum.color = this.winColor;
                    // if (this.bgFishWinBg !== null) this.bgFishWinBg.node.active = false;
                    if (this.bgSharkWinBg !== null) this.bgSharkWinBg.node.active = true;
                }

                let sharkOddsS = cr.UIUtil.updateAndMeasureLabel(
                    this.sharkOddsTxt0,
                    cr.CurrencyUtil.convertServerAmountToDisplayString(detail.odds) +
                        (pf.LANGUAGE_GROUPS.zh_CN === pf.languageManager.currentLanguage ? '倍' : '')
                );
                this._sharkOddsTxt.setPosition(this.sharkOddsTxt0.node.x - sharkOddsS.width, this._sharkOddsTxt.y);
                let sharkBetS = cr.UIUtil.updateAndMeasureLabel(
                    this.sharkBetTxt0,
                    cr.CurrencyUtil.convertServerAmountToDisplayString(detail.betAmount)
                );
                this._sharkBetTxt.setPosition(this.sharkBetTxt0.node.x - sharkBetS.width, this._sharkBetTxt.y);
                let isWin = detail.winAmt > 0;
                this._sharkNum.getComponent(cc.Label).string =
                    (isWin ? '+' : '') + cr.CurrencyUtil.convertServerAmountToDisplayString(detail.winAmt);
            } else {
                for (let j = 0; j < 5; j++) {
                    if (detail.option === tempArr[j]) {
                        this.paixingPeilvArr[j].string =
                            cr.CurrencyUtil.convertServerAmountToDisplayString(detail.odds) +
                            (pf.LANGUAGE_GROUPS.zh_CN === pf.languageManager.currentLanguage ? '倍' : '');
                        this.paixingXiazhuArr[j].string = cr.CurrencyUtil.convertServerAmountToDisplayString(
                            detail.betAmount
                        );

                        let isWin = detail.winAmt > 0;
                        this.paixingWinArr[j].string =
                            (isWin ? '+' : '') + cr.CurrencyUtil.convertServerAmountToDisplayString(detail.winAmt);

                        this.setLabColor(this.paixingPeilvArr[j].node, isWin);
                        this.setLabColor(this.paixingXiazhuArr[j].node, isWin);
                        this.setLabColor(this.paixingWinArr[j].node, isWin);
                        this.setLabColor(this.paixingTxtArr[j], isWin);
                        break;
                    }
                }
            }
        }
    }

    setLabColor(lab: cc.Node, isWin: boolean): void {
        lab.color = isWin ? this.winColor : this.loseColor;
    }
}
