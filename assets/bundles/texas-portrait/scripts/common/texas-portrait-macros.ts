export namespace macros {
    export const BUNDLE_NAME = 'texas-portrait';

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/texas-portrait-addressable-assets-zh_cn',
        EN_US = 'configs/texas-portrait-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        HI_IN = 'languages/string-hi_in',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph'
    }

    export enum DynamicAsset {
        BG_1 = 'texas-portrait-dynamic.room-01',
        BG_2 = 'texas-portrait-dynamic.room-02',
        Table_1 = 'texas-portrait-dynamic.table-01'
    }
}
