import { registerEntry } from 'pf';
import * as pf from 'pf';
import { macros } from './common/texas-portrait-macros';
import BUNDLE_NAME = macros.BUNDLE_NAME;
import * as common from 'common';
import * as network from '../../../bundles/texas-common/scripts/network/texas-network-index';
import type * as domain from '../../../bundles/texas-common/scripts/domain/texas-domain-index';
import { TexasService, BarrageService } from '../../../bundles/texas-common/scripts/domain/texas-domain-index';
import PopupManager = common.components.PopupManager;
import BannerPopupOptions = common.components.BannerPopupOptions;

@registerEntry(BUNDLE_NAME)
export class TexasPortraitEntry extends pf.BundleEntryBase {
    private _socket: pf.Nullable<pf.client.ISocket> = null;
    private _texasService: pf.Nullable<TexasService> = null;
    private _room: pf.Nullable<domain.TexasRoom> = null;

    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_GAME;
    }

    protected getAddressableConfigPath(language?: pf.LANGUAGE_GROUPS) {
        const targetLanguage = language === null ? pf.languageManager.currentLanguage : language;
        switch (targetLanguage) {
            case pf.LANGUAGE_GROUPS.zh_CN:
                return macros.Addressable_Config_Path.ZH_CN;
            default:
                return macros.Addressable_Config_Path.EN_US;
        }
    }

    protected getBundleName(): string {
        return macros.BUNDLE_NAME;
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        const context = pf.app.getGameContext<pf.services.GameContext>();
        this._socket = context?.socket;
        if (!this._socket) {
            return Promise.reject(new pf.InvalidParameterError('options.socket is null or undefined!'));
        }
        if (options.roomId === undefined) {
            return Promise.reject(new pf.InvalidParameterError('options.roomId is undefined!'));
        }
        try {
            await this.loginGame(context, options.roomId, options.gameId); //
            await this.loadConfigs();
            const assetLoader = new pf.AddressalbeAssetLoader();
            assetLoader.addLoadAddressableGroupTask(this.getBundleName());
            await assetLoader.start();

            const asyncOp = new pf.AsyncOperation<void>();
            const scene = await this.loadScene();
            cc.director.runScene(
                scene,
                () => {
                    if (this.onBeforeLoadScene) {
                        this.onBeforeLoadScene();
                    }
                },
                () => {
                    asyncOp.resolve();
                }
            );
            return asyncOp.promise;
        } catch (err) {
            this.exit();
            throw err;
        }
    }

    async loadScene(): Promise<cc.SceneAsset> {
        const sceneName = 'Game';
        return new Promise((resolve, reject) => {
            this.bundle.loadScene(sceneName, (err, scene) => {
                if (err) {
                    cc.warn(err);
                    reject(err);
                } else {
                    resolve(scene);
                }
            });
        });
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${this.getBundleName()} onExit`);
        pf.bundleManager.exitBundle('texas-common');
        pf.bundleManager.releaseAll(this.bundle);

        const context = pf.app.getGameContext<pf.services.GameContext>();
        pf.serviceManager.unregister(TexasService);
        pf.serviceManager.unregister(BarrageService);

        this._texasService = null;
        this._room = null;

        this._socket?.removeGameSession(network.TexasSession);
        this._socket = null;

        context.bundle = '';
        context.session = null;
        context.service = null;
        context.room = null;
        context.exitCallback = null;
        pf.bundleManager.releaseAll(this.bundle);
    }

    protected async loginGame(context: pf.services.GameContext, roomId: number, gameId: number): Promise<void> {
        try {
            const texasSession = this._socket.createGameSession(network.TexasSession, gameId);
            texasSession.setGameId(gameId);

            this._texasService = new TexasService(texasSession, context.client);
            pf.serviceManager.register(this._texasService);

            cc.log(`login ${this.getBundleName()}`);
            const authService = pf.serviceManager.get(pf.services.AuthService);
            const ip = authService.currentUser.ip;
            await this._texasService.login(ip);

            cc.log(`${this.getBundleName()} join room`);
            this._room = (await this._texasService.joinRoom(roomId)) as domain.TexasRoom;

            // store game state to game context
            context.bundle = this.getBundleName();
            context.gameId = gameId;
            context.session = texasSession;
            context.service = this._texasService;
            context.room = this._room;
            context.roomId = this._room.id;
            pf.serviceManager.register(new BarrageService(this._room));
        } catch (err) {
            // NOTE:
            // Do not throw error to prevent exit bundle when login fail.
            // Let GameSession re join room when socket reconnect
            cc.warn(`login ${this.getBundleName()} failed: ${(err as Error).message})`);

            if (this._texasService) {
                pf.serviceManager.unregister(TexasService);
                this._socket.removeGameSession(network.TexasSession);
                this._texasService = null;
            }

            switch (err.errorCode) {
                case 1250:
                case 1260:
                case 1280:
                case 22:
                    break;
                case 1261:
                    {
                        const options: BannerPopupOptions = {
                            content: pf.StringUtil.formatC(
                                pf.languageManager.getString('Recaptcha_ResultForbid'),
                                err.cd
                            )
                        };
                        PopupManager.showBanner(options);
                    }
                    break;
                default: {
                    const options: BannerPopupOptions = {
                        content: err
                    };
                    PopupManager.showBanner(options);
                }
            }

            throw err;
        }
    }
}
