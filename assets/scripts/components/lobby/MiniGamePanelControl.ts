const { ccclass, property } = cc._decorator;

import * as pf from '../../../poker-framework/scripts/pf';
import RoomListViewControl from './RoomListViewControl';
import type { IExtraOptions } from '../../common/define';
import { GameListItemEvent, GameListItemControl } from './GameListItemControl';
import { RoomListItemControl, RoomListItemEvent } from './RoomListItemControl';
import { TexasListViewControl } from './TexasListViewControl';
import { TexasListItemControl, TexasListItemEvent } from './TexasListItemControl';
// import { Config, LayoutMode } from 'main';

type ProgressCallback = (
    bundleCount: number,
    totalBundle: number,
    downloadBytes: number,
    totalBytes: number,
    percentage: number
) => void;

@ccclass
export class MiniGamePanelControl extends cc.Component {
    @property(RoomListViewControl) roomListControl: RoomListViewControl = null;
    @property(TexasListViewControl) texasListControl: TexasListViewControl = null;
    @property(cc.Node) texasGameList: cc.Node = null;
    @property(cc.Node) miniGameList: cc.Node = null;
    @property(cc.Button) texasGameTab: cc.Button = null;
    @property(cc.Button) miniGameTab: cc.Button = null;

    // LIFE-CYCLE CALLBACKS:

    _authService: pf.services.AuthService = null;

    _socket: pf.client.ISocket = null;

    private _extraOptions: IExtraOptions = null;
    private _cashGameBundle: string = null; // 默认的现金游戏bundle
    persistRoot: cc.Node = null;

    onLoad() {
        pf.languageManager.addListener('languageChange', this.onLanguageChange.bind(this));
        pf.languageManager.currentLanguage = pf.LANGUAGE_GROUPS.en_US;

        this.node.on(GameListItemEvent.EnterGame, (event: cc.Event) => {
            const itemCtrl = (event.target as cc.Node).getComponent(GameListItemControl);
            this.enterGame(itemCtrl.gameId, itemCtrl.bundle);
        });

        this.node.on(RoomListItemEvent.EnterRoom, (event: cc.Event) => {
            const itemCtrl = (event.target as cc.Node).getComponent(RoomListItemControl);
            this.enterRoom(itemCtrl.roomID, itemCtrl.bundle);
        });

        this.node.on(TexasListItemEvent.EnterTexas, (event: cc.Event) => {
            const itemCtrl = (event.target as cc.Node).getComponent(TexasListItemControl);
            this.enterTexas(itemCtrl.roomId, itemCtrl.gameId, itemCtrl.bundle);
        });

        // create a persistent node for the mini game panel
        this.persistRoot = cc.director.getScene().getChildByName('rootPopup') || cc.find('rootPopup');
        if (!this.persistRoot) {
            cc.warn('rootPopup node not found, creating a new one');
            const newPersistRoot = new cc.Node('rootPopup');

            newPersistRoot.setAnchorPoint(0.5, 0.5);
            newPersistRoot.setPosition(562, 1428);
            cc.game.addPersistRootNode(newPersistRoot);
            this.persistRoot = newPersistRoot;
        }
    }

    async start() {
        this._authService = pf.serviceManager.get(pf.services.AuthService);
        if (!this._authService) {
            cc.error('AuthService does not exist');
            return;
        }

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._socket = context.socket;
        if (!this._socket) {
            cc.error('socket does not exist');
            return;
        }

        this.showTexasGameList();

        this.texasListControl.getTexasGameList();
    }

    async enterGame(gameId: number, bundle: string): Promise<void> {
        try {
            // if (gameId === pf.client.GameId.Texas) {
            //     this._cashGameBundle = bundle;
            //     Config.layoutMode = bundle === 'texas-portrait' ? LayoutMode.Portrait : LayoutMode.Landscape;
            //     await this._socket.getClubCurrentBoard();
            // } else {
            //     this._cashGameBundle = null;
            const resp = await this._socket.getRoomList(gameId);
            this.roomListControl.updateModel(bundle, resp.games);
            this.roomListControl.show();
            // }
        } catch (err) {
            cc.warn((err as Error).message);
        }
    }

    async enterRoom(roomId: number, bundle: string): Promise<void> {
        cc.log(RoomListItemEvent.EnterRoom, roomId, bundle);

        try {
            this.roomListControl.hide();
            this.hide();

            const enableFeatureTest = this._extraOptions?.isLoadFeatureTestBundle ?? false;

            if (enableFeatureTest) {
                await this.loadFeatureTestBundle();
            }

            // if (!this._cashGameBundle) {
            //     await this.loadCommonBundle();
            //     await this.loadCommonResourceBundle();
            // } else {
            //     await this.loadCommonBundle();
            //     await this.loadTexasCommonBundle();
            //     await this.loadCommonResourceBundle();
            // }

            let progress = 0;
            const options: pf.IBundleOptions = {
                roomId,
                onProgress: (finish: number, total: number) => {
                    if (finish < total) {
                        const newProgress = Math.trunc((100 * finish) / total);
                        if (newProgress > progress) {
                            progress = newProgress;
                            // cc.log(`download bundle asset: ${progress}%`)
                        }
                    } else {
                        // cc.log('switch to scene');
                    }
                }
            };
            const updateItem = pf.updateManager.getUpdateItem(bundle);
            const entry = await pf.updateManager.loadBundle(updateItem, options);
            entry.onBeforeLoadScene = () => {
                // if (cc.sys.isBrowser && !pf.system.view.isScreenLandscape()) {
                //     pf.system.view.setLandscape();
                // }

                if (!pf.system.view.isScreenLandscape()) {
                    console.log('!!!!!! MiniGamePanelControl setLandscape');
                    pf.system.view.setLandscape();
                }

                // if (bundle === 'texas-portrait') {
                //     pf.system.view.setPortrait();
                // } else {
                //     pf.system.view.setLandscape();
                // }
            };
            entry.onAfterExit = () => {
                if (enableFeatureTest) {
                    const bundleName = 'feature-test';
                    pf.bundleManager.exitBundle(bundleName);
                }

                if (cc.sys.isBrowser && pf.system.view.isScreenLandscape()) {
                    pf.system.view.setPortrait();
                }

                cc.director.loadScene('Lobby', () => {
                    this.show();
                });
            };
            // await entry.enter(options);
            await pf.updateManager.enterBundle(updateItem, options);
            entry.preload();

            // if (!this._cashGameBundle) {
            // const commonEntry = pf.bundleManager.getEntry('common');
            // commonEntry?.preload();
            const commonResourceEntry = pf.bundleManager.getEntry('common-resource');
            commonResourceEntry?.preload();
            // }
        } catch (err) {
            cc.warn((err as Error).message);
        }
    }

    async enterTexas(roomId: number, gameId: number, bundle: string): Promise<void> {
        cc.error(TexasListItemEvent.EnterTexas, roomId, bundle);

        this.roomListControl.hide();
        this.hide();

        const enableFeatureTest = this._extraOptions?.isLoadFeatureTestBundle ?? false;

        if (enableFeatureTest) {
            await this.loadFeatureTestBundle();
        }

        // await this.loadCommonBundle();
        // await this.loadTexasCommonBundle();

        let progress = 0;
        const options: pf.IBundleOptions = {
            roomId,
            gameId,
            onProgress: (finish: number, total: number) => {
                if (finish < total) {
                    const newProgress = Math.trunc((100 * finish) / total);
                    if (newProgress > progress) {
                        progress = newProgress;
                        // cc.log(`download bundle asset: ${progress}%`)
                    }
                } else {
                    // cc.log('switch to scene');
                }
            }
        };
        const updateItem = pf.updateManager.getUpdateItem(bundle);
        const entry = await pf.updateManager.loadBundle(updateItem, options);
        entry.onBeforeLoadScene = () => {
            // if (bundle === 'texas-portrait') {
            pf.system.view.setPortrait();
            // } else {
            // pf.system.view.setLandscape();
            // }
        };
        entry.onAfterExit = () => {
            if (enableFeatureTest) {
                const bundleName = 'feature-test';
                pf.bundleManager.exitBundle(bundleName);
            }
            cc.director.loadScene('Lobby', () => {
                this.show();
            });
        };
        // await entry.enter(options);
        await pf.updateManager.enterBundle(updateItem, options);
        // entry.preload();

        // if (!this._cashGameBundle) {
        //     const commonEntry = pf.bundleManager.getEntry('common');
        //     commonEntry?.preload();
        //     const commonResourceEntry = pf.bundleManager.getEntry('common-resource');
        //     commonResourceEntry?.preload();
        // }
    }

    private async loadCommonResourceBundle(): Promise<void> {
        const bundleName = 'common-resource';

        const bundle = pf.bundleManager.getBundle(bundleName);
        if (!bundle) {
            const updateItem = pf.updateManager.getUpdateItem(bundleName);

            await pf.updateManager.loadBundle(updateItem);
            await pf.updateManager.enterBundle(updateItem);
        }
    }

    private async loadTexasCommonBundle(): Promise<void> {
        const bundleName = 'texas-common';

        // const bundle = pf.bundleManager.getBundle(bundleName);
        // if (!bundle) {
        cc.log(`[3in1] load and enter bundle:${bundleName}`);
        const updateItem = pf.updateManager.getUpdateItem(bundleName);

        await pf.updateManager.loadBundle(updateItem);
        await pf.updateManager.enterBundle(updateItem);
        // }
    }

    private async loadCommonBundle(): Promise<void> {
        const bundles = ['common', 'common-portrait'];

        // if (this._cashGameBundle) {
        //     if (this._cashGameBundle === 'texas-portrait') {
        //         bundles.push('common-portrait');
        //     } else {
        //         bundles.push('common-landscape');
        //     }
        // }

        const loadFunc = async (bundleName: string) => {
            // const bundle = pf.bundleManager.getBundle(bundleName);
            // if (!bundle) {
            cc.log(`[3in1] load and enter bundle:${bundleName}`);
            const updateItem = pf.updateManager.getUpdateItem(bundleName);
            const options: pf.IBundleOptions = {
                // nodes: [this.persistRoot, this.persistRoot]
            };

            await pf.updateManager.loadBundle(updateItem);
            await pf.updateManager.enterBundle(updateItem, options);
            // }
        };

        for (const bundleName of bundles) {
            await loadFunc(bundleName);
        }
    }

    private async loadFeatureTestBundle(): Promise<void> {
        const bundleName = 'feature-test';
        const updateItem = pf.updateManager.getUpdateItem(bundleName);
        const entry = await pf.updateManager.loadBundle(updateItem);
        return entry.enter();
    }

    async onLanguageChange(): Promise<void> {
        const bundleName = 'common-resource';
        const bundleInfo = pf.updateManager.localManifest.bundles.get(bundleName);
        const entry = pf.bundleManager.getEntry(bundleName);
        if (entry) {
            if (entry.bundle === null) {
                return;
            }

            const options: pf.IBundleOptions = {
                version: bundleInfo.md5
            };

            // re-enter common-resource bundle
            await entry.exit();
            entry.enter(options);
        }
    }

    show() {
        this.node.active = true;
    }

    hide() {
        this.node.active = false;
    }

    setExtraOptions(options: IExtraOptions) {
        this._extraOptions = options;
    }

    showTexasGameList() {
        this.texasGameList.active = true;
        this.miniGameList.active = false;

        this.texasGameTab.interactable = false;
        this.miniGameTab.interactable = true;
    }

    showMiniGameList() {
        this.texasGameList.active = false;
        this.miniGameList.active = true;

        this.texasGameTab.interactable = true;
        this.miniGameTab.interactable = false;
    }
}
