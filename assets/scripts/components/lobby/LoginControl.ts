// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import type { AppConfig } from 'main';
import * as pf from '../../../poker-framework/scripts/pf';
import type { IExtraOptions } from '../../common/define';
import type { PKWSession } from '../../../poker-framework/scripts/poker-client/pkw/pkw-session';

// wpk consts
// const WPK_HOST = '**************';
const WPK_HOST = '**************';
const WPK_APP_VERSION = '5.7.21';

// pkw consts
// const PKW_HOST = '**************'; // pre-launch-1
// const PKW_HOST = '************'; // pre-launch-2
// const PKW_HOST = '*************';
const PKW_ENVIRONEMNT_HOST = {
    DEV: 'coin5.dev.liuxinyi1.cn',
    STAGE: 'coin5.stg.liuxinyi1.cn'
};
const PKW_PORT = 20000;
const PKW_CLIENT_VERSION = '2.34.5';

enum Platform {
    PKW,
    WPK
}

enum Environment {
    DEV,
    STAGE
}
@ccclass
export class LoginControl extends cc.Component {
    @property(cc.EditBox)
    usernameEditBox: cc.EditBox = null;

    @property(cc.EditBox)
    passwordEditBox: cc.EditBox = null;

    @property(cc.Button)
    loginButton: cc.Button = null;

    @property(cc.Node)
    miniGamePanel: cc.Node = null;

    @property(cc.Toggle)
    autoLoginToggle: cc.Toggle = null;

    @property(cc.Label)
    autoLoginLabel: cc.Label = null;

    // @property(cc.Boolean)
    // autoLogin: boolean = false;

    @property({
        type: cc.Enum(Platform)
    })
    platform = Platform.PKW;

    @property({
        type: cc.Enum(Environment)
    })
    environment = Environment.DEV;

    // @property(cc.Boolean)
    // isMockSocket: boolean = false;

    _client: pf.client.IPokerClient = null;

    _socket: pf.client.ISocket = null;

    _context: pf.services.MiniGameContext = null;

    _account: string = null;
    _psd: string = null;

    private _extraOptions: IExtraOptions = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        pf.languageManager.currentLanguage = pf.LANGUAGE_GROUPS.zh_CN;
        const autoLoginSetting: string = cc.sys.localStorage.getItem('autoLogin');
        this.autoLoginToggle.isChecked = autoLoginSetting === 'true';
        this.autoLoginToggle.node.on('toggle', this.onToggle, this);

        this.loadDefault();
        if (this._account !== null) {
            this.usernameEditBox.string = this._account;
            this.passwordEditBox.string = this._psd;
        }
    }

    onToggle(event: cc.Toggle) {
        cc.sys.localStorage.setItem('autoLogin', event.isChecked.toString());
    }

    async start() {
        if (this.autoLoginToggle.isChecked) {
            if (!pf.app.getAppConfig<AppConfig>()) {
                // workaround for load AppConfig race condition
                await pf.sleep(1000);
                this.login();
            } else {
                this.login();
            }
        }
    }

    // update (dt) {}

    async login() {
        try {
            this.loginButton.interactable = false;

            // create game context
            this._context = new pf.services.MiniGameContext();
            pf.app.gameContext = this._context;

            this.createPokerClient();

            const authService = new pf.services.AuthService(this._client);
            pf.serviceManager.register(authService);

            this._account = this.usernameEditBox.string;
            this._psd = this.passwordEditBox.string;

            cc.log('login paltform');
            const session = await authService.login(this._account, this._psd);
            cc.log('session', session);
            cc.log('current user', authService.currentUser);

            const domainService = new pf.services.DomainService(this._client);
            pf.serviceManager.register(domainService);
            const domainInfo = domainService.getDomainInfo();
            cc.log('domian info', domainInfo);

            this.createSocket();

            this.connectSocket();
            this._socket.context.encryptedServers = (session as PKWSession).data.encry_switch;
            this.saveAccountToStorage();
        } catch (err) {
            cc.warn(err);
            this.loginButton.interactable = true;
        } finally {
            // this.loginButton.interactable = true;
        }
    }

    private async createPokerClient() {
        const config = pf.app.getAppConfig<AppConfig>();
        pf.app.clientType = pf.client.ClientType.H5;
        if (this.platform === Platform.PKW) {
            const host = this.environment === Environment.DEV ? config.devHostUrl : config.stageHostUrl;
            this._client = pf.client.PokerClient.create('pkw', host, {
                scheme: 'https://',
                appVersion: PKW_CLIENT_VERSION,
                clientType: pf.app.clientType,
                deviceType: 'win32',
                deviceId: pf.system.getDeviceUUID(),
                basePath: 'index.php',
                os: pf.system.os,
                osVersion: pf.system.osVersion,
                deviceInfo: JSON.stringify(pf.system.getDeviceInfo()),
                langauage: 'zh_CN',
                coord: {
                    latitude: 10,
                    longitude: 10
                },
                domainType: 11,
                deviceVersion: '',
                isEmulator: false,
                isInstallSiliao: true
            });

            this._context.platform = 'pkw';
        } else {
            const deviceInfo = pf.system.getDeviceInfo();
            this._client = pf.client.PokerClient.create('wpk', WPK_HOST, {
                appVersion: WPK_APP_VERSION,
                clientType: pf.app.clientType,
                deviceType: 1,
                basePath: 'wepoker',
                os: pf.system.os,
                osVersion: pf.system.osVersion,
                deviceInfo: JSON.stringify(deviceInfo),
                langauage: 'zh_CN',
                coord: {
                    latitude: 10,
                    longitude: 10
                },
                domainType: 11
            });

            this._context.platform = 'wpk';
        }

        this._context.client = this._client;
    }

    private createSocket() {
        this._socket = this._client.createSocket({
            socketApiVersion: 'v1'
        });
        this._socket.verbose = true;
        this._socket.notification.addListener('timeout', () => {
            this._socket.disconnect();
            this.connectSocket();
        });

        pf.serviceManager.register(new pf.services.ErrorMessageService());
        pf.serviceManager.register(new pf.services.RankService(this._socket));
        pf.serviceManager.register(new pf.services.WalletService(this._client));
        pf.serviceManager.register(new pf.services.PushNotificationService(this._socket));
        pf.serviceManager.register(new pf.services.ShopService(this._socket));
        pf.serviceManager.register(new pf.services.LuckTurntableService(this._socket));
        pf.serviceManager.register(new pf.services.CalmDownService(this._socket));
        pf.serviceManager.register(new pf.services.ExchangeCurrencyService(this._socket));
        pf.serviceManager.register(new pf.services.RebateService(this._socket));
        pf.serviceManager.register(new pf.services.CaptchaService(this._socket));
        pf.serviceManager.register(new pf.services.SportService(this._socket));
        pf.serviceManager.register(new pf.services.AofService(this._socket));

        const dataService = new pf.services.DataService(
            this._socket,
            this._client.getCurrentUser().userId,
            this._client.getCurrentUser().userToken
        );
        pf.serviceManager.register(dataService);

        this._context.socket = this._socket;
    }

    private connectSocket() {
        const domainService = pf.serviceManager.get(pf.services.DomainService);

        const domainInfo = domainService.getDomainInfo();

        console.log(`connect to domain ${domainService.domainIndex}`);

        let options: pf.client.ISocketOptions | undefined;

        if (cc.sys.isNative) {
            options = {
                cert: cc.url.raw('resources/ca/cacert.pem')
            };
            console.log('connectSocket to', options.cert);
        }

        this._socket
            .connect(domainInfo.gateServer, options)
            .then(async () => {
                cc.log('socket login');
                const response = await this._socket.login();
                cc.log('socket login reponse', response);

                const authService = pf.serviceManager.get(pf.services.AuthService);
                authService.currentUser.firstClubId = response.firstClubId;
                authService.currentUser.firstAlliId = response.firstAlliId;
                authService.registerNotificationHandlers();
                authService.getUserData().then((response) => {
                    cc.log('get user data after login');
                });

                await this._socket.requestSecretKey();

                if (this._context.bundle.length > 0) {
                    if (this._context.service) {
                        await this._context.service.login();

                        if (this._context.room) {
                            await this._context.room.joinRoom();
                        }
                    }
                } else {
                    this.switchToMiniGame();
                }
            })
            .catch((err) => {
                cc.warn('socket connect failed: ', err);

                setTimeout(() => {
                    cc.log('try next domain');
                    domainService.domainIndex = (domainService.domainIndex + 1) % domainService.getDomains().length;
                    this.connectSocket();
                }, 1000);
            });
    }

    private switchToMiniGame() {
        this.node.active = false;
        this.miniGamePanel.active = true;
    }

    saveAccountToStorage() {
        if (this._account !== null) {
            pf.localStorage.setItem('user_account', this._account);
        }
        pf.localStorage.setItem('user_password', this._psd);
    }

    loadDefault() {
        this._account = pf.localStorage.getItem('user_account');
        this._psd = pf.localStorage.getItem('user_password');
    }

    setExtraOptions(options: IExtraOptions) {
        this._extraOptions = options;
    }
}
