/* eslint-disable camelcase */
/* eslint-disable @typescript-eslint/consistent-type-definitions */
import type { Environment, IAppConfig } from 'pf';

type Platform = 'pkw' | 'wpk';

export class AppConfig implements IAppConfig {
    environment: Environment = 'local';
    platform: Platform = 'pkw';
    version: string = '1.0.0';
    devHostUrl: string = '';
    stageHostUrl: string = '';

    private _debugMode: boolean = false;
    isWebApp: any;

    get isProd(): boolean {
        return this.environment === 'prod';
    }
    get debugMode(): boolean {
        return (this.environment === 'local' || this.environment === 'dev') && this._debugMode;
    }
    set debugMode(value: boolean) {
        this._debugMode = value;
    }

    initConfig(obj: any) {
        for (const key in obj) {
            (this as any)[key] = obj[key];
        }
    }

    // getPlatform(): macros.Platform {
    //     if ((this.environment === 'local' || this.environment === 'dev') && this.isUsernamePasswordLogin)
    //         return macros.Platform.WPT_OLD;
    //     else return macros.Platform.WPT;
    // }

    // getWebUrl() {
    //     const webServer = this.profileMode === 'wc' ? this.wcWebUrl : this.pgWebUrl;
    //     if (webServer.endsWith('/')) return webServer.substring(0, webServer.length - 1);
    //     else return webServer;
    // }

    analytics: Partial<AnalyticsType> = {
        enabled: false,
        segmentTool_KeyDev: '',
        segmentTool_KeyProd: '',
        segmentTool_AppName: ''
    };
}

/** analytics module details */
export type AnalyticsType = {
    /** Whether Analytics module should be enabled and events should be collected or not */
    enabled: boolean;
    /** Segment Tool source Write Key to be used for development build */
    segmentTool_KeyDev: string;
    /** Segment Tool source Write Key to be used for production build */
    segmentTool_KeyProd: string;
    /** Segment Tool app name to be used */
    segmentTool_AppName: string;
};
